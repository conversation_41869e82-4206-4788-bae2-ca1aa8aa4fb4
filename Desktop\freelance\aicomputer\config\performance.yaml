# Performance Optimization Configuration
# Ultimate Voice-Controlled AI Computer System

# Memory Management
memory:
  max_usage_mb: 2048
  garbage_collection_interval: 300  # seconds
  cache_size_mb: 512
  context_history_limit: 1000
  audio_buffer_size: 4096
  
# CPU Optimization
cpu:
  max_usage_percent: 70
  thread_pool_size: 4
  process_priority: "normal"  # low, normal, high
  enable_multiprocessing: true
  worker_processes: 2

# Audio Processing
audio:
  sample_rate: 16000
  chunk_size: 1024
  channels: 1
  buffer_duration_ms: 100
  noise_reduction: true
  auto_gain_control: true
  echo_cancellation: false
  
# Voice Recognition
voice:
  whisper_model_size: "base"  # tiny, base, small, medium, large
  recognition_timeout: 5
  phrase_timeout: 10
  energy_threshold: 300
  dynamic_energy_threshold: true
  pause_threshold: 0.8
  
# AI Processing
ai:
  model_cache_size: 3
  max_tokens: 1000
  temperature: 0.3
  request_timeout: 30
  retry_attempts: 3
  retry_delay: 1
  batch_processing: false
  
# System Monitoring
monitoring:
  stats_update_interval: 30  # seconds
  performance_logging: true
  resource_alerts: true
  memory_alert_threshold: 80  # percent
  cpu_alert_threshold: 85    # percent
  
# Caching
cache:
  enable_command_cache: true
  command_cache_size: 500
  enable_response_cache: true
  response_cache_ttl: 3600  # seconds
  enable_model_cache: true
  
# Network
network:
  connection_timeout: 10
  read_timeout: 30
  max_retries: 3
  backoff_factor: 0.3
  pool_connections: 10
  pool_maxsize: 10
  
# Database
database:
  connection_pool_size: 5
  max_overflow: 10
  pool_timeout: 30
  pool_recycle: 3600
  echo_sql: false
  
# Logging
logging:
  max_file_size_mb: 100
  backup_count: 5
  compression: true
  async_logging: true
  buffer_size: 1000
  
# Background Tasks
background:
  cleanup_interval: 600  # seconds
  maintenance_interval: 3600  # seconds
  backup_interval: 86400  # seconds
  enable_auto_cleanup: true
  
# Power Management
power:
  enable_power_saving: false
  idle_timeout: 300  # seconds
  sleep_mode_enabled: false
  cpu_scaling: "performance"  # powersave, performance, ondemand
  
# Feature Toggles for Performance
features:
  enable_gesture_recognition: false  # Disable for better performance
  enable_advanced_nlp: true
  enable_context_learning: true
  enable_predictive_text: false
  enable_real_time_transcription: true
  enable_background_listening: true
  
# Quality vs Performance Trade-offs
quality:
  audio_quality: "balanced"  # low, balanced, high
  recognition_accuracy: "balanced"  # fast, balanced, accurate
  response_quality: "high"  # fast, balanced, high
  
# Development Settings
development:
  enable_profiling: false
  profile_output_dir: "logs/profiling"
  memory_profiling: false
  cpu_profiling: false
  
# Platform Specific
windows:
  process_priority_class: "NORMAL_PRIORITY_CLASS"
  enable_windows_optimizations: true
  use_wasapi: true
  
# Emergency Settings
emergency:
  max_memory_usage_mb: 4096
  emergency_cleanup_threshold: 90  # percent
  force_garbage_collection: true
  disable_non_essential_features: true
