# Ultimate Voice-Controlled AI Computer System
# Main Configuration File

# Application Settings
app:
  name: "Voice AI Controller"
  version: "1.0.0-alpha"
  debug: false
  safe_mode: true

# Voice Recognition Configuration (moved to bottom with optimizations)

# AI and NLP Configuration
ai:
  primary_model: "gpt-4-turbo-preview"
  fallback_model: "gpt-3.5-turbo"
  max_tokens: 4096
  temperature: 0.3
  
  context:
    max_history: 100
    session_timeout: 3600
    enable_memory: true
    
  safety:
    content_filter: true
    dangerous_commands: false
    require_confirmation: true

# Command Processing
commands:
  timeout: 30
  max_concurrent: 5
  retry_attempts: 3
  
  categories:
    file_management: true
    application_control: true
    system_info: true
    web_browsing: true   # Phase 2 - Now enabled
    email: true          # Phase 2 - Now enabled
    
  confirmation_required:
    - "delete"
    - "remove"
    - "uninstall"
    - "shutdown"
    - "restart"
    - "format"

# File System Configuration
file_system:
  allowed_paths:
    - "C:\\Users"
    - "D:\\"
    - "Desktop"
    - "Documents"
    - "Downloads"
    
  restricted_paths:
    - "C:\\Windows\\System32"
    - "C:\\Program Files"
    - "C:\\Program Files (x86)"
    
  backup:
    enabled: true
    path: "backups"
    max_backups: 10

# Application Control
applications:
  allowed_apps:
    - "notepad.exe"
    - "calculator.exe"
    - "explorer.exe"
    - "chrome.exe"
    - "firefox.exe"
    - "code.exe"
    - "winword.exe"
    - "excel.exe"
    - "powerpnt.exe"
    
  startup_apps:
    - name: "Voice AI Controller"
      path: "main.py"
      auto_start: true

# Security Configuration
security:
  encryption:
    enabled: true
    algorithm: "AES-256"
    
  authentication:
    voice_print: true   # Phase 2 - Now enabled with fallback
    biometric: true     # Phase 4 - Now enabled with fallback
    
  logging:
    audit_trail: true
    sensitive_data: false
    retention_days: 90

# Performance Settings
performance:
  memory_limit_mb: 2048
  cpu_limit_percent: 50
  
  optimization:
    preload_models: true
    cache_responses: true
    background_processing: true

# Logging Configuration
logging:
  level: "INFO"
  file: "logs/voice_ai.log"
  max_size_mb: 100
  backup_count: 5
  
  loggers:
    voice: "DEBUG"
    ai: "INFO"
    commands: "INFO"
    security: "WARNING"

# Database Configuration
database:
  url: "sqlite:///data/voice_ai.db"
  echo: false
  pool_size: 5
  
  tables:
    user_profiles: true
    command_history: true
    context_memory: true
    system_state: true

# UI Configuration
ui:
  enabled: true
  system_tray: true
  notifications: true
  theme: "dark"

  windows:
    main:
      width: 800
      height: 600
      resizable: true

    settings:
      width: 600
      height: 500
      resizable: false

# Phase 2 - Intelligence Integration Configuration
intelligence:
  enabled: true

  # Advanced Context Engine
  context:
    max_history: 1000
    semantic_threshold: 0.7

  # Workflow Engine
  workflow:
    max_concurrent: 5
    step_timeout: 300
    timeout: 3600
    max_retries: 3

  # Learning Engine
  learning:
    enabled: true
    min_pattern_frequency: 3
    confidence_threshold: 0.7
    adaptation_sensitivity: 0.8
    max_history: 10000

  # Error Recovery System
  error_recovery:
    enabled: true
    max_retries: 3
    timeout: 300
    learning: true

# Phase 3 - Advanced Automation Configuration
automation:
  enabled: true

  # Predictive Intelligence Engine
  prediction:
    horizon_minutes: 60
    min_frequency: 5
    confidence_threshold: 0.7
    max_active: 20

  # Proactive Assistant System
  assistant:
    max_active: 10
    timeout_minutes: 30
    auto_execute_threshold: 0.9

  # Behavioral Pattern Analyzer
  pattern_analysis:
    window_days: 30
    min_frequency: 3
    confidence_threshold: 0.6

  # Workflow Optimizer
  optimization:
    analysis_interval: 3600
    min_executions: 5
    threshold: 0.2

# Phase 4 - Enhanced User Experience Configuration
experience:
  enabled: true

  # Multi-Modal Interface
  multimodal:
    fusion_window_ms: 2000

  # Gesture Recognition
  gesture:
    enabled: true
    confidence_threshold: 0.7

  # Eye Tracking
  eye_tracking:
    enabled: false

  # Advanced Security Framework
  security:
    level: "medium"  # low, medium, high, critical
    session_timeout: 3600
    max_failed_attempts: 3
    encryption_enabled: true
    master_password: "secure_password_123"

  # IoT Integration Hub
  iot:
    mqtt_broker: "localhost"
    mqtt_port: 1883
    discovery_interval: 300
    device_timeout: 600

  # Augmented Reality Overlay
  ar:
    enabled: true
    default_opacity: 0.8
    animation_speed: 1.0
    max_elements: 10

# Voice Recognition Optimizations (Added by voice_fix.py)
voice:
  recognition:
    engine: "whisper"  # whisper, azure, google
    language: "en-US"
    timeout: 10
    phrase_time_limit: 8
    energy_threshold: 200
    pause_threshold: 0.5
    dynamic_energy_threshold: true

  wake_word:
    enabled: true
    word: "computer"
    sensitivity: 0.8
    timeout: 5

  audio:
    sample_rate: 16000
    chunk_size: 1024
    channels: 1
    buffer_duration: 100
    format: "int16"
    device_index: null  # null for default
