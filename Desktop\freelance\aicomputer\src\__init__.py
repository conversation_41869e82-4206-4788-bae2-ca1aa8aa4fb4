# Ultimate Voice-Controlled AI Computer System
# Core Package Initialization

__version__ = "1.0.0-alpha"
__author__ = "Voice AI Development Team"
__description__ = "Ultimate Voice-Controlled AI Computer System"

# Core imports for easy access
from .core.voice_engine import VoiceEngine
from .core.ai_processor import AIProcessor
from .core.command_executor import CommandExecutor
from .core.system_controller import System<PERSON>ontroller

__all__ = [
    "VoiceEngine",
    "AIProcessor", 
    "CommandExecutor",
    "SystemController"
]
