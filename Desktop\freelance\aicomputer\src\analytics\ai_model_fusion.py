"""
AI Model Fusion - Phase 8 Component

Advanced AI model fusion and ensemble analytics.
"""

import asyncio
import time
import json
import numpy as np
from typing import Dict, List, Optional, Any, Tuple, Callable
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from datetime import datetime, timedelta
import threading
from concurrent.futures import ThreadPoolExecutor

from loguru import logger

from ..utils.config_manager import ConfigManager
from .real_time_analytics import RealTimeAnalytics


@dataclass
class ModelPrediction:
    """Individual model prediction."""
    model_id: str
    prediction: Any
    confidence: float
    latency: float
    metadata: Dict[str, Any]
    timestamp: float


@dataclass
class FusionResult:
    """Result of model fusion."""
    fusion_id: str
    final_prediction: Any
    final_confidence: float
    contributing_models: List[str]
    fusion_method: str
    model_weights: Dict[str, float]
    total_latency: float
    timestamp: float


@dataclass
class ModelPerformance:
    """Model performance metrics."""
    model_id: str
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    latency_avg: float
    latency_p95: float
    error_rate: float
    last_updated: float


class AIModelFusion:
    """
    Advanced AI model fusion and ensemble system.
    
    Features:
    - Multi-model ensemble predictions
    - Dynamic model weighting
    - Performance-based fusion
    - Real-time model selection
    - Adaptive fusion strategies
    - Model performance analytics
    """
    
    def __init__(self, config_manager: ConfigManager, analytics_engine: RealTimeAnalytics):
        self.config = config_manager
        self.analytics = analytics_engine
        
        # Model fusion data
        self.registered_models: Dict[str, Dict[str, Any]] = {}
        self.model_performances: Dict[str, ModelPerformance] = {}
        self.fusion_history: List[FusionResult] = []
        self.model_predictions: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        
        # Fusion strategies
        self.fusion_strategies: Dict[str, Callable] = {
            "weighted_average": self._weighted_average_fusion,
            "majority_vote": self._majority_vote_fusion,
            "confidence_based": self._confidence_based_fusion,
            "performance_weighted": self._performance_weighted_fusion,
            "dynamic_selection": self._dynamic_selection_fusion
        }
        
        # Configuration
        self.default_fusion_method = self.config.get("ai_fusion.default_method", "weighted_average")
        self.min_models_for_fusion = self.config.get("ai_fusion.min_models", 2)
        self.confidence_threshold = self.config.get("ai_fusion.confidence_threshold", 0.7)
        self.performance_window = self.config.get("ai_fusion.performance_window_hours", 24)
        
        # Threading
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        logger.info("AI Model Fusion initialized")
    
    async def register_model(self, model_id: str, model_info: Dict[str, Any]):
        """Register a model for fusion."""
        try:
            self.registered_models[model_id] = {
                "model_info": model_info,
                "registered_at": time.time(),
                "active": True,
                "prediction_count": 0,
                "last_used": None
            }
            
            # Initialize performance metrics
            self.model_performances[model_id] = ModelPerformance(
                model_id=model_id,
                accuracy=0.5,
                precision=0.5,
                recall=0.5,
                f1_score=0.5,
                latency_avg=1.0,
                latency_p95=2.0,
                error_rate=0.1,
                last_updated=time.time()
            )
            
            logger.info(f"Registered model for fusion: {model_id}")
            
        except Exception as e:
            logger.error(f"Error registering model {model_id}: {e}")
    
    async def fuse_predictions(self, predictions: List[ModelPrediction], 
                             fusion_method: str = None,
                             context: Dict[str, Any] = None) -> FusionResult:
        """Fuse predictions from multiple models."""
        try:
            if context is None:
                context = {}
            
            if len(predictions) < self.min_models_for_fusion:
                logger.warning(f"Insufficient models for fusion: {len(predictions)} < {self.min_models_for_fusion}")
                if predictions:
                    # Return single prediction as fusion result
                    pred = predictions[0]
                    return FusionResult(
                        fusion_id=f"single_{int(time.time() * 1000)}",
                        final_prediction=pred.prediction,
                        final_confidence=pred.confidence,
                        contributing_models=[pred.model_id],
                        fusion_method="single_model",
                        model_weights={pred.model_id: 1.0},
                        total_latency=pred.latency,
                        timestamp=time.time()
                    )
                else:
                    raise ValueError("No predictions provided")
            
            # Select fusion method
            method = fusion_method or self.default_fusion_method
            if method not in self.fusion_strategies:
                method = "weighted_average"
            
            # Apply fusion strategy
            fusion_func = self.fusion_strategies[method]
            result = await fusion_func(predictions, context)
            
            # Store predictions for performance tracking
            for pred in predictions:
                self.model_predictions[pred.model_id].append(pred)
                
                # Update model usage
                if pred.model_id in self.registered_models:
                    self.registered_models[pred.model_id]["prediction_count"] += 1
                    self.registered_models[pred.model_id]["last_used"] = time.time()
            
            # Store fusion result
            self.fusion_history.append(result)
            if len(self.fusion_history) > 10000:
                self.fusion_history = self.fusion_history[-10000:]
            
            # Record analytics
            self.analytics.record_metric("ai_fusion_confidence", result.final_confidence)
            self.analytics.record_metric("ai_fusion_latency", result.total_latency)
            self.analytics.record_metric("ai_fusion_models_used", len(result.contributing_models))
            
            return result
            
        except Exception as e:
            logger.error(f"Error fusing predictions: {e}")
            # Return fallback result
            return FusionResult(
                fusion_id=f"error_{int(time.time() * 1000)}",
                final_prediction=None,
                final_confidence=0.0,
                contributing_models=[],
                fusion_method="error",
                model_weights={},
                total_latency=0.0,
                timestamp=time.time()
            )
    
    async def _weighted_average_fusion(self, predictions: List[ModelPrediction], 
                                     context: Dict[str, Any]) -> FusionResult:
        """Weighted average fusion strategy."""
        try:
            # Calculate weights based on confidence
            total_confidence = sum(pred.confidence for pred in predictions)
            weights = {}
            
            if total_confidence > 0:
                for pred in predictions:
                    weights[pred.model_id] = pred.confidence / total_confidence
            else:
                # Equal weights if no confidence
                weight = 1.0 / len(predictions)
                for pred in predictions:
                    weights[pred.model_id] = weight
            
            # Fuse predictions (assuming numerical predictions)
            final_prediction = 0.0
            final_confidence = 0.0
            
            for pred in predictions:
                weight = weights[pred.model_id]
                if isinstance(pred.prediction, (int, float)):
                    final_prediction += pred.prediction * weight
                final_confidence += pred.confidence * weight
            
            return FusionResult(
                fusion_id=f"weighted_avg_{int(time.time() * 1000)}",
                final_prediction=final_prediction,
                final_confidence=final_confidence,
                contributing_models=[pred.model_id for pred in predictions],
                fusion_method="weighted_average",
                model_weights=weights,
                total_latency=max(pred.latency for pred in predictions),
                timestamp=time.time()
            )
            
        except Exception as e:
            logger.error(f"Error in weighted average fusion: {e}")
            raise
    
    async def _majority_vote_fusion(self, predictions: List[ModelPrediction], 
                                  context: Dict[str, Any]) -> FusionResult:
        """Majority vote fusion strategy."""
        try:
            # Count votes for each prediction
            vote_counts = defaultdict(float)
            confidence_sums = defaultdict(float)
            
            for pred in predictions:
                vote_counts[pred.prediction] += 1
                confidence_sums[pred.prediction] += pred.confidence
            
            # Find majority prediction
            majority_prediction = max(vote_counts.items(), key=lambda x: x[1])[0]
            vote_count = vote_counts[majority_prediction]
            
            # Calculate final confidence
            final_confidence = confidence_sums[majority_prediction] / vote_count
            
            # Calculate weights (equal for contributing models)
            contributing_models = [pred.model_id for pred in predictions 
                                 if pred.prediction == majority_prediction]
            weight = 1.0 / len(contributing_models)
            weights = {model_id: weight for model_id in contributing_models}
            
            return FusionResult(
                fusion_id=f"majority_vote_{int(time.time() * 1000)}",
                final_prediction=majority_prediction,
                final_confidence=final_confidence,
                contributing_models=contributing_models,
                fusion_method="majority_vote",
                model_weights=weights,
                total_latency=max(pred.latency for pred in predictions),
                timestamp=time.time()
            )
            
        except Exception as e:
            logger.error(f"Error in majority vote fusion: {e}")
            raise
    
    async def _confidence_based_fusion(self, predictions: List[ModelPrediction], 
                                     context: Dict[str, Any]) -> FusionResult:
        """Confidence-based fusion strategy."""
        try:
            # Filter predictions by confidence threshold
            high_confidence_preds = [pred for pred in predictions 
                                   if pred.confidence >= self.confidence_threshold]
            
            if not high_confidence_preds:
                # Use all predictions if none meet threshold
                high_confidence_preds = predictions
            
            # Use highest confidence prediction
            best_pred = max(high_confidence_preds, key=lambda x: x.confidence)
            
            return FusionResult(
                fusion_id=f"confidence_based_{int(time.time() * 1000)}",
                final_prediction=best_pred.prediction,
                final_confidence=best_pred.confidence,
                contributing_models=[best_pred.model_id],
                fusion_method="confidence_based",
                model_weights={best_pred.model_id: 1.0},
                total_latency=best_pred.latency,
                timestamp=time.time()
            )
            
        except Exception as e:
            logger.error(f"Error in confidence-based fusion: {e}")
            raise
    
    async def _performance_weighted_fusion(self, predictions: List[ModelPrediction], 
                                         context: Dict[str, Any]) -> FusionResult:
        """Performance-weighted fusion strategy."""
        try:
            # Calculate weights based on model performance
            weights = {}
            total_performance = 0.0
            
            for pred in predictions:
                if pred.model_id in self.model_performances:
                    perf = self.model_performances[pred.model_id]
                    # Use F1 score as performance metric
                    performance_score = perf.f1_score * (1 - perf.error_rate)
                    weights[pred.model_id] = performance_score
                    total_performance += performance_score
                else:
                    weights[pred.model_id] = 0.5  # Default weight
                    total_performance += 0.5
            
            # Normalize weights
            if total_performance > 0:
                for model_id in weights:
                    weights[model_id] /= total_performance
            else:
                # Equal weights if no performance data
                weight = 1.0 / len(predictions)
                for pred in predictions:
                    weights[pred.model_id] = weight
            
            # Weighted fusion
            final_prediction = 0.0
            final_confidence = 0.0
            
            for pred in predictions:
                weight = weights[pred.model_id]
                if isinstance(pred.prediction, (int, float)):
                    final_prediction += pred.prediction * weight
                final_confidence += pred.confidence * weight
            
            return FusionResult(
                fusion_id=f"performance_weighted_{int(time.time() * 1000)}",
                final_prediction=final_prediction,
                final_confidence=final_confidence,
                contributing_models=[pred.model_id for pred in predictions],
                fusion_method="performance_weighted",
                model_weights=weights,
                total_latency=max(pred.latency for pred in predictions),
                timestamp=time.time()
            )
            
        except Exception as e:
            logger.error(f"Error in performance-weighted fusion: {e}")
            raise
    
    async def _dynamic_selection_fusion(self, predictions: List[ModelPrediction], 
                                      context: Dict[str, Any]) -> FusionResult:
        """Dynamic model selection fusion strategy."""
        try:
            # Select best model based on context and performance
            best_model_id = None
            best_score = -1.0
            
            for pred in predictions:
                score = 0.0
                
                # Base score from confidence
                score += pred.confidence * 0.4
                
                # Performance score
                if pred.model_id in self.model_performances:
                    perf = self.model_performances[pred.model_id]
                    score += perf.f1_score * 0.3
                    score += (1 - perf.error_rate) * 0.2
                
                # Latency penalty
                score -= min(pred.latency / 10.0, 0.1)
                
                if score > best_score:
                    best_score = score
                    best_model_id = pred.model_id
            
            # Find the best prediction
            best_pred = next(pred for pred in predictions if pred.model_id == best_model_id)
            
            return FusionResult(
                fusion_id=f"dynamic_selection_{int(time.time() * 1000)}",
                final_prediction=best_pred.prediction,
                final_confidence=best_pred.confidence,
                contributing_models=[best_pred.model_id],
                fusion_method="dynamic_selection",
                model_weights={best_pred.model_id: 1.0},
                total_latency=best_pred.latency,
                timestamp=time.time()
            )
            
        except Exception as e:
            logger.error(f"Error in dynamic selection fusion: {e}")
            raise
    
    async def update_model_performance(self, model_id: str, 
                                     actual_result: Any, 
                                     predicted_result: Any,
                                     latency: float):
        """Update model performance metrics."""
        try:
            if model_id not in self.model_performances:
                return
            
            perf = self.model_performances[model_id]
            
            # Calculate accuracy (simplified)
            is_correct = actual_result == predicted_result
            
            # Update metrics with exponential moving average
            alpha = 0.1  # Learning rate
            
            if is_correct:
                perf.accuracy = perf.accuracy * (1 - alpha) + alpha
            else:
                perf.accuracy = perf.accuracy * (1 - alpha)
            
            # Update latency
            perf.latency_avg = perf.latency_avg * (1 - alpha) + latency * alpha
            
            # Update error rate
            if not is_correct:
                perf.error_rate = perf.error_rate * (1 - alpha) + alpha
            else:
                perf.error_rate = perf.error_rate * (1 - alpha)
            
            # Update F1 score (simplified)
            perf.f1_score = perf.accuracy  # Simplified for this example
            perf.precision = perf.accuracy
            perf.recall = perf.accuracy
            
            perf.last_updated = time.time()
            
            # Record analytics
            self.analytics.record_metric(f"model_accuracy_{model_id}", perf.accuracy)
            self.analytics.record_metric(f"model_latency_{model_id}", latency)
            self.analytics.record_metric(f"model_error_rate_{model_id}", perf.error_rate)
            
        except Exception as e:
            logger.error(f"Error updating model performance for {model_id}: {e}")
    
    async def get_fusion_analytics(self) -> Dict[str, Any]:
        """Get fusion analytics and insights."""
        try:
            analytics = {
                "registered_models": len(self.registered_models),
                "total_fusions": len(self.fusion_history),
                "fusion_methods_used": {},
                "model_performance_summary": {},
                "average_fusion_confidence": 0.0,
                "average_fusion_latency": 0.0,
                "top_performing_models": [],
                "fusion_trends": {}
            }
            
            if self.fusion_history:
                # Fusion method distribution
                method_counts = Counter(result.fusion_method for result in self.fusion_history)
                analytics["fusion_methods_used"] = dict(method_counts)
                
                # Average metrics
                analytics["average_fusion_confidence"] = sum(
                    result.final_confidence for result in self.fusion_history
                ) / len(self.fusion_history)
                
                analytics["average_fusion_latency"] = sum(
                    result.total_latency for result in self.fusion_history
                ) / len(self.fusion_history)
            
            # Model performance summary
            for model_id, perf in self.model_performances.items():
                analytics["model_performance_summary"][model_id] = {
                    "accuracy": perf.accuracy,
                    "f1_score": perf.f1_score,
                    "latency_avg": perf.latency_avg,
                    "error_rate": perf.error_rate
                }
            
            # Top performing models
            if self.model_performances:
                sorted_models = sorted(
                    self.model_performances.items(),
                    key=lambda x: x[1].f1_score * (1 - x[1].error_rate),
                    reverse=True
                )[:5]
                
                analytics["top_performing_models"] = [
                    {"model_id": model_id, "score": perf.f1_score * (1 - perf.error_rate)}
                    for model_id, perf in sorted_models
                ]
            
            return analytics
            
        except Exception as e:
            logger.error(f"Error getting fusion analytics: {e}")
            return {}
    
    async def optimize_fusion_strategy(self, context: Dict[str, Any] = None) -> str:
        """Recommend optimal fusion strategy based on current performance."""
        try:
            if context is None:
                context = {}
            
            # Analyze recent fusion performance
            recent_fusions = self.fusion_history[-100:] if self.fusion_history else []
            
            if not recent_fusions:
                return self.default_fusion_method
            
            # Calculate performance by method
            method_performance = defaultdict(list)
            for result in recent_fusions:
                method_performance[result.fusion_method].append(result.final_confidence)
            
            # Find best performing method
            best_method = self.default_fusion_method
            best_avg_confidence = 0.0
            
            for method, confidences in method_performance.items():
                if confidences:
                    avg_confidence = sum(confidences) / len(confidences)
                    if avg_confidence > best_avg_confidence:
                        best_avg_confidence = avg_confidence
                        best_method = method
            
            return best_method
            
        except Exception as e:
            logger.error(f"Error optimizing fusion strategy: {e}")
            return self.default_fusion_method


# Factory functions
def create_model_prediction(model_id: str, prediction: Any, confidence: float) -> ModelPrediction:
    """Create a new model prediction."""
    return ModelPrediction(
        model_id=model_id,
        prediction=prediction,
        confidence=confidence,
        latency=0.0,
        metadata={},
        timestamp=time.time()
    )

def create_fusion_result(final_prediction: Any, final_confidence: float, 
                        contributing_models: List[str]) -> FusionResult:
    """Create a new fusion result."""
    return FusionResult(
        fusion_id=f"fusion_{int(time.time() * 1000)}",
        final_prediction=final_prediction,
        final_confidence=final_confidence,
        contributing_models=contributing_models,
        fusion_method="manual",
        model_weights={model: 1.0/len(contributing_models) for model in contributing_models},
        total_latency=0.0,
        timestamp=time.time()
    )
