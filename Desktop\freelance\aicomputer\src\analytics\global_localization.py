"""
Global Localization - Phase 8 Component

Advanced global localization and internationalization analytics.
"""

import asyncio
import time
import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
from datetime import datetime, timedelta
import locale
import gettext

from loguru import logger

from ..utils.config_manager import ConfigManager
from .real_time_analytics import RealTimeAnalytics


@dataclass
class LocalizationMetric:
    """Localization metric data structure."""
    metric_id: str
    locale: str
    region: str
    language: str
    metric_type: str
    value: float
    timestamp: float
    metadata: Dict[str, Any]


@dataclass
class CulturalContext:
    """Cultural context information."""
    context_id: str
    locale: str
    cultural_dimensions: Dict[str, float]
    communication_style: str
    time_orientation: str
    social_hierarchy: str
    decision_making_style: str
    preferences: Dict[str, Any]


class GlobalLocalization:
    """
    Advanced global localization and cultural analytics system.
    
    Features:
    - Multi-language support analytics
    - Cultural adaptation metrics
    - Regional usage patterns
    - Localization effectiveness tracking
    - Cultural context analysis
    - Global user behavior insights
    """
    
    def __init__(self, config_manager: ConfigManager, analytics_engine: RealTimeAnalytics):
        self.config = config_manager
        self.analytics = analytics_engine
        
        # Localization data
        self.localization_metrics: List[LocalizationMetric] = []
        self.cultural_contexts: Dict[str, CulturalContext] = {}
        self.supported_locales: List[str] = []
        self.usage_patterns: Dict[str, Dict[str, Any]] = defaultdict(dict)
        
        # Configuration
        self.default_locale = self.config.get("localization.default_locale", "en_US")
        self.auto_detection = self.config.get("localization.auto_detection", True)
        self.cultural_adaptation = self.config.get("localization.cultural_adaptation", True)
        
        # Initialize localization system
        self._initialize_localization()
        
        logger.info("Global Localization initialized")
    
    def _initialize_localization(self):
        """Initialize localization system."""
        try:
            # Initialize supported locales
            self.supported_locales = [
                "en_US", "en_GB", "es_ES", "es_MX", "fr_FR", "fr_CA",
                "de_DE", "it_IT", "pt_BR", "pt_PT", "ru_RU", "zh_CN",
                "zh_TW", "ja_JP", "ko_KR", "ar_SA", "hi_IN", "th_TH"
            ]
            
            # Initialize cultural contexts
            self._initialize_cultural_contexts()
            
            logger.info(f"Initialized {len(self.supported_locales)} supported locales")
            
        except Exception as e:
            logger.error(f"Error initializing localization: {e}")
    
    def _initialize_cultural_contexts(self):
        """Initialize cultural context data."""
        try:
            # Sample cultural contexts (simplified)
            cultural_data = {
                "en_US": {
                    "cultural_dimensions": {
                        "individualism": 0.91,
                        "power_distance": 0.40,
                        "uncertainty_avoidance": 0.46,
                        "masculinity": 0.62,
                        "long_term_orientation": 0.26
                    },
                    "communication_style": "direct",
                    "time_orientation": "monochronic",
                    "social_hierarchy": "low",
                    "decision_making_style": "individual"
                },
                "ja_JP": {
                    "cultural_dimensions": {
                        "individualism": 0.46,
                        "power_distance": 0.54,
                        "uncertainty_avoidance": 0.92,
                        "masculinity": 0.95,
                        "long_term_orientation": 0.88
                    },
                    "communication_style": "indirect",
                    "time_orientation": "monochronic",
                    "social_hierarchy": "high",
                    "decision_making_style": "consensus"
                },
                "de_DE": {
                    "cultural_dimensions": {
                        "individualism": 0.67,
                        "power_distance": 0.35,
                        "uncertainty_avoidance": 0.65,
                        "masculinity": 0.66,
                        "long_term_orientation": 0.83
                    },
                    "communication_style": "direct",
                    "time_orientation": "monochronic",
                    "social_hierarchy": "medium",
                    "decision_making_style": "structured"
                }
            }
            
            for locale, data in cultural_data.items():
                context = CulturalContext(
                    context_id=f"cultural_{locale}",
                    locale=locale,
                    cultural_dimensions=data["cultural_dimensions"],
                    communication_style=data["communication_style"],
                    time_orientation=data["time_orientation"],
                    social_hierarchy=data["social_hierarchy"],
                    decision_making_style=data["decision_making_style"],
                    preferences={}
                )
                self.cultural_contexts[locale] = context
            
        except Exception as e:
            logger.error(f"Error initializing cultural contexts: {e}")
    
    async def analyze_localization_usage(self, user_locale: str, 
                                       interaction_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze localization usage patterns."""
        try:
            # Record usage metric
            await self._record_localization_metric(
                locale=user_locale,
                metric_type="usage",
                value=1.0,
                metadata=interaction_data
            )
            
            # Analyze cultural adaptation
            cultural_analysis = await self._analyze_cultural_adaptation(user_locale, interaction_data)
            
            # Update usage patterns
            await self._update_usage_patterns(user_locale, interaction_data)
            
            # Generate insights
            insights = await self._generate_localization_insights(user_locale)
            
            return {
                "locale": user_locale,
                "cultural_analysis": cultural_analysis,
                "usage_patterns": self.usage_patterns.get(user_locale, {}),
                "insights": insights,
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"Error analyzing localization usage: {e}")
            return {}
    
    async def _record_localization_metric(self, locale: str, metric_type: str, 
                                        value: float, metadata: Dict[str, Any] = None):
        """Record a localization metric."""
        try:
            if metadata is None:
                metadata = {}
            
            # Parse locale
            language, region = self._parse_locale(locale)
            
            metric = LocalizationMetric(
                metric_id=f"loc_metric_{int(time.time() * 1000)}_{hash(str(metadata)) % 10000}",
                locale=locale,
                region=region,
                language=language,
                metric_type=metric_type,
                value=value,
                timestamp=time.time(),
                metadata=metadata
            )
            
            self.localization_metrics.append(metric)
            
            # Keep only recent metrics
            if len(self.localization_metrics) > 10000:
                self.localization_metrics = self.localization_metrics[-10000:]
            
            # Record in real-time analytics
            self.analytics.record_metric(f"localization_{metric_type}_{locale}", value, metadata)
            
        except Exception as e:
            logger.error(f"Error recording localization metric: {e}")
    
    def _parse_locale(self, locale: str) -> Tuple[str, str]:
        """Parse locale into language and region."""
        try:
            if "_" in locale:
                language, region = locale.split("_", 1)
            else:
                language = locale
                region = "unknown"
            
            return language, region
            
        except Exception as e:
            logger.error(f"Error parsing locale {locale}: {e}")
            return "unknown", "unknown"
    
    async def _analyze_cultural_adaptation(self, locale: str, 
                                         interaction_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze cultural adaptation effectiveness."""
        try:
            cultural_context = self.cultural_contexts.get(locale)
            if not cultural_context:
                return {"adaptation_score": 0.5, "recommendations": []}
            
            adaptation_score = 0.0
            recommendations = []
            
            # Analyze communication style adaptation
            user_communication = interaction_data.get("communication_style", "unknown")
            if user_communication == cultural_context.communication_style:
                adaptation_score += 0.3
            else:
                recommendations.append(f"Adapt to {cultural_context.communication_style} communication style")
            
            # Analyze time orientation
            time_sensitivity = interaction_data.get("time_sensitivity", 0.5)
            if cultural_context.time_orientation == "monochronic" and time_sensitivity > 0.7:
                adaptation_score += 0.2
            elif cultural_context.time_orientation == "polychronic" and time_sensitivity < 0.5:
                adaptation_score += 0.2
            else:
                recommendations.append(f"Adapt to {cultural_context.time_orientation} time orientation")
            
            # Analyze decision making style
            decision_complexity = interaction_data.get("decision_complexity", 0.5)
            if cultural_context.decision_making_style == "consensus" and decision_complexity > 0.6:
                adaptation_score += 0.2
            elif cultural_context.decision_making_style == "individual" and decision_complexity < 0.4:
                adaptation_score += 0.2
            
            # Analyze social hierarchy awareness
            formality_level = interaction_data.get("formality_level", 0.5)
            if cultural_context.social_hierarchy == "high" and formality_level > 0.7:
                adaptation_score += 0.3
            elif cultural_context.social_hierarchy == "low" and formality_level < 0.4:
                adaptation_score += 0.3
            
            return {
                "adaptation_score": min(adaptation_score, 1.0),
                "cultural_dimensions": cultural_context.cultural_dimensions,
                "recommendations": recommendations,
                "context_used": cultural_context.context_id
            }
            
        except Exception as e:
            logger.error(f"Error analyzing cultural adaptation: {e}")
            return {"adaptation_score": 0.5, "recommendations": []}
    
    async def _update_usage_patterns(self, locale: str, interaction_data: Dict[str, Any]):
        """Update usage patterns for locale."""
        try:
            if locale not in self.usage_patterns:
                self.usage_patterns[locale] = {
                    "total_interactions": 0,
                    "feature_usage": defaultdict(int),
                    "time_patterns": defaultdict(int),
                    "success_rate": 0.0,
                    "satisfaction_score": 0.0
                }
            
            patterns = self.usage_patterns[locale]
            patterns["total_interactions"] += 1
            
            # Update feature usage
            features_used = interaction_data.get("features_used", [])
            for feature in features_used:
                patterns["feature_usage"][feature] += 1
            
            # Update time patterns
            current_hour = datetime.now().hour
            patterns["time_patterns"][current_hour] += 1
            
            # Update success rate
            success = interaction_data.get("success", True)
            current_success_rate = patterns["success_rate"]
            total_interactions = patterns["total_interactions"]
            
            patterns["success_rate"] = (
                (current_success_rate * (total_interactions - 1) + (1.0 if success else 0.0)) 
                / total_interactions
            )
            
            # Update satisfaction score
            satisfaction = interaction_data.get("satisfaction", 0.7)
            current_satisfaction = patterns["satisfaction_score"]
            patterns["satisfaction_score"] = (
                (current_satisfaction * (total_interactions - 1) + satisfaction) 
                / total_interactions
            )
            
        except Exception as e:
            logger.error(f"Error updating usage patterns: {e}")
    
    async def _generate_localization_insights(self, locale: str) -> List[str]:
        """Generate localization insights for locale."""
        try:
            insights = []
            
            # Get usage patterns
            patterns = self.usage_patterns.get(locale, {})
            if not patterns:
                return insights
            
            # Success rate insights
            success_rate = patterns.get("success_rate", 0.0)
            if success_rate < 0.7:
                insights.append(f"Low success rate ({success_rate:.1%}) for {locale} - consider improving localization")
            elif success_rate > 0.9:
                insights.append(f"Excellent success rate ({success_rate:.1%}) for {locale}")
            
            # Satisfaction insights
            satisfaction = patterns.get("satisfaction_score", 0.0)
            if satisfaction < 0.6:
                insights.append(f"Low satisfaction ({satisfaction:.1f}/1.0) for {locale} - review cultural adaptation")
            elif satisfaction > 0.8:
                insights.append(f"High satisfaction ({satisfaction:.1f}/1.0) for {locale}")
            
            # Usage volume insights
            total_interactions = patterns.get("total_interactions", 0)
            if total_interactions > 1000:
                insights.append(f"High usage volume ({total_interactions} interactions) for {locale}")
            elif total_interactions < 50:
                insights.append(f"Low usage volume ({total_interactions} interactions) for {locale}")
            
            # Feature usage insights
            feature_usage = patterns.get("feature_usage", {})
            if feature_usage:
                most_used_feature = max(feature_usage.items(), key=lambda x: x[1])
                insights.append(f"Most used feature in {locale}: {most_used_feature[0]} ({most_used_feature[1]} uses)")
            
            return insights
            
        except Exception as e:
            logger.error(f"Error generating localization insights: {e}")
            return []
    
    async def get_global_analytics(self) -> Dict[str, Any]:
        """Get global localization analytics."""
        try:
            analytics = {
                "supported_locales": len(self.supported_locales),
                "active_locales": len(self.usage_patterns),
                "total_interactions": sum(p.get("total_interactions", 0) for p in self.usage_patterns.values()),
                "locale_distribution": {},
                "cultural_adaptation_scores": {},
                "regional_insights": {},
                "top_performing_locales": [],
                "improvement_opportunities": []
            }
            
            # Calculate locale distribution
            total_interactions = analytics["total_interactions"]
            if total_interactions > 0:
                for locale, patterns in self.usage_patterns.items():
                    locale_interactions = patterns.get("total_interactions", 0)
                    analytics["locale_distribution"][locale] = locale_interactions / total_interactions
            
            # Calculate cultural adaptation scores
            for locale in self.usage_patterns.keys():
                if locale in self.cultural_contexts:
                    # Simplified adaptation score based on success rate and satisfaction
                    patterns = self.usage_patterns[locale]
                    success_rate = patterns.get("success_rate", 0.0)
                    satisfaction = patterns.get("satisfaction_score", 0.0)
                    adaptation_score = (success_rate + satisfaction) / 2
                    analytics["cultural_adaptation_scores"][locale] = adaptation_score
            
            # Identify top performing locales
            if analytics["cultural_adaptation_scores"]:
                top_locales = sorted(
                    analytics["cultural_adaptation_scores"].items(),
                    key=lambda x: x[1],
                    reverse=True
                )[:5]
                analytics["top_performing_locales"] = [
                    {"locale": locale, "score": score} for locale, score in top_locales
                ]
            
            # Identify improvement opportunities
            for locale, score in analytics["cultural_adaptation_scores"].items():
                if score < 0.6:
                    analytics["improvement_opportunities"].append({
                        "locale": locale,
                        "score": score,
                        "recommendation": "Review cultural adaptation and localization quality"
                    })
            
            return analytics
            
        except Exception as e:
            logger.error(f"Error getting global analytics: {e}")
            return {}
    
    async def optimize_for_locale(self, locale: str) -> Dict[str, Any]:
        """Generate optimization recommendations for a specific locale."""
        try:
            patterns = self.usage_patterns.get(locale, {})
            cultural_context = self.cultural_contexts.get(locale)
            
            if not patterns or not cultural_context:
                return {"recommendations": [], "priority": "low"}
            
            recommendations = []
            priority = "low"
            
            # Analyze success rate
            success_rate = patterns.get("success_rate", 0.0)
            if success_rate < 0.7:
                recommendations.append({
                    "type": "success_improvement",
                    "description": f"Improve success rate from {success_rate:.1%}",
                    "actions": ["Review command recognition", "Improve error handling", "Enhance feedback"]
                })
                priority = "high"
            
            # Analyze cultural adaptation
            if cultural_context.communication_style == "indirect":
                recommendations.append({
                    "type": "communication_adaptation",
                    "description": "Adapt to indirect communication style",
                    "actions": ["Use softer language", "Provide context", "Avoid direct commands"]
                })
            
            # Analyze time orientation
            if cultural_context.time_orientation == "polychronic":
                recommendations.append({
                    "type": "time_adaptation",
                    "description": "Adapt to polychronic time orientation",
                    "actions": ["Allow flexible scheduling", "Support multi-tasking", "Reduce time pressure"]
                })
            
            # Analyze social hierarchy
            if cultural_context.social_hierarchy == "high":
                recommendations.append({
                    "type": "hierarchy_adaptation",
                    "description": "Adapt to high social hierarchy culture",
                    "actions": ["Increase formality", "Show respect", "Use appropriate titles"]
                })
            
            return {
                "locale": locale,
                "recommendations": recommendations,
                "priority": priority,
                "cultural_context": asdict(cultural_context),
                "current_performance": patterns
            }
            
        except Exception as e:
            logger.error(f"Error optimizing for locale {locale}: {e}")
            return {"recommendations": [], "priority": "low"}


# Factory functions
def create_localization_metric(locale: str, metric_type: str, value: float) -> LocalizationMetric:
    """Create a new localization metric."""
    language, region = locale.split("_") if "_" in locale else (locale, "unknown")
    
    return LocalizationMetric(
        metric_id=f"loc_metric_{int(time.time() * 1000)}",
        locale=locale,
        region=region,
        language=language,
        metric_type=metric_type,
        value=value,
        timestamp=time.time(),
        metadata={}
    )
