"""
Performance Analytics - Phase 8 Component

Advanced performance analytics and optimization insights.
"""

import asyncio
import time
import json
import statistics
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from datetime import datetime, timedelta
import threading
import psutil
import gc

from loguru import logger

from ..utils.config_manager import ConfigManager
from .real_time_analytics import RealTimeAnalytics


@dataclass
class PerformanceMetric:
    """Performance metric data point."""
    metric_id: str
    metric_name: str
    value: float
    unit: str
    component: str
    timestamp: float
    metadata: Dict[str, Any]


@dataclass
class PerformanceBenchmark:
    """Performance benchmark result."""
    benchmark_id: str
    benchmark_name: str
    component: str
    duration: float
    operations_count: int
    throughput: float
    latency_avg: float
    latency_p95: float
    latency_p99: float
    memory_usage: float
    cpu_usage: float
    timestamp: float


@dataclass
class PerformanceAlert:
    """Performance alert."""
    alert_id: str
    alert_type: str
    component: str
    metric_name: str
    current_value: float
    threshold_value: float
    severity: str
    description: str
    timestamp: float
    resolved: bool


class PerformanceAnalytics:
    """
    Advanced performance analytics and optimization system.
    
    Features:
    - Real-time performance monitoring
    - Performance benchmarking
    - Bottleneck identification
    - Resource utilization analysis
    - Performance trend analysis
    - Optimization recommendations
    """
    
    def __init__(self, config_manager: ConfigManager, analytics_engine: RealTimeAnalytics):
        self.config = config_manager
        self.analytics = analytics_engine
        
        # Performance data
        self.performance_metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10000))
        self.benchmarks: List[PerformanceBenchmark] = []
        self.performance_alerts: List[PerformanceAlert] = []
        self.component_baselines: Dict[str, Dict[str, float]] = {}
        
        # Performance thresholds
        self.cpu_threshold = self.config.get("performance.cpu_threshold", 80.0)
        self.memory_threshold = self.config.get("performance.memory_threshold", 85.0)
        self.latency_threshold = self.config.get("performance.latency_threshold_ms", 1000.0)
        self.throughput_threshold = self.config.get("performance.throughput_threshold", 100.0)
        
        # Monitoring configuration
        self.monitoring_interval = self.config.get("performance.monitoring_interval", 5.0)
        self.benchmark_enabled = self.config.get("performance.benchmark_enabled", True)
        self.auto_optimization = self.config.get("performance.auto_optimization", False)
        
        # System monitoring
        self.system_monitor_active = False
        self.monitor_thread: Optional[threading.Thread] = None
        
        logger.info("Performance Analytics initialized")
    
    async def start_monitoring(self):
        """Start performance monitoring."""
        try:
            self.system_monitor_active = True
            
            # Start system monitoring thread
            self.monitor_thread = threading.Thread(target=self._system_monitor_loop, daemon=True)
            self.monitor_thread.start()
            
            # Initialize component baselines
            await self._initialize_baselines()
            
            logger.info("Performance monitoring started")
            
        except Exception as e:
            logger.error(f"Error starting performance monitoring: {e}")
    
    async def stop_monitoring(self):
        """Stop performance monitoring."""
        self.system_monitor_active = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5.0)
        
        logger.info("Performance monitoring stopped")
    
    def _system_monitor_loop(self):
        """System monitoring loop."""
        while self.system_monitor_active:
            try:
                # Collect system metrics
                self._collect_system_metrics()
                
                # Check performance thresholds
                self._check_performance_thresholds()
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"Error in system monitor loop: {e}")
                time.sleep(self.monitoring_interval)
    
    def _collect_system_metrics(self):
        """Collect system performance metrics."""
        try:
            current_time = time.time()
            
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=None)
            self._record_metric("cpu_usage", cpu_percent, "percent", "system")
            
            # Memory metrics
            memory = psutil.virtual_memory()
            self._record_metric("memory_usage", memory.percent, "percent", "system")
            self._record_metric("memory_available", memory.available / (1024**3), "GB", "system")
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            self._record_metric("disk_usage", (disk.used / disk.total) * 100, "percent", "system")
            
            # Network metrics
            network = psutil.net_io_counters()
            self._record_metric("network_bytes_sent", network.bytes_sent, "bytes", "system")
            self._record_metric("network_bytes_recv", network.bytes_recv, "bytes", "system")
            
            # Process metrics
            process = psutil.Process()
            self._record_metric("process_cpu", process.cpu_percent(), "percent", "process")
            self._record_metric("process_memory", process.memory_info().rss / (1024**2), "MB", "process")
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
    
    def _record_metric(self, metric_name: str, value: float, unit: str, component: str):
        """Record a performance metric."""
        try:
            metric = PerformanceMetric(
                metric_id=f"perf_{int(time.time() * 1000)}_{hash(metric_name) % 10000}",
                metric_name=metric_name,
                value=value,
                unit=unit,
                component=component,
                timestamp=time.time(),
                metadata={}
            )
            
            self.performance_metrics[metric_name].append(metric)
            
            # Record in real-time analytics
            self.analytics.record_metric(f"performance_{metric_name}", value, {
                "component": component,
                "unit": unit
            })
            
        except Exception as e:
            logger.error(f"Error recording metric {metric_name}: {e}")
    
    def _check_performance_thresholds(self):
        """Check performance thresholds and generate alerts."""
        try:
            current_time = time.time()
            
            # Check CPU threshold
            if "cpu_usage" in self.performance_metrics:
                recent_cpu = list(self.performance_metrics["cpu_usage"])[-5:]
                if recent_cpu:
                    avg_cpu = sum(m.value for m in recent_cpu) / len(recent_cpu)
                    if avg_cpu > self.cpu_threshold:
                        self._create_performance_alert(
                            "high_cpu_usage",
                            "system",
                            "cpu_usage",
                            avg_cpu,
                            self.cpu_threshold,
                            "warning",
                            f"CPU usage ({avg_cpu:.1f}%) exceeds threshold ({self.cpu_threshold}%)"
                        )
            
            # Check memory threshold
            if "memory_usage" in self.performance_metrics:
                recent_memory = list(self.performance_metrics["memory_usage"])[-5:]
                if recent_memory:
                    avg_memory = sum(m.value for m in recent_memory) / len(recent_memory)
                    if avg_memory > self.memory_threshold:
                        self._create_performance_alert(
                            "high_memory_usage",
                            "system",
                            "memory_usage",
                            avg_memory,
                            self.memory_threshold,
                            "warning",
                            f"Memory usage ({avg_memory:.1f}%) exceeds threshold ({self.memory_threshold}%)"
                        )
            
        except Exception as e:
            logger.error(f"Error checking performance thresholds: {e}")
    
    def _create_performance_alert(self, alert_type: str, component: str, metric_name: str,
                                current_value: float, threshold_value: float,
                                severity: str, description: str):
        """Create a performance alert."""
        try:
            alert = PerformanceAlert(
                alert_id=f"alert_{int(time.time() * 1000)}_{hash(description) % 10000}",
                alert_type=alert_type,
                component=component,
                metric_name=metric_name,
                current_value=current_value,
                threshold_value=threshold_value,
                severity=severity,
                description=description,
                timestamp=time.time(),
                resolved=False
            )
            
            self.performance_alerts.append(alert)
            
            # Record in analytics
            self.analytics.record_event(
                event_type="performance_alert",
                data={
                    "alert_type": alert_type,
                    "component": component,
                    "metric_name": metric_name,
                    "current_value": current_value,
                    "threshold_value": threshold_value
                },
                source="performance_analytics",
                severity=severity
            )
            
            logger.warning(f"Performance alert: {description}")
            
        except Exception as e:
            logger.error(f"Error creating performance alert: {e}")
    
    async def run_benchmark(self, benchmark_name: str, component: str,
                          benchmark_func: callable, *args, **kwargs) -> PerformanceBenchmark:
        """Run a performance benchmark."""
        try:
            if not self.benchmark_enabled:
                logger.info("Benchmarking is disabled")
                return None
            
            # Record initial state
            initial_memory = psutil.Process().memory_info().rss / (1024**2)
            initial_cpu = psutil.cpu_percent()
            
            # Run benchmark
            start_time = time.time()
            result = await benchmark_func(*args, **kwargs) if asyncio.iscoroutinefunction(benchmark_func) else benchmark_func(*args, **kwargs)
            end_time = time.time()
            
            # Calculate metrics
            duration = end_time - start_time
            operations_count = kwargs.get('operations_count', 1)
            throughput = operations_count / duration if duration > 0 else 0
            
            # Record final state
            final_memory = psutil.Process().memory_info().rss / (1024**2)
            final_cpu = psutil.cpu_percent()
            
            # Create benchmark result
            benchmark = PerformanceBenchmark(
                benchmark_id=f"bench_{int(time.time() * 1000)}_{hash(benchmark_name) % 10000}",
                benchmark_name=benchmark_name,
                component=component,
                duration=duration,
                operations_count=operations_count,
                throughput=throughput,
                latency_avg=duration * 1000,  # Convert to ms
                latency_p95=duration * 1000 * 1.2,  # Estimated
                latency_p99=duration * 1000 * 1.5,  # Estimated
                memory_usage=final_memory - initial_memory,
                cpu_usage=final_cpu - initial_cpu,
                timestamp=time.time()
            )
            
            self.benchmarks.append(benchmark)
            
            # Record analytics
            self.analytics.record_metric("performance_benchmark_duration", duration)
            self.analytics.record_metric("performance_benchmark_throughput", throughput)
            
            logger.info(f"Benchmark '{benchmark_name}' completed: {duration:.3f}s, {throughput:.1f} ops/s")
            
            return benchmark
            
        except Exception as e:
            logger.error(f"Error running benchmark '{benchmark_name}': {e}")
            return None
    
    async def _initialize_baselines(self):
        """Initialize performance baselines for components."""
        try:
            # Default baselines (these would be learned over time)
            self.component_baselines = {
                "system": {
                    "cpu_usage": 20.0,
                    "memory_usage": 30.0,
                    "disk_usage": 50.0
                },
                "voice_engine": {
                    "processing_latency": 200.0,
                    "recognition_accuracy": 95.0,
                    "throughput": 50.0
                },
                "ai_processor": {
                    "inference_latency": 500.0,
                    "model_accuracy": 90.0,
                    "throughput": 20.0
                }
            }
            
            logger.info("Performance baselines initialized")
            
        except Exception as e:
            logger.error(f"Error initializing baselines: {e}")
    
    async def analyze_performance_trends(self, component: str = None,
                                       time_window_hours: int = 24) -> Dict[str, Any]:
        """Analyze performance trends."""
        try:
            current_time = time.time()
            start_time = current_time - (time_window_hours * 3600)
            
            trends = {
                "time_window_hours": time_window_hours,
                "component": component,
                "metric_trends": {},
                "performance_summary": {},
                "anomalies": [],
                "recommendations": []
            }
            
            # Analyze each metric
            for metric_name, metric_data in self.performance_metrics.items():
                if component and not any(m.component == component for m in metric_data):
                    continue
                
                # Filter by time window
                recent_metrics = [m for m in metric_data if m.timestamp >= start_time]
                
                if len(recent_metrics) < 2:
                    continue
                
                # Calculate trend
                values = [m.value for m in recent_metrics]
                trend_analysis = self._calculate_trend(values)
                
                trends["metric_trends"][metric_name] = {
                    "trend_direction": trend_analysis["direction"],
                    "trend_strength": trend_analysis["strength"],
                    "current_value": values[-1] if values else 0,
                    "average_value": statistics.mean(values) if values else 0,
                    "min_value": min(values) if values else 0,
                    "max_value": max(values) if values else 0,
                    "std_deviation": statistics.stdev(values) if len(values) > 1 else 0
                }
                
                # Detect anomalies
                anomalies = self._detect_anomalies(values)
                if anomalies:
                    trends["anomalies"].extend([
                        {
                            "metric_name": metric_name,
                            "anomaly_type": "outlier",
                            "value": anomaly,
                            "severity": "medium"
                        } for anomaly in anomalies
                    ])
            
            # Generate performance summary
            trends["performance_summary"] = await self._generate_performance_summary(trends["metric_trends"])
            
            # Generate recommendations
            trends["recommendations"] = await self._generate_performance_recommendations(trends)
            
            return trends
            
        except Exception as e:
            logger.error(f"Error analyzing performance trends: {e}")
            return {}
    
    def _calculate_trend(self, values: List[float]) -> Dict[str, Any]:
        """Calculate trend direction and strength."""
        try:
            if len(values) < 2:
                return {"direction": "stable", "strength": 0.0}
            
            # Simple linear regression slope
            n = len(values)
            x = list(range(n))
            
            sum_x = sum(x)
            sum_y = sum(values)
            sum_xy = sum(x[i] * values[i] for i in range(n))
            sum_x2 = sum(xi * xi for xi in x)
            
            # Calculate slope
            slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
            
            # Determine direction and strength
            if abs(slope) < 0.01:
                direction = "stable"
            elif slope > 0:
                direction = "increasing"
            else:
                direction = "decreasing"
            
            strength = min(abs(slope) * 100, 1.0)  # Normalize to 0-1
            
            return {"direction": direction, "strength": strength}
            
        except Exception as e:
            logger.error(f"Error calculating trend: {e}")
            return {"direction": "stable", "strength": 0.0}
    
    def _detect_anomalies(self, values: List[float]) -> List[float]:
        """Detect anomalies in performance data."""
        try:
            if len(values) < 10:
                return []
            
            # Use IQR method for anomaly detection
            q1 = statistics.quantiles(values, n=4)[0]
            q3 = statistics.quantiles(values, n=4)[2]
            iqr = q3 - q1
            
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            
            anomalies = [v for v in values if v < lower_bound or v > upper_bound]
            
            return anomalies[:10]  # Return up to 10 anomalies
            
        except Exception as e:
            logger.error(f"Error detecting anomalies: {e}")
            return []
    
    async def _generate_performance_summary(self, metric_trends: Dict[str, Any]) -> Dict[str, Any]:
        """Generate performance summary."""
        try:
            summary = {
                "overall_health": "good",
                "critical_metrics": [],
                "improving_metrics": [],
                "degrading_metrics": [],
                "stable_metrics": []
            }
            
            critical_count = 0
            
            for metric_name, trend in metric_trends.items():
                direction = trend["direction"]
                current_value = trend["current_value"]
                
                # Categorize metrics
                if direction == "increasing":
                    if metric_name in ["cpu_usage", "memory_usage", "latency"]:
                        summary["degrading_metrics"].append(metric_name)
                        if current_value > 80:  # High threshold
                            summary["critical_metrics"].append(metric_name)
                            critical_count += 1
                    else:
                        summary["improving_metrics"].append(metric_name)
                elif direction == "decreasing":
                    if metric_name in ["cpu_usage", "memory_usage", "latency"]:
                        summary["improving_metrics"].append(metric_name)
                    else:
                        summary["degrading_metrics"].append(metric_name)
                else:
                    summary["stable_metrics"].append(metric_name)
            
            # Determine overall health
            if critical_count > 2:
                summary["overall_health"] = "critical"
            elif critical_count > 0 or len(summary["degrading_metrics"]) > 3:
                summary["overall_health"] = "warning"
            elif len(summary["improving_metrics"]) > len(summary["degrading_metrics"]):
                summary["overall_health"] = "excellent"
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating performance summary: {e}")
            return {}
    
    async def _generate_performance_recommendations(self, trends: Dict[str, Any]) -> List[str]:
        """Generate performance optimization recommendations."""
        try:
            recommendations = []
            
            metric_trends = trends.get("metric_trends", {})
            performance_summary = trends.get("performance_summary", {})
            
            # CPU recommendations
            if "cpu_usage" in performance_summary.get("critical_metrics", []):
                recommendations.append("High CPU usage detected - consider optimizing algorithms or scaling resources")
            
            # Memory recommendations
            if "memory_usage" in performance_summary.get("critical_metrics", []):
                recommendations.append("High memory usage detected - implement memory optimization or increase available memory")
            
            # Latency recommendations
            if any("latency" in metric for metric in performance_summary.get("degrading_metrics", [])):
                recommendations.append("Increasing latency detected - optimize processing pipelines and reduce bottlenecks")
            
            # General recommendations based on trends
            degrading_count = len(performance_summary.get("degrading_metrics", []))
            if degrading_count > 3:
                recommendations.append("Multiple performance metrics degrading - conduct comprehensive performance review")
            
            # Anomaly recommendations
            anomalies = trends.get("anomalies", [])
            if len(anomalies) > 5:
                recommendations.append("Multiple performance anomalies detected - investigate system stability")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating performance recommendations: {e}")
            return []
    
    async def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report."""
        try:
            current_time = time.time()
            
            report = {
                "report_timestamp": current_time,
                "monitoring_status": "active" if self.system_monitor_active else "inactive",
                "total_metrics": sum(len(metrics) for metrics in self.performance_metrics.values()),
                "total_benchmarks": len(self.benchmarks),
                "active_alerts": len([a for a in self.performance_alerts if not a.resolved]),
                "performance_trends": await self.analyze_performance_trends(),
                "recent_benchmarks": [asdict(b) for b in self.benchmarks[-10:]],
                "active_alerts_details": [asdict(a) for a in self.performance_alerts if not a.resolved],
                "system_health": await self._assess_system_health()
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return {}
    
    async def _assess_system_health(self) -> Dict[str, Any]:
        """Assess overall system health."""
        try:
            health = {
                "status": "healthy",
                "score": 100,
                "issues": [],
                "recommendations": []
            }
            
            # Check recent alerts
            recent_alerts = [a for a in self.performance_alerts 
                           if not a.resolved and (time.time() - a.timestamp) < 3600]
            
            if len(recent_alerts) > 5:
                health["status"] = "critical"
                health["score"] -= 50
                health["issues"].append("Multiple active performance alerts")
            elif len(recent_alerts) > 2:
                health["status"] = "warning"
                health["score"] -= 25
                health["issues"].append("Several performance alerts active")
            
            # Check current system metrics
            if "cpu_usage" in self.performance_metrics:
                recent_cpu = list(self.performance_metrics["cpu_usage"])[-5:]
                if recent_cpu:
                    avg_cpu = sum(m.value for m in recent_cpu) / len(recent_cpu)
                    if avg_cpu > 90:
                        health["status"] = "critical"
                        health["score"] -= 30
                        health["issues"].append(f"Very high CPU usage: {avg_cpu:.1f}%")
                    elif avg_cpu > 80:
                        health["status"] = "warning"
                        health["score"] -= 15
                        health["issues"].append(f"High CPU usage: {avg_cpu:.1f}%")
            
            return health
            
        except Exception as e:
            logger.error(f"Error assessing system health: {e}")
            return {"status": "unknown", "score": 0, "issues": [], "recommendations": []}


# Factory functions
def create_performance_metric(metric_name: str, value: float, component: str) -> PerformanceMetric:
    """Create a new performance metric."""
    return PerformanceMetric(
        metric_id=f"perf_{int(time.time() * 1000)}",
        metric_name=metric_name,
        value=value,
        unit="unknown",
        component=component,
        timestamp=time.time(),
        metadata={}
    )
