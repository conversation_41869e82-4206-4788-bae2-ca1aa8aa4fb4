"""
Privacy Analytics - Phase 8 Component

Advanced privacy analytics and data protection monitoring.
"""

import asyncio
import time
import json
import hashlib
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
from datetime import datetime, <PERSON><PERSON><PERSON>
from enum import Enum
import uuid

from loguru import logger

from ..utils.config_manager import ConfigManager
from .real_time_analytics import RealTimeAnalytics


class PrivacyLevel(Enum):
    """Privacy protection levels."""
    PUBLIC = "public"
    INTERNAL = "internal"
    CONFIDENTIAL = "confidential"
    RESTRICTED = "restricted"
    TOP_SECRET = "top_secret"


class DataCategory(Enum):
    """Data categories for privacy classification."""
    PERSONAL_IDENTIFIABLE = "pii"
    SENSITIVE_PERSONAL = "spi"
    BIOMETRIC = "biometric"
    FINANCIAL = "financial"
    HEALTH = "health"
    BEHAVIORAL = "behavioral"
    TECHNICAL = "technical"
    METADATA = "metadata"


@dataclass
class PrivacyEvent:
    """Privacy-related event."""
    event_id: str
    event_type: str
    data_category: DataCategory
    privacy_level: PrivacyLevel
    user_id: str
    data_size: int
    anonymized: bool
    encrypted: bool
    consent_given: bool
    purpose: str
    timestamp: float
    metadata: Dict[str, Any]


@dataclass
class DataFlowRecord:
    """Data flow tracking record."""
    flow_id: str
    source: str
    destination: str
    data_category: DataCategory
    privacy_level: PrivacyLevel
    data_size: int
    processing_purpose: str
    retention_period: int
    anonymization_applied: bool
    encryption_applied: bool
    timestamp: float


@dataclass
class ConsentRecord:
    """User consent record."""
    consent_id: str
    user_id: str
    data_categories: List[DataCategory]
    purposes: List[str]
    granted: bool
    timestamp: float
    expiry_date: Optional[float]
    withdrawal_date: Optional[float]
    metadata: Dict[str, Any]


class PrivacyAnalytics:
    """
    Advanced privacy analytics and data protection monitoring system.
    
    Features:
    - Privacy compliance monitoring
    - Data flow tracking and analysis
    - Consent management analytics
    - Anonymization effectiveness measurement
    - Privacy risk assessment
    - GDPR/CCPA compliance reporting
    """
    
    def __init__(self, config_manager: ConfigManager, analytics_engine: RealTimeAnalytics):
        self.config = config_manager
        self.analytics = analytics_engine
        
        # Privacy data
        self.privacy_events: List[PrivacyEvent] = []
        self.data_flows: List[DataFlowRecord] = []
        self.consent_records: Dict[str, ConsentRecord] = {}
        self.anonymization_metrics: Dict[str, Dict[str, Any]] = {}
        
        # Privacy configuration
        self.default_retention_days = self.config.get("privacy.default_retention_days", 365)
        self.anonymization_threshold = self.config.get("privacy.anonymization_threshold", 0.95)
        self.consent_expiry_days = self.config.get("privacy.consent_expiry_days", 730)  # 2 years
        self.privacy_audit_enabled = self.config.get("privacy.audit_enabled", True)
        
        # Privacy policies
        self.data_classification_rules = self._initialize_classification_rules()
        self.retention_policies = self._initialize_retention_policies()
        
        logger.info("Privacy Analytics initialized")
    
    def _initialize_classification_rules(self) -> Dict[str, Dict[str, Any]]:
        """Initialize data classification rules."""
        return {
            "email": {
                "category": DataCategory.PERSONAL_IDENTIFIABLE,
                "privacy_level": PrivacyLevel.CONFIDENTIAL,
                "requires_consent": True
            },
            "phone": {
                "category": DataCategory.PERSONAL_IDENTIFIABLE,
                "privacy_level": PrivacyLevel.CONFIDENTIAL,
                "requires_consent": True
            },
            "voice_data": {
                "category": DataCategory.BIOMETRIC,
                "privacy_level": PrivacyLevel.RESTRICTED,
                "requires_consent": True
            },
            "behavioral_patterns": {
                "category": DataCategory.BEHAVIORAL,
                "privacy_level": PrivacyLevel.INTERNAL,
                "requires_consent": False
            },
            "system_logs": {
                "category": DataCategory.TECHNICAL,
                "privacy_level": PrivacyLevel.INTERNAL,
                "requires_consent": False
            }
        }
    
    def _initialize_retention_policies(self) -> Dict[DataCategory, int]:
        """Initialize data retention policies (in days)."""
        return {
            DataCategory.PERSONAL_IDENTIFIABLE: 365,  # 1 year
            DataCategory.SENSITIVE_PERSONAL: 180,     # 6 months
            DataCategory.BIOMETRIC: 90,               # 3 months
            DataCategory.FINANCIAL: 2555,             # 7 years
            DataCategory.HEALTH: 3650,                # 10 years
            DataCategory.BEHAVIORAL: 730,             # 2 years
            DataCategory.TECHNICAL: 1095,             # 3 years
            DataCategory.METADATA: 30                 # 1 month
        }
    
    async def track_data_access(self, user_id: str, data_type: str, 
                              data_size: int, purpose: str,
                              context: Dict[str, Any] = None) -> str:
        """Track data access event."""
        try:
            if context is None:
                context = {}
            
            # Classify data
            classification = self._classify_data(data_type)
            
            # Check consent
            consent_valid = await self._check_consent(user_id, classification["category"], purpose)
            
            # Create privacy event
            event = PrivacyEvent(
                event_id=f"privacy_evt_{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}",
                event_type="data_access",
                data_category=classification["category"],
                privacy_level=classification["privacy_level"],
                user_id=user_id,
                data_size=data_size,
                anonymized=context.get("anonymized", False),
                encrypted=context.get("encrypted", True),
                consent_given=consent_valid,
                purpose=purpose,
                timestamp=time.time(),
                metadata=context
            )
            
            # Store event
            self.privacy_events.append(event)
            
            # Record analytics
            self.analytics.record_metric("privacy_data_access", 1.0, {
                "data_category": classification["category"].value,
                "privacy_level": classification["privacy_level"].value,
                "consent_valid": consent_valid
            })
            
            # Check for privacy violations
            await self._check_privacy_violations(event)
            
            return event.event_id
            
        except Exception as e:
            logger.error(f"Error tracking data access: {e}")
            return ""
    
    def _classify_data(self, data_type: str) -> Dict[str, Any]:
        """Classify data type for privacy purposes."""
        try:
            # Check classification rules
            for pattern, classification in self.data_classification_rules.items():
                if pattern.lower() in data_type.lower():
                    return classification
            
            # Default classification
            return {
                "category": DataCategory.TECHNICAL,
                "privacy_level": PrivacyLevel.INTERNAL,
                "requires_consent": False
            }
            
        except Exception as e:
            logger.error(f"Error classifying data type {data_type}: {e}")
            return {
                "category": DataCategory.TECHNICAL,
                "privacy_level": PrivacyLevel.INTERNAL,
                "requires_consent": False
            }
    
    async def _check_consent(self, user_id: str, data_category: DataCategory, purpose: str) -> bool:
        """Check if user has given valid consent."""
        try:
            # Find relevant consent records
            user_consents = [
                consent for consent in self.consent_records.values()
                if consent.user_id == user_id and data_category in consent.data_categories
            ]
            
            if not user_consents:
                return False
            
            current_time = time.time()
            
            # Check for valid, non-expired consent
            for consent in user_consents:
                if (consent.granted and 
                    consent.withdrawal_date is None and
                    purpose in consent.purposes and
                    (consent.expiry_date is None or consent.expiry_date > current_time)):
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking consent for user {user_id}: {e}")
            return False
    
    async def _check_privacy_violations(self, event: PrivacyEvent):
        """Check for potential privacy violations."""
        try:
            violations = []
            
            # Check consent requirement
            classification = self.data_classification_rules.get(event.data_category.value, {})
            if classification.get("requires_consent", False) and not event.consent_given:
                violations.append("missing_consent")
            
            # Check encryption requirement
            if event.privacy_level in [PrivacyLevel.CONFIDENTIAL, PrivacyLevel.RESTRICTED, PrivacyLevel.TOP_SECRET]:
                if not event.encrypted:
                    violations.append("missing_encryption")
            
            # Check data size limits
            if event.data_size > 1000000:  # 1MB limit for sensitive data
                if event.data_category in [DataCategory.PERSONAL_IDENTIFIABLE, DataCategory.SENSITIVE_PERSONAL]:
                    violations.append("excessive_data_size")
            
            # Record violations
            if violations:
                self.analytics.record_event(
                    event_type="privacy_violation",
                    data={
                        "event_id": event.event_id,
                        "violations": violations,
                        "user_id": event.user_id,
                        "data_category": event.data_category.value
                    },
                    source="privacy_analytics",
                    severity="warning"
                )
                
                logger.warning(f"Privacy violations detected: {violations} for event {event.event_id}")
            
        except Exception as e:
            logger.error(f"Error checking privacy violations: {e}")
    
    async def record_consent(self, user_id: str, data_categories: List[DataCategory],
                           purposes: List[str], granted: bool,
                           expiry_days: Optional[int] = None) -> str:
        """Record user consent."""
        try:
            current_time = time.time()
            expiry_date = None
            
            if expiry_days:
                expiry_date = current_time + (expiry_days * 24 * 3600)
            elif granted:
                expiry_date = current_time + (self.consent_expiry_days * 24 * 3600)
            
            consent = ConsentRecord(
                consent_id=f"consent_{int(current_time * 1000)}_{uuid.uuid4().hex[:8]}",
                user_id=user_id,
                data_categories=data_categories,
                purposes=purposes,
                granted=granted,
                timestamp=current_time,
                expiry_date=expiry_date,
                withdrawal_date=None,
                metadata={}
            )
            
            self.consent_records[consent.consent_id] = consent
            
            # Record analytics
            self.analytics.record_metric("privacy_consent_recorded", 1.0, {
                "user_id": user_id,
                "granted": granted,
                "categories_count": len(data_categories)
            })
            
            logger.info(f"Recorded consent for user {user_id}: {granted}")
            return consent.consent_id
            
        except Exception as e:
            logger.error(f"Error recording consent: {e}")
            return ""
    
    async def withdraw_consent(self, user_id: str, consent_id: str) -> bool:
        """Withdraw user consent."""
        try:
            if consent_id in self.consent_records:
                consent = self.consent_records[consent_id]
                if consent.user_id == user_id:
                    consent.withdrawal_date = time.time()
                    
                    # Record analytics
                    self.analytics.record_metric("privacy_consent_withdrawn", 1.0, {
                        "user_id": user_id,
                        "consent_id": consent_id
                    })
                    
                    logger.info(f"Consent withdrawn for user {user_id}: {consent_id}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error withdrawing consent: {e}")
            return False
    
    async def track_data_flow(self, source: str, destination: str, 
                            data_category: DataCategory, data_size: int,
                            purpose: str, anonymized: bool = False,
                            encrypted: bool = True) -> str:
        """Track data flow between systems."""
        try:
            # Determine privacy level based on category
            privacy_level = self._get_privacy_level_for_category(data_category)
            
            # Determine retention period
            retention_period = self.retention_policies.get(data_category, self.default_retention_days)
            
            flow = DataFlowRecord(
                flow_id=f"flow_{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}",
                source=source,
                destination=destination,
                data_category=data_category,
                privacy_level=privacy_level,
                data_size=data_size,
                processing_purpose=purpose,
                retention_period=retention_period,
                anonymization_applied=anonymized,
                encryption_applied=encrypted,
                timestamp=time.time()
            )
            
            self.data_flows.append(flow)
            
            # Record analytics
            self.analytics.record_metric("privacy_data_flow", 1.0, {
                "source": source,
                "destination": destination,
                "data_category": data_category.value,
                "data_size": data_size
            })
            
            return flow.flow_id
            
        except Exception as e:
            logger.error(f"Error tracking data flow: {e}")
            return ""
    
    def _get_privacy_level_for_category(self, category: DataCategory) -> PrivacyLevel:
        """Get privacy level for data category."""
        mapping = {
            DataCategory.PERSONAL_IDENTIFIABLE: PrivacyLevel.CONFIDENTIAL,
            DataCategory.SENSITIVE_PERSONAL: PrivacyLevel.RESTRICTED,
            DataCategory.BIOMETRIC: PrivacyLevel.RESTRICTED,
            DataCategory.FINANCIAL: PrivacyLevel.CONFIDENTIAL,
            DataCategory.HEALTH: PrivacyLevel.RESTRICTED,
            DataCategory.BEHAVIORAL: PrivacyLevel.INTERNAL,
            DataCategory.TECHNICAL: PrivacyLevel.INTERNAL,
            DataCategory.METADATA: PrivacyLevel.PUBLIC
        }
        return mapping.get(category, PrivacyLevel.INTERNAL)
    
    async def analyze_anonymization_effectiveness(self, dataset_id: str, 
                                                original_data: List[Dict[str, Any]],
                                                anonymized_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze effectiveness of anonymization."""
        try:
            if len(original_data) != len(anonymized_data):
                raise ValueError("Original and anonymized datasets must have same length")
            
            # Calculate k-anonymity
            k_anonymity = self._calculate_k_anonymity(anonymized_data)
            
            # Calculate l-diversity
            l_diversity = self._calculate_l_diversity(anonymized_data)
            
            # Calculate information loss
            info_loss = self._calculate_information_loss(original_data, anonymized_data)
            
            # Calculate re-identification risk
            reident_risk = self._calculate_reidentification_risk(anonymized_data)
            
            metrics = {
                "dataset_id": dataset_id,
                "k_anonymity": k_anonymity,
                "l_diversity": l_diversity,
                "information_loss": info_loss,
                "reidentification_risk": reident_risk,
                "effectiveness_score": self._calculate_effectiveness_score(k_anonymity, l_diversity, info_loss),
                "timestamp": time.time()
            }
            
            self.anonymization_metrics[dataset_id] = metrics
            
            # Record analytics
            self.analytics.record_metric("privacy_anonymization_effectiveness", metrics["effectiveness_score"])
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error analyzing anonymization effectiveness: {e}")
            return {}
    
    def _calculate_k_anonymity(self, data: List[Dict[str, Any]]) -> int:
        """Calculate k-anonymity of dataset."""
        try:
            if not data:
                return 0
            
            # Group by quasi-identifiers (simplified)
            groups = defaultdict(int)
            for record in data:
                # Create a key from quasi-identifiers
                key = tuple(sorted(record.items()))
                groups[key] += 1
            
            # Return minimum group size
            return min(groups.values()) if groups else 0
            
        except Exception as e:
            logger.error(f"Error calculating k-anonymity: {e}")
            return 0
    
    def _calculate_l_diversity(self, data: List[Dict[str, Any]]) -> float:
        """Calculate l-diversity of dataset."""
        try:
            if not data:
                return 0.0
            
            # Simplified l-diversity calculation
            # In practice, this would analyze sensitive attributes
            unique_values = set()
            for record in data:
                for value in record.values():
                    unique_values.add(str(value))
            
            return len(unique_values) / len(data) if data else 0.0
            
        except Exception as e:
            logger.error(f"Error calculating l-diversity: {e}")
            return 0.0
    
    def _calculate_information_loss(self, original: List[Dict[str, Any]], 
                                  anonymized: List[Dict[str, Any]]) -> float:
        """Calculate information loss due to anonymization."""
        try:
            if not original or not anonymized:
                return 1.0
            
            # Simplified information loss calculation
            total_fields = len(original[0]) if original else 0
            if total_fields == 0:
                return 1.0
            
            # Count fields that were modified/suppressed
            modified_fields = 0
            for orig_record, anon_record in zip(original, anonymized):
                for key in orig_record:
                    if key in anon_record:
                        if orig_record[key] != anon_record[key]:
                            modified_fields += 1
                    else:
                        modified_fields += 1
            
            return modified_fields / (total_fields * len(original))
            
        except Exception as e:
            logger.error(f"Error calculating information loss: {e}")
            return 1.0
    
    def _calculate_reidentification_risk(self, data: List[Dict[str, Any]]) -> float:
        """Calculate re-identification risk."""
        try:
            if not data:
                return 1.0
            
            # Simplified risk calculation based on uniqueness
            unique_records = len(set(tuple(sorted(record.items())) for record in data))
            total_records = len(data)
            
            # Higher uniqueness = higher re-identification risk
            return unique_records / total_records if total_records > 0 else 1.0
            
        except Exception as e:
            logger.error(f"Error calculating re-identification risk: {e}")
            return 1.0
    
    def _calculate_effectiveness_score(self, k_anonymity: int, l_diversity: float, info_loss: float) -> float:
        """Calculate overall anonymization effectiveness score."""
        try:
            # Normalize k-anonymity (higher is better)
            k_score = min(k_anonymity / 10.0, 1.0)
            
            # Normalize l-diversity (higher is better)
            l_score = min(l_diversity, 1.0)
            
            # Information loss (lower is better)
            loss_score = 1.0 - min(info_loss, 1.0)
            
            # Weighted average
            effectiveness = (k_score * 0.4 + l_score * 0.3 + loss_score * 0.3)
            
            return effectiveness
            
        except Exception as e:
            logger.error(f"Error calculating effectiveness score: {e}")
            return 0.0
    
    async def get_privacy_compliance_report(self) -> Dict[str, Any]:
        """Generate privacy compliance report."""
        try:
            current_time = time.time()
            
            report = {
                "report_timestamp": current_time,
                "total_privacy_events": len(self.privacy_events),
                "total_data_flows": len(self.data_flows),
                "total_consent_records": len(self.consent_records),
                "consent_compliance": {},
                "data_retention_compliance": {},
                "anonymization_compliance": {},
                "privacy_violations": [],
                "recommendations": []
            }
            
            # Analyze consent compliance
            report["consent_compliance"] = await self._analyze_consent_compliance()
            
            # Analyze data retention compliance
            report["data_retention_compliance"] = await self._analyze_retention_compliance()
            
            # Analyze anonymization compliance
            report["anonymization_compliance"] = await self._analyze_anonymization_compliance()
            
            # Identify privacy violations
            report["privacy_violations"] = await self._identify_privacy_violations()
            
            # Generate recommendations
            report["recommendations"] = await self._generate_privacy_recommendations(report)
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating privacy compliance report: {e}")
            return {}
    
    async def _analyze_consent_compliance(self) -> Dict[str, Any]:
        """Analyze consent compliance."""
        try:
            total_consents = len(self.consent_records)
            granted_consents = sum(1 for c in self.consent_records.values() if c.granted)
            expired_consents = sum(1 for c in self.consent_records.values() 
                                 if c.expiry_date and c.expiry_date < time.time())
            
            return {
                "total_consents": total_consents,
                "granted_consents": granted_consents,
                "expired_consents": expired_consents,
                "compliance_rate": granted_consents / total_consents if total_consents > 0 else 0.0
            }
            
        except Exception as e:
            logger.error(f"Error analyzing consent compliance: {e}")
            return {}
    
    async def _analyze_retention_compliance(self) -> Dict[str, Any]:
        """Analyze data retention compliance."""
        try:
            current_time = time.time()
            compliance_issues = []
            
            for flow in self.data_flows:
                retention_deadline = flow.timestamp + (flow.retention_period * 24 * 3600)
                if current_time > retention_deadline:
                    compliance_issues.append({
                        "flow_id": flow.flow_id,
                        "data_category": flow.data_category.value,
                        "days_overdue": (current_time - retention_deadline) / (24 * 3600)
                    })
            
            return {
                "total_flows": len(self.data_flows),
                "overdue_flows": len(compliance_issues),
                "compliance_rate": 1.0 - (len(compliance_issues) / len(self.data_flows)) if self.data_flows else 1.0,
                "overdue_details": compliance_issues[:10]  # Top 10 issues
            }
            
        except Exception as e:
            logger.error(f"Error analyzing retention compliance: {e}")
            return {}
    
    async def _analyze_anonymization_compliance(self) -> Dict[str, Any]:
        """Analyze anonymization compliance."""
        try:
            compliant_datasets = sum(1 for metrics in self.anonymization_metrics.values()
                                   if metrics.get("effectiveness_score", 0) >= self.anonymization_threshold)
            
            return {
                "total_datasets": len(self.anonymization_metrics),
                "compliant_datasets": compliant_datasets,
                "compliance_rate": compliant_datasets / len(self.anonymization_metrics) if self.anonymization_metrics else 1.0,
                "average_effectiveness": sum(m.get("effectiveness_score", 0) for m in self.anonymization_metrics.values()) / len(self.anonymization_metrics) if self.anonymization_metrics else 0.0
            }
            
        except Exception as e:
            logger.error(f"Error analyzing anonymization compliance: {e}")
            return {}
    
    async def _identify_privacy_violations(self) -> List[Dict[str, Any]]:
        """Identify privacy violations."""
        try:
            violations = []
            
            # Check for events without proper consent
            for event in self.privacy_events[-1000:]:  # Check recent events
                if not event.consent_given and event.data_category in [
                    DataCategory.PERSONAL_IDENTIFIABLE, 
                    DataCategory.SENSITIVE_PERSONAL,
                    DataCategory.BIOMETRIC
                ]:
                    violations.append({
                        "type": "missing_consent",
                        "event_id": event.event_id,
                        "user_id": event.user_id,
                        "data_category": event.data_category.value,
                        "timestamp": event.timestamp
                    })
            
            return violations[:50]  # Return top 50 violations
            
        except Exception as e:
            logger.error(f"Error identifying privacy violations: {e}")
            return []
    
    async def _generate_privacy_recommendations(self, report: Dict[str, Any]) -> List[str]:
        """Generate privacy recommendations based on compliance report."""
        try:
            recommendations = []
            
            # Consent compliance recommendations
            consent_rate = report.get("consent_compliance", {}).get("compliance_rate", 1.0)
            if consent_rate < 0.8:
                recommendations.append("Improve consent collection processes - compliance rate below 80%")
            
            # Retention compliance recommendations
            retention_rate = report.get("data_retention_compliance", {}).get("compliance_rate", 1.0)
            if retention_rate < 0.9:
                recommendations.append("Implement automated data deletion for expired retention periods")
            
            # Anonymization recommendations
            anon_rate = report.get("anonymization_compliance", {}).get("compliance_rate", 1.0)
            if anon_rate < 0.8:
                recommendations.append("Improve anonymization techniques to meet privacy thresholds")
            
            # Violation recommendations
            violations = report.get("privacy_violations", [])
            if len(violations) > 10:
                recommendations.append("Address privacy violations - implement stronger consent validation")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating privacy recommendations: {e}")
            return []


# Factory functions
def create_privacy_event(event_type: str, data_category: DataCategory, user_id: str) -> PrivacyEvent:
    """Create a new privacy event."""
    return PrivacyEvent(
        event_id=f"privacy_evt_{int(time.time() * 1000)}",
        event_type=event_type,
        data_category=data_category,
        privacy_level=PrivacyLevel.INTERNAL,
        user_id=user_id,
        data_size=0,
        anonymized=False,
        encrypted=True,
        consent_given=False,
        purpose="unknown",
        timestamp=time.time(),
        metadata={}
    )
