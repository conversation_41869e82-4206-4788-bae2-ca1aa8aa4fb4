"""
Real-Time Analytics Module
Provides real-time analytics and monitoring capabilities for the Ultimate Voice AI System.
"""

import asyncio
import time
import json
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from datetime import datetime, timedelta
import threading
import queue

from loguru import logger

from ..utils.config_manager import ConfigManager


@dataclass
class AnalyticsEvent:
    """Real-time analytics event."""
    event_id: str
    event_type: str
    timestamp: float
    data: Dict[str, Any]
    source: str
    severity: str
    tags: List[str]


@dataclass
class MetricSnapshot:
    """Snapshot of a metric at a point in time."""
    metric_name: str
    value: float
    timestamp: float
    metadata: Dict[str, Any]


@dataclass
class AlertRule:
    """Alert rule configuration."""
    rule_id: str
    metric_name: str
    condition: str  # 'greater_than', 'less_than', 'equals', 'not_equals'
    threshold: float
    duration: float  # seconds
    enabled: bool
    callback: Optional[Callable]


class RealTimeAnalytics:
    """
    Real-time analytics and monitoring system.
    
    Features:
    - Real-time metric collection
    - Event streaming and processing
    - Alert and notification system
    - Performance monitoring
    - Resource usage tracking
    - Custom metric support
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        
        # Analytics data storage
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.events: deque = deque(maxlen=5000)
        self.alert_rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, Dict[str, Any]] = {}
        
        # Real-time processing
        self.event_queue: queue.Queue = queue.Queue()
        self.metric_queue: queue.Queue = queue.Queue()
        self.processing_thread: Optional[threading.Thread] = None
        self.is_running = False
        
        # Configuration
        self.collection_interval = self.config.get("analytics.real_time.collection_interval", 1.0)
        self.retention_hours = self.config.get("analytics.real_time.retention_hours", 24)
        self.alert_cooldown = self.config.get("analytics.real_time.alert_cooldown", 300)  # 5 minutes
        
        # Callbacks
        self.event_callbacks: List[Callable] = []
        self.alert_callbacks: List[Callable] = []
        
        logger.info("Real-Time Analytics initialized")
    
    async def start(self):
        """Start real-time analytics processing."""
        try:
            self.is_running = True
            
            # Start processing thread
            self.processing_thread = threading.Thread(target=self._processing_loop, daemon=True)
            self.processing_thread.start()
            
            # Initialize default alert rules
            self._initialize_default_alerts()
            
            logger.info("Real-time analytics started")
            
        except Exception as e:
            logger.error(f"Error starting real-time analytics: {e}")
            self.is_running = False
    
    async def stop(self):
        """Stop real-time analytics processing."""
        self.is_running = False
        
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=5.0)
        
        logger.info("Real-time analytics stopped")
    
    def record_event(self, event_type: str, data: Dict[str, Any], 
                    source: str = "system", severity: str = "info", tags: List[str] = None):
        """Record a real-time event."""
        try:
            if tags is None:
                tags = []
            
            event = AnalyticsEvent(
                event_id=f"evt_{int(time.time() * 1000)}_{hash(str(data)) % 10000}",
                event_type=event_type,
                timestamp=time.time(),
                data=data,
                source=source,
                severity=severity,
                tags=tags
            )
            
            # Add to queue for processing
            self.event_queue.put(event)
            
        except Exception as e:
            logger.error(f"Error recording event: {e}")
    
    def record_metric(self, metric_name: str, value: float, metadata: Dict[str, Any] = None):
        """Record a real-time metric."""
        try:
            if metadata is None:
                metadata = {}
            
            snapshot = MetricSnapshot(
                metric_name=metric_name,
                value=value,
                timestamp=time.time(),
                metadata=metadata
            )
            
            # Add to queue for processing
            self.metric_queue.put(snapshot)
            
        except Exception as e:
            logger.error(f"Error recording metric: {e}")
    
    def _processing_loop(self):
        """Main processing loop for real-time analytics."""
        while self.is_running:
            try:
                # Process events
                self._process_events()
                
                # Process metrics
                self._process_metrics()
                
                # Check alerts
                self._check_alerts()
                
                # Cleanup old data
                self._cleanup_old_data()
                
                time.sleep(0.1)  # Small delay to prevent excessive CPU usage
                
            except Exception as e:
                logger.error(f"Error in analytics processing loop: {e}")
                time.sleep(1.0)
    
    def _process_events(self):
        """Process queued events."""
        try:
            while not self.event_queue.empty():
                try:
                    event = self.event_queue.get_nowait()
                    
                    # Store event
                    self.events.append(event)
                    
                    # Trigger callbacks
                    for callback in self.event_callbacks:
                        try:
                            callback(event)
                        except Exception as e:
                            logger.error(f"Error in event callback: {e}")
                    
                    # Log significant events
                    if event.severity in ['warning', 'error', 'critical']:
                        logger.warning(f"Analytics event: {event.event_type} - {event.data}")
                    
                except queue.Empty:
                    break
                except Exception as e:
                    logger.error(f"Error processing event: {e}")
                    
        except Exception as e:
            logger.error(f"Error in event processing: {e}")
    
    def _process_metrics(self):
        """Process queued metrics."""
        try:
            while not self.metric_queue.empty():
                try:
                    snapshot = self.metric_queue.get_nowait()
                    
                    # Store metric
                    self.metrics[snapshot.metric_name].append(snapshot)
                    
                    # Update derived metrics
                    self._update_derived_metrics(snapshot)
                    
                except queue.Empty:
                    break
                except Exception as e:
                    logger.error(f"Error processing metric: {e}")
                    
        except Exception as e:
            logger.error(f"Error in metric processing: {e}")
    
    def _update_derived_metrics(self, snapshot: MetricSnapshot):
        """Update derived metrics based on new snapshot."""
        try:
            metric_name = snapshot.metric_name
            metric_data = self.metrics[metric_name]
            
            if len(metric_data) >= 2:
                # Calculate rate of change
                prev_snapshot = metric_data[-2]
                time_diff = snapshot.timestamp - prev_snapshot.timestamp
                
                if time_diff > 0:
                    rate = (snapshot.value - prev_snapshot.value) / time_diff
                    rate_metric_name = f"{metric_name}_rate"
                    
                    rate_snapshot = MetricSnapshot(
                        metric_name=rate_metric_name,
                        value=rate,
                        timestamp=snapshot.timestamp,
                        metadata={"derived_from": metric_name}
                    )
                    
                    self.metrics[rate_metric_name].append(rate_snapshot)
            
            # Calculate moving averages
            if len(metric_data) >= 10:
                recent_values = [s.value for s in list(metric_data)[-10:]]
                avg_value = sum(recent_values) / len(recent_values)
                
                avg_metric_name = f"{metric_name}_avg_10"
                avg_snapshot = MetricSnapshot(
                    metric_name=avg_metric_name,
                    value=avg_value,
                    timestamp=snapshot.timestamp,
                    metadata={"derived_from": metric_name, "window_size": 10}
                )
                
                self.metrics[avg_metric_name].append(avg_snapshot)
                
        except Exception as e:
            logger.error(f"Error updating derived metrics: {e}")
    
    def _check_alerts(self):
        """Check alert rules and trigger alerts."""
        try:
            current_time = time.time()
            
            for rule_id, rule in self.alert_rules.items():
                if not rule.enabled:
                    continue
                
                try:
                    # Check if alert is in cooldown
                    if rule_id in self.active_alerts:
                        last_alert_time = self.active_alerts[rule_id].get('last_triggered', 0)
                        if current_time - last_alert_time < self.alert_cooldown:
                            continue
                    
                    # Get recent metric data
                    metric_data = self.metrics.get(rule.metric_name, deque())
                    if not metric_data:
                        continue
                    
                    # Check condition over duration
                    duration_start = current_time - rule.duration
                    relevant_data = [s for s in metric_data if s.timestamp >= duration_start]
                    
                    if not relevant_data:
                        continue
                    
                    # Evaluate condition
                    condition_met = self._evaluate_alert_condition(rule, relevant_data)
                    
                    if condition_met:
                        self._trigger_alert(rule, relevant_data[-1])
                    elif rule_id in self.active_alerts:
                        # Clear alert if condition no longer met
                        del self.active_alerts[rule_id]
                        
                except Exception as e:
                    logger.error(f"Error checking alert rule {rule_id}: {e}")
                    
        except Exception as e:
            logger.error(f"Error in alert checking: {e}")
    
    def _evaluate_alert_condition(self, rule: AlertRule, data: List[MetricSnapshot]) -> bool:
        """Evaluate if alert condition is met."""
        try:
            if not data:
                return False
            
            # Use the most recent value for evaluation
            current_value = data[-1].value
            
            if rule.condition == "greater_than":
                return current_value > rule.threshold
            elif rule.condition == "less_than":
                return current_value < rule.threshold
            elif rule.condition == "equals":
                return abs(current_value - rule.threshold) < 0.001
            elif rule.condition == "not_equals":
                return abs(current_value - rule.threshold) >= 0.001
            
            return False
            
        except Exception as e:
            logger.error(f"Error evaluating alert condition: {e}")
            return False
    
    def _trigger_alert(self, rule: AlertRule, snapshot: MetricSnapshot):
        """Trigger an alert."""
        try:
            alert_data = {
                'rule_id': rule.rule_id,
                'metric_name': rule.metric_name,
                'current_value': snapshot.value,
                'threshold': rule.threshold,
                'condition': rule.condition,
                'timestamp': snapshot.timestamp
            }
            
            # Record alert as event
            self.record_event(
                event_type="alert_triggered",
                data=alert_data,
                source="alert_system",
                severity="warning",
                tags=["alert", rule.metric_name]
            )
            
            # Update active alerts
            self.active_alerts[rule.rule_id] = {
                'triggered_at': snapshot.timestamp,
                'last_triggered': snapshot.timestamp,
                'alert_data': alert_data
            }
            
            # Call rule callback if available
            if rule.callback:
                try:
                    rule.callback(alert_data)
                except Exception as e:
                    logger.error(f"Error in alert callback: {e}")
            
            # Call global alert callbacks
            for callback in self.alert_callbacks:
                try:
                    callback(alert_data)
                except Exception as e:
                    logger.error(f"Error in global alert callback: {e}")
            
            logger.warning(f"Alert triggered: {rule.rule_id} - {rule.metric_name} {rule.condition} {rule.threshold}")
            
        except Exception as e:
            logger.error(f"Error triggering alert: {e}")
    
    def _cleanup_old_data(self):
        """Clean up old analytics data."""
        try:
            current_time = time.time()
            cutoff_time = current_time - (self.retention_hours * 3600)
            
            # Clean up old events
            while self.events and self.events[0].timestamp < cutoff_time:
                self.events.popleft()
            
            # Clean up old metrics
            for metric_name, metric_data in self.metrics.items():
                while metric_data and metric_data[0].timestamp < cutoff_time:
                    metric_data.popleft()
            
        except Exception as e:
            logger.error(f"Error cleaning up old data: {e}")
    
    def _initialize_default_alerts(self):
        """Initialize default alert rules."""
        try:
            # CPU usage alert
            self.add_alert_rule(
                rule_id="high_cpu_usage",
                metric_name="cpu_usage",
                condition="greater_than",
                threshold=85.0,
                duration=60.0,  # 1 minute
                enabled=True
            )
            
            # Memory usage alert
            self.add_alert_rule(
                rule_id="high_memory_usage",
                metric_name="memory_usage",
                condition="greater_than",
                threshold=90.0,
                duration=30.0,  # 30 seconds
                enabled=True
            )
            
            # Error rate alert
            self.add_alert_rule(
                rule_id="high_error_rate",
                metric_name="error_rate",
                condition="greater_than",
                threshold=0.1,  # 10%
                duration=120.0,  # 2 minutes
                enabled=True
            )
            
            logger.info("Default alert rules initialized")
            
        except Exception as e:
            logger.error(f"Error initializing default alerts: {e}")
    
    def add_alert_rule(self, rule_id: str, metric_name: str, condition: str, 
                      threshold: float, duration: float, enabled: bool = True,
                      callback: Optional[Callable] = None):
        """Add a new alert rule."""
        try:
            rule = AlertRule(
                rule_id=rule_id,
                metric_name=metric_name,
                condition=condition,
                threshold=threshold,
                duration=duration,
                enabled=enabled,
                callback=callback
            )
            
            self.alert_rules[rule_id] = rule
            logger.info(f"Added alert rule: {rule_id}")
            
        except Exception as e:
            logger.error(f"Error adding alert rule: {e}")
    
    def remove_alert_rule(self, rule_id: str):
        """Remove an alert rule."""
        try:
            if rule_id in self.alert_rules:
                del self.alert_rules[rule_id]
                
                # Clear active alert if exists
                if rule_id in self.active_alerts:
                    del self.active_alerts[rule_id]
                
                logger.info(f"Removed alert rule: {rule_id}")
            
        except Exception as e:
            logger.error(f"Error removing alert rule: {e}")
    
    def add_event_callback(self, callback: Callable):
        """Add a callback for events."""
        self.event_callbacks.append(callback)
    
    def add_alert_callback(self, callback: Callable):
        """Add a callback for alerts."""
        self.alert_callbacks.append(callback)
    
    def get_recent_events(self, event_type: Optional[str] = None, 
                         limit: int = 100) -> List[AnalyticsEvent]:
        """Get recent events."""
        try:
            events = list(self.events)
            
            if event_type:
                events = [e for e in events if e.event_type == event_type]
            
            # Sort by timestamp (most recent first)
            events.sort(key=lambda e: e.timestamp, reverse=True)
            
            return events[:limit]
            
        except Exception as e:
            logger.error(f"Error getting recent events: {e}")
            return []
    
    def get_metric_data(self, metric_name: str, 
                       start_time: Optional[float] = None,
                       end_time: Optional[float] = None) -> List[MetricSnapshot]:
        """Get metric data for a time range."""
        try:
            if metric_name not in self.metrics:
                return []
            
            data = list(self.metrics[metric_name])
            
            if start_time:
                data = [s for s in data if s.timestamp >= start_time]
            
            if end_time:
                data = [s for s in data if s.timestamp <= end_time]
            
            return data
            
        except Exception as e:
            logger.error(f"Error getting metric data: {e}")
            return []
    
    def get_current_metrics(self) -> Dict[str, float]:
        """Get current values for all metrics."""
        try:
            current_metrics = {}
            
            for metric_name, metric_data in self.metrics.items():
                if metric_data:
                    current_metrics[metric_name] = metric_data[-1].value
            
            return current_metrics
            
        except Exception as e:
            logger.error(f"Error getting current metrics: {e}")
            return {}
    
    def get_analytics_summary(self) -> Dict[str, Any]:
        """Get a summary of analytics data."""
        try:
            summary = {
                'total_events': len(self.events),
                'total_metrics': len(self.metrics),
                'active_alerts': len(self.active_alerts),
                'alert_rules': len(self.alert_rules),
                'current_metrics': self.get_current_metrics(),
                'recent_events_by_type': {},
                'system_status': 'healthy'
            }
            
            # Count recent events by type
            recent_events = self.get_recent_events(limit=1000)
            event_counts = defaultdict(int)
            for event in recent_events:
                event_counts[event.event_type] += 1
            
            summary['recent_events_by_type'] = dict(event_counts)
            
            # Determine system status
            if self.active_alerts:
                summary['system_status'] = 'warning'
                
                # Check for critical alerts
                for alert_data in self.active_alerts.values():
                    if alert_data.get('alert_data', {}).get('condition') == 'critical':
                        summary['system_status'] = 'critical'
                        break
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting analytics summary: {e}")
            return {}


# Factory functions
def create_analytics_event(event_type: str, data: Dict[str, Any]) -> AnalyticsEvent:
    """Create a new analytics event."""
    return AnalyticsEvent(
        event_id=f"evt_{int(time.time() * 1000)}",
        event_type=event_type,
        timestamp=time.time(),
        data=data,
        source="manual",
        severity="info",
        tags=[]
    )

def create_metric_snapshot(metric_name: str, value: float) -> MetricSnapshot:
    """Create a new metric snapshot."""
    return MetricSnapshot(
        metric_name=metric_name,
        value=value,
        timestamp=time.time(),
        metadata={}
    )

# Alias for compatibility
RealTimeAnalyticsEngine = RealTimeAnalytics
