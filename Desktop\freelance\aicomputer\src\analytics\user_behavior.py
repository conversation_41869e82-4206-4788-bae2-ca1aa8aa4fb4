"""
User Behavior Analytics Engine
Phase 8 - Advanced Analytics & Global Intelligence

This module provides comprehensive user behavior analysis, pattern recognition,
and personalization capabilities for the Voice AI system.
"""

import asyncio
import json
import sqlite3
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import numpy as np
from pathlib import Path

from ..utils.config_manager import ConfigManager


@dataclass
class UserAction:
    """Represents a single user action for analysis."""
    timestamp: float
    command: str
    intent: str
    success: bool
    execution_time: float
    context: Dict[str, Any]
    user_id: str = "default"


@dataclass
class BehaviorPattern:
    """Represents a detected behavior pattern."""
    pattern_id: str
    pattern_type: str
    frequency: int
    confidence: float
    description: str
    suggestions: List[str]
    last_seen: float


@dataclass
class UserProfile:
    """Comprehensive user profile with preferences and patterns."""
    user_id: str
    created_at: float
    last_active: float
    total_commands: int
    success_rate: float
    preferred_times: List[int]  # Hours of day
    common_commands: Dict[str, int]
    efficiency_score: float
    learning_progress: float


class UserBehaviorAnalytics:
    """
    Advanced user behavior analytics engine with pattern recognition,
    personalization, and predictive modeling capabilities.
    """
    
    def __init__(self, config: ConfigManager):
        self.config = config
        self.db_path = Path("data/user_behavior.db")
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Analytics configuration
        self.max_history_days = config.get("analytics.max_history_days", 90)
        self.pattern_min_frequency = config.get("analytics.pattern_min_frequency", 3)
        self.confidence_threshold = config.get("analytics.confidence_threshold", 0.7)
        
        # In-memory caches
        self.recent_actions = deque(maxlen=1000)
        self.user_profiles = {}
        self.detected_patterns = {}
        self.prediction_cache = {}
        
        # Analytics state
        self.is_running = False
        self.last_analysis = 0
        self.analysis_interval = 300  # 5 minutes
        
        # Initialize database
        self._init_database()
    
    def _init_database(self):
        """Initialize the user behavior database."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS user_actions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp REAL NOT NULL,
                    user_id TEXT NOT NULL,
                    command TEXT NOT NULL,
                    intent TEXT NOT NULL,
                    success BOOLEAN NOT NULL,
                    execution_time REAL NOT NULL,
                    context TEXT NOT NULL
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS behavior_patterns (
                    pattern_id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    pattern_type TEXT NOT NULL,
                    frequency INTEGER NOT NULL,
                    confidence REAL NOT NULL,
                    description TEXT NOT NULL,
                    suggestions TEXT NOT NULL,
                    last_seen REAL NOT NULL,
                    created_at REAL NOT NULL
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS user_profiles (
                    user_id TEXT PRIMARY KEY,
                    created_at REAL NOT NULL,
                    last_active REAL NOT NULL,
                    total_commands INTEGER NOT NULL,
                    success_rate REAL NOT NULL,
                    preferred_times TEXT NOT NULL,
                    common_commands TEXT NOT NULL,
                    efficiency_score REAL NOT NULL,
                    learning_progress REAL NOT NULL
                )
            """)
            
            # Create indexes for performance
            conn.execute("CREATE INDEX IF NOT EXISTS idx_actions_timestamp ON user_actions(timestamp)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_actions_user ON user_actions(user_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_patterns_user ON behavior_patterns(user_id)")
    
    async def start(self):
        """Start the user behavior analytics engine."""
        self.is_running = True
        
        # Load existing data
        await self._load_user_profiles()
        await self._load_behavior_patterns()
        
        # Start background analysis task
        asyncio.create_task(self._background_analysis())
    
    async def stop(self):
        """Stop the user behavior analytics engine."""
        self.is_running = False
        await self._save_all_data()
    
    async def record_action(self, action: UserAction):
        """Record a user action for analysis."""
        # Add to recent actions cache
        self.recent_actions.append(action)
        
        # Store in database
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO user_actions 
                (timestamp, user_id, command, intent, success, execution_time, context)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                action.timestamp,
                action.user_id,
                action.command,
                action.intent,
                action.success,
                action.execution_time,
                json.dumps(action.context)
            ))
        
        # Update user profile
        await self._update_user_profile(action)
        
        # Trigger real-time analysis if needed
        if len(self.recent_actions) % 10 == 0:
            asyncio.create_task(self._analyze_recent_patterns())
    
    async def get_user_insights(self, user_id: str = "default") -> Dict[str, Any]:
        """Get comprehensive user behavior insights."""
        profile = self.user_profiles.get(user_id)
        if not profile:
            return {"error": "User profile not found"}
        
        patterns = [p for p in self.detected_patterns.values() 
                   if p.confidence >= self.confidence_threshold]
        
        # Get recent activity
        recent_actions = [a for a in self.recent_actions 
                         if a.user_id == user_id and 
                         time.time() - a.timestamp < 86400]  # Last 24 hours
        
        # Calculate insights
        insights = {
            "profile": asdict(profile),
            "patterns": [asdict(p) for p in patterns],
            "recent_activity": {
                "commands_today": len(recent_actions),
                "success_rate_today": sum(a.success for a in recent_actions) / max(len(recent_actions), 1),
                "avg_execution_time": np.mean([a.execution_time for a in recent_actions]) if recent_actions else 0,
                "most_used_commands": self._get_top_commands(recent_actions, 5)
            },
            "recommendations": await self._generate_recommendations(user_id),
            "efficiency_trends": await self._calculate_efficiency_trends(user_id),
            "learning_opportunities": await self._identify_learning_opportunities(user_id)
        }
        
        return insights
    
    async def predict_next_action(self, user_id: str = "default", context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Predict the user's next likely action."""
        cache_key = f"{user_id}_{hash(str(context))}"
        
        if cache_key in self.prediction_cache:
            cached_result = self.prediction_cache[cache_key]
            if time.time() - cached_result["timestamp"] < 300:  # 5 minutes
                return cached_result["prediction"]
        
        # Get recent user actions
        recent_actions = [a for a in self.recent_actions 
                         if a.user_id == user_id and 
                         time.time() - a.timestamp < 3600]  # Last hour
        
        if not recent_actions:
            return {"prediction": "No recent activity", "confidence": 0.0}
        
        # Analyze patterns
        command_sequences = self._extract_command_sequences(recent_actions)
        time_patterns = self._analyze_time_patterns(recent_actions)
        context_patterns = self._analyze_context_patterns(recent_actions, context)
        
        # Generate prediction
        prediction = self._generate_prediction(command_sequences, time_patterns, context_patterns)
        
        # Cache result
        self.prediction_cache[cache_key] = {
            "prediction": prediction,
            "timestamp": time.time()
        }
        
        return prediction
    
    async def get_personalization_settings(self, user_id: str = "default") -> Dict[str, Any]:
        """Get personalized settings recommendations."""
        profile = self.user_profiles.get(user_id)
        if not profile:
            return {}
        
        settings = {
            "voice_sensitivity": self._recommend_voice_sensitivity(profile),
            "response_speed": self._recommend_response_speed(profile),
            "confirmation_level": self._recommend_confirmation_level(profile),
            "preferred_shortcuts": self._recommend_shortcuts(profile),
            "optimal_break_times": self._recommend_break_times(profile),
            "productivity_tips": self._generate_productivity_tips(profile)
        }
        
        return settings

    async def _background_analysis(self):
        """Background task for continuous behavior analysis."""
        while self.is_running:
            try:
                current_time = time.time()

                if current_time - self.last_analysis >= self.analysis_interval:
                    await self._perform_full_analysis()
                    self.last_analysis = current_time

                # Clean old data
                await self._cleanup_old_data()

                await asyncio.sleep(60)  # Check every minute

            except Exception as e:
                print(f"Error in background analysis: {e}")
                await asyncio.sleep(60)

    async def _perform_full_analysis(self):
        """Perform comprehensive behavior analysis."""
        # Analyze patterns for all users
        for user_id in self.user_profiles.keys():
            await self._analyze_user_patterns(user_id)
            await self._update_efficiency_score(user_id)
            await self._update_learning_progress(user_id)

        # Save updated data
        await self._save_behavior_patterns()
        await self._save_user_profiles()

    async def _analyze_recent_patterns(self):
        """Analyze patterns in recent actions."""
        if len(self.recent_actions) < 5:
            return

        recent = list(self.recent_actions)[-20:]  # Last 20 actions

        # Look for command sequences
        sequences = self._extract_command_sequences(recent)
        for seq in sequences:
            if len(seq) >= 2:
                pattern_id = f"sequence_{hash(str(seq))}"
                if pattern_id not in self.detected_patterns:
                    pattern = BehaviorPattern(
                        pattern_id=pattern_id,
                        pattern_type="command_sequence",
                        frequency=1,
                        confidence=0.6,
                        description=f"Command sequence: {' -> '.join(seq)}",
                        suggestions=[f"Consider creating a workflow for: {' -> '.join(seq)}"],
                        last_seen=time.time()
                    )
                    self.detected_patterns[pattern_id] = pattern
                else:
                    self.detected_patterns[pattern_id].frequency += 1
                    self.detected_patterns[pattern_id].last_seen = time.time()
                    self.detected_patterns[pattern_id].confidence = min(0.95,
                        self.detected_patterns[pattern_id].confidence + 0.1)

    async def _update_user_profile(self, action: UserAction):
        """Update user profile based on new action."""
        user_id = action.user_id

        if user_id not in self.user_profiles:
            self.user_profiles[user_id] = UserProfile(
                user_id=user_id,
                created_at=time.time(),
                last_active=action.timestamp,
                total_commands=0,
                success_rate=0.0,
                preferred_times=[],
                common_commands={},
                efficiency_score=0.0,
                learning_progress=0.0
            )

        profile = self.user_profiles[user_id]
        profile.last_active = action.timestamp
        profile.total_commands += 1

        # Update success rate
        if profile.total_commands == 1:
            profile.success_rate = 1.0 if action.success else 0.0
        else:
            profile.success_rate = (profile.success_rate * (profile.total_commands - 1) +
                                  (1.0 if action.success else 0.0)) / profile.total_commands

        # Update common commands
        if action.intent not in profile.common_commands:
            profile.common_commands[action.intent] = 0
        profile.common_commands[action.intent] += 1

        # Update preferred times
        hour = datetime.fromtimestamp(action.timestamp).hour
        if hour not in profile.preferred_times:
            profile.preferred_times.append(hour)

        # Keep only top 10 preferred times
        if len(profile.preferred_times) > 10:
            # Count frequency and keep most common
            hour_counts = defaultdict(int)
            recent_actions = [a for a in self.recent_actions if a.user_id == user_id]
            for a in recent_actions:
                h = datetime.fromtimestamp(a.timestamp).hour
                hour_counts[h] += 1

            profile.preferred_times = sorted(hour_counts.keys(),
                                           key=lambda x: hour_counts[x], reverse=True)[:10]

    async def _analyze_user_patterns(self, user_id: str):
        """Analyze behavior patterns for a specific user."""
        user_actions = [a for a in self.recent_actions if a.user_id == user_id]

        if len(user_actions) < 10:
            return

        # Temporal patterns
        await self._analyze_temporal_patterns(user_id, user_actions)

        # Command patterns
        await self._analyze_command_patterns(user_id, user_actions)

        # Efficiency patterns
        await self._analyze_efficiency_patterns(user_id, user_actions)

    async def _analyze_temporal_patterns(self, user_id: str, actions: List[UserAction]):
        """Analyze temporal usage patterns."""
        hours = [datetime.fromtimestamp(a.timestamp).hour for a in actions]
        hour_counts = defaultdict(int)
        for hour in hours:
            hour_counts[hour] += 1

        # Find peak hours
        if hour_counts:
            peak_hours = sorted(hour_counts.items(), key=lambda x: x[1], reverse=True)[:3]
            peak_description = f"Most active during: {', '.join([f'{h}:00' for h, _ in peak_hours])}"

            pattern = BehaviorPattern(
                pattern_id=f"temporal_{user_id}",
                pattern_type="temporal",
                frequency=sum(hour_counts.values()),
                confidence=0.8,
                description=peak_description,
                suggestions=[f"Consider scheduling important tasks during peak hours: {', '.join([f'{h}:00' for h, _ in peak_hours[:2]])}"],
                last_seen=time.time()
            )
            self.detected_patterns[pattern.pattern_id] = pattern

    async def _analyze_command_patterns(self, user_id: str, actions: List[UserAction]):
        """Analyze command usage patterns."""
        command_counts = defaultdict(int)
        for action in actions:
            command_counts[action.intent] += 1

        if command_counts:
            top_commands = sorted(command_counts.items(), key=lambda x: x[1], reverse=True)[:5]

            pattern = BehaviorPattern(
                pattern_id=f"commands_{user_id}",
                pattern_type="command_usage",
                frequency=sum(command_counts.values()),
                confidence=0.9,
                description=f"Most used commands: {', '.join([cmd for cmd, _ in top_commands])}",
                suggestions=[f"Consider creating shortcuts for: {', '.join([cmd for cmd, _ in top_commands[:3]])}"],
                last_seen=time.time()
            )
            self.detected_patterns[pattern.pattern_id] = pattern

    async def _analyze_efficiency_patterns(self, user_id: str, actions: List[UserAction]):
        """Analyze efficiency and performance patterns."""
        if not actions:
            return

        success_rate = sum(a.success for a in actions) / len(actions)
        avg_execution_time = np.mean([a.execution_time for a in actions])

        # Identify slow commands
        slow_commands = [a for a in actions if a.execution_time > avg_execution_time * 1.5]

        if slow_commands:
            slow_command_types = defaultdict(list)
            for cmd in slow_commands:
                slow_command_types[cmd.intent].append(cmd.execution_time)

            suggestions = []
            for cmd_type, times in slow_command_types.items():
                avg_time = np.mean(times)
                suggestions.append(f"Optimize {cmd_type} commands (avg: {avg_time:.2f}s)")

            pattern = BehaviorPattern(
                pattern_id=f"efficiency_{user_id}",
                pattern_type="efficiency",
                frequency=len(slow_commands),
                confidence=0.7,
                description=f"Performance analysis: {success_rate:.1%} success rate, {avg_execution_time:.2f}s avg time",
                suggestions=suggestions,
                last_seen=time.time()
            )
            self.detected_patterns[pattern.pattern_id] = pattern

    def _extract_command_sequences(self, actions: List[UserAction]) -> List[List[str]]:
        """Extract command sequences from actions."""
        sequences = []
        current_sequence = []

        for i, action in enumerate(actions):
            current_sequence.append(action.intent)

            # End sequence if there's a time gap or different context
            if (i < len(actions) - 1 and
                actions[i + 1].timestamp - action.timestamp > 300):  # 5 minutes
                if len(current_sequence) >= 2:
                    sequences.append(current_sequence.copy())
                current_sequence = []

        if len(current_sequence) >= 2:
            sequences.append(current_sequence)

        return sequences

    def _analyze_time_patterns(self, actions: List[UserAction]) -> Dict[str, Any]:
        """Analyze temporal patterns in actions."""
        if not actions:
            return {}

        hours = [datetime.fromtimestamp(a.timestamp).hour for a in actions]
        days = [datetime.fromtimestamp(a.timestamp).weekday() for a in actions]

        return {
            "peak_hours": self._find_peak_hours(hours),
            "peak_days": self._find_peak_days(days),
            "session_length": self._calculate_session_length(actions)
        }

    def _analyze_context_patterns(self, actions: List[UserAction], current_context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze context-based patterns."""
        if not current_context:
            return {}

        similar_contexts = []
        for action in actions:
            similarity = self._calculate_context_similarity(action.context, current_context)
            if similarity > 0.7:
                similar_contexts.append(action)

        return {
            "similar_actions": len(similar_contexts),
            "common_next_actions": self._get_common_next_actions(similar_contexts)
        }

    def _generate_prediction(self, sequences: List[List[str]], time_patterns: Dict[str, Any],
                           context_patterns: Dict[str, Any]) -> Dict[str, Any]:
        """Generate action prediction based on patterns."""
        predictions = []

        # Sequence-based prediction
        if sequences:
            last_commands = sequences[-1][-3:]  # Last 3 commands
            for seq in sequences:
                if seq[:-1] == last_commands:
                    predictions.append({
                        "action": seq[-1],
                        "confidence": 0.8,
                        "reason": "command_sequence"
                    })

        # Time-based prediction
        current_hour = datetime.now().hour
        if current_hour in time_patterns.get("peak_hours", []):
            predictions.append({
                "action": "high_activity_period",
                "confidence": 0.6,
                "reason": "temporal_pattern"
            })

        # Context-based prediction
        if context_patterns.get("common_next_actions"):
            for action, freq in context_patterns["common_next_actions"].items():
                predictions.append({
                    "action": action,
                    "confidence": min(0.9, freq / 10),
                    "reason": "context_pattern"
                })

        # Return best prediction
        if predictions:
            best = max(predictions, key=lambda x: x["confidence"])
            return best

        return {"action": "unknown", "confidence": 0.0, "reason": "insufficient_data"}

    def _find_peak_hours(self, hours: List[int]) -> List[int]:
        """Find peak activity hours."""
        hour_counts = defaultdict(int)
        for hour in hours:
            hour_counts[hour] += 1

        if not hour_counts:
            return []

        avg_count = sum(hour_counts.values()) / len(hour_counts)
        return [hour for hour, count in hour_counts.items() if count > avg_count]

    def _find_peak_days(self, days: List[int]) -> List[int]:
        """Find peak activity days."""
        day_counts = defaultdict(int)
        for day in days:
            day_counts[day] += 1

        if not day_counts:
            return []

        avg_count = sum(day_counts.values()) / len(day_counts)
        return [day for day, count in day_counts.items() if count > avg_count]

    def _calculate_session_length(self, actions: List[UserAction]) -> float:
        """Calculate average session length."""
        if len(actions) < 2:
            return 0.0

        sessions = []
        session_start = actions[0].timestamp

        for i in range(1, len(actions)):
            if actions[i].timestamp - actions[i-1].timestamp > 1800:  # 30 minutes gap
                sessions.append(actions[i-1].timestamp - session_start)
                session_start = actions[i].timestamp

        # Add final session
        sessions.append(actions[-1].timestamp - session_start)

        return np.mean(sessions) if sessions else 0.0

    def _calculate_context_similarity(self, context1: Dict[str, Any], context2: Dict[str, Any]) -> float:
        """Calculate similarity between two contexts."""
        if not context1 or not context2:
            return 0.0

        common_keys = set(context1.keys()) & set(context2.keys())
        if not common_keys:
            return 0.0

        matches = 0
        for key in common_keys:
            if context1[key] == context2[key]:
                matches += 1

        return matches / len(common_keys)

    def _get_common_next_actions(self, actions: List[UserAction]) -> Dict[str, int]:
        """Get common next actions from similar contexts."""
        next_actions = defaultdict(int)

        for i in range(len(actions) - 1):
            next_action = actions[i + 1].intent
            next_actions[next_action] += 1

        return dict(next_actions)

    def _get_top_commands(self, actions: List[UserAction], limit: int) -> List[Tuple[str, int]]:
        """Get top commands by frequency."""
        command_counts = defaultdict(int)
        for action in actions:
            command_counts[action.intent] += 1

        return sorted(command_counts.items(), key=lambda x: x[1], reverse=True)[:limit]

    async def _generate_recommendations(self, user_id: str) -> List[str]:
        """Generate personalized recommendations."""
        profile = self.user_profiles.get(user_id)
        if not profile:
            return []

        recommendations = []

        # Success rate recommendations
        if profile.success_rate < 0.8:
            recommendations.append("Consider using more specific voice commands for better recognition")

        # Efficiency recommendations
        if profile.efficiency_score < 0.7:
            recommendations.append("Try using keyboard shortcuts for frequently used commands")

        # Usage pattern recommendations
        if len(profile.preferred_times) > 0:
            peak_hour = max(profile.preferred_times, key=lambda h: profile.common_commands.get(str(h), 0))
            recommendations.append(f"Your peak productivity time is around {peak_hour}:00")

        # Command-specific recommendations
        top_commands = sorted(profile.common_commands.items(), key=lambda x: x[1], reverse=True)[:3]
        for command, count in top_commands:
            if count > 10:
                recommendations.append(f"Consider creating a shortcut for '{command}' (used {count} times)")

        return recommendations

    async def _calculate_efficiency_trends(self, user_id: str) -> Dict[str, Any]:
        """Calculate efficiency trends over time."""
        user_actions = [a for a in self.recent_actions if a.user_id == user_id]

        if len(user_actions) < 10:
            return {"trend": "insufficient_data"}

        # Split into time periods
        now = time.time()
        recent = [a for a in user_actions if now - a.timestamp < 86400]  # Last 24 hours
        older = [a for a in user_actions if 86400 <= now - a.timestamp < 172800]  # 24-48 hours ago

        if not recent or not older:
            return {"trend": "insufficient_data"}

        recent_success = sum(a.success for a in recent) / len(recent)
        older_success = sum(a.success for a in older) / len(older)

        recent_speed = np.mean([a.execution_time for a in recent])
        older_speed = np.mean([a.execution_time for a in older])

        return {
            "success_rate_trend": "improving" if recent_success > older_success else "declining",
            "speed_trend": "improving" if recent_speed < older_speed else "declining",
            "recent_success_rate": recent_success,
            "speed_improvement": (older_speed - recent_speed) / older_speed if older_speed > 0 else 0
        }

    async def _identify_learning_opportunities(self, user_id: str) -> List[str]:
        """Identify learning opportunities for the user."""
        profile = self.user_profiles.get(user_id)
        if not profile:
            return []

        opportunities = []

        # Unused features
        all_intents = {"file_management", "application_control", "system_info", "automation", "analytics"}
        used_intents = set(profile.common_commands.keys())
        unused = all_intents - used_intents

        for intent in unused:
            opportunities.append(f"Learn {intent.replace('_', ' ')} commands to boost productivity")

        # Advanced features
        if profile.total_commands > 50 and "workflow" not in used_intents:
            opportunities.append("Try creating custom workflows for repeated tasks")

        if profile.total_commands > 100 and "automation" not in used_intents:
            opportunities.append("Explore automation features to save time")

        return opportunities

    def _recommend_voice_sensitivity(self, profile: UserProfile) -> float:
        """Recommend optimal voice sensitivity."""
        if profile.success_rate > 0.9:
            return 0.8  # Higher sensitivity for accurate users
        elif profile.success_rate > 0.7:
            return 0.7  # Default sensitivity
        else:
            return 0.6  # Lower sensitivity for users with recognition issues

    def _recommend_response_speed(self, profile: UserProfile) -> str:
        """Recommend optimal response speed."""
        if profile.efficiency_score > 0.8:
            return "fast"
        elif profile.efficiency_score > 0.6:
            return "normal"
        else:
            return "slow"

    def _recommend_confirmation_level(self, profile: UserProfile) -> str:
        """Recommend confirmation level."""
        if profile.success_rate > 0.95 and profile.total_commands > 100:
            return "minimal"
        elif profile.success_rate > 0.8:
            return "normal"
        else:
            return "high"

    def _recommend_shortcuts(self, profile: UserProfile) -> List[str]:
        """Recommend custom shortcuts."""
        shortcuts = []
        top_commands = sorted(profile.common_commands.items(), key=lambda x: x[1], reverse=True)[:5]

        for command, count in top_commands:
            if count > 20:
                shortcuts.append(f"Create shortcut for '{command}'")

        return shortcuts

    def _recommend_break_times(self, profile: UserProfile) -> List[str]:
        """Recommend optimal break times."""
        if not profile.preferred_times:
            return ["Take breaks every 2 hours"]

        # Find gaps in preferred times
        sorted_times = sorted(profile.preferred_times)
        breaks = []

        for i in range(len(sorted_times) - 1):
            gap = sorted_times[i + 1] - sorted_times[i]
            if gap > 2:  # 2+ hour gap
                break_time = sorted_times[i] + gap // 2
                breaks.append(f"Consider a break around {break_time}:00")

        return breaks if breaks else ["Take breaks every 2 hours"]

    def _generate_productivity_tips(self, profile: UserProfile) -> List[str]:
        """Generate productivity tips."""
        tips = []

        if profile.efficiency_score < 0.7:
            tips.append("Practice common commands to improve speed")

        if profile.success_rate < 0.8:
            tips.append("Speak clearly and use consistent phrasing")

        if len(profile.common_commands) < 5:
            tips.append("Explore more voice commands to increase productivity")

        tips.append("Use voice commands during your peak hours for best results")

        return tips

    async def _update_efficiency_score(self, user_id: str):
        """Update user efficiency score."""
        profile = self.user_profiles.get(user_id)
        if not profile:
            return

        user_actions = [a for a in self.recent_actions if a.user_id == user_id]
        if not user_actions:
            return

        # Calculate efficiency based on success rate and speed
        success_rate = sum(a.success for a in user_actions) / len(user_actions)
        avg_time = np.mean([a.execution_time for a in user_actions])

        # Normalize speed (lower is better)
        speed_score = max(0, 1 - (avg_time - 1) / 10)  # Assume 1s is ideal, 10s+ is poor

        # Combine metrics
        profile.efficiency_score = (success_rate * 0.7 + speed_score * 0.3)

    async def _update_learning_progress(self, user_id: str):
        """Update user learning progress."""
        profile = self.user_profiles.get(user_id)
        if not profile:
            return

        # Calculate progress based on command diversity and usage patterns
        command_diversity = len(profile.common_commands) / 20  # Assume 20 is max diversity
        usage_consistency = min(1.0, profile.total_commands / 100)  # 100 commands = consistent user

        profile.learning_progress = min(1.0, (command_diversity * 0.6 + usage_consistency * 0.4))

    async def _load_user_profiles(self):
        """Load user profiles from database."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT * FROM user_profiles")
            for row in cursor.fetchall():
                profile = UserProfile(
                    user_id=row[0],
                    created_at=row[1],
                    last_active=row[2],
                    total_commands=row[3],
                    success_rate=row[4],
                    preferred_times=json.loads(row[5]),
                    common_commands=json.loads(row[6]),
                    efficiency_score=row[7],
                    learning_progress=row[8]
                )
                self.user_profiles[profile.user_id] = profile

    async def _load_behavior_patterns(self):
        """Load behavior patterns from database."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT * FROM behavior_patterns")
            for row in cursor.fetchall():
                pattern = BehaviorPattern(
                    pattern_id=row[0],
                    pattern_type=row[2],
                    frequency=row[3],
                    confidence=row[4],
                    description=row[5],
                    suggestions=json.loads(row[6]),
                    last_seen=row[7]
                )
                self.detected_patterns[pattern.pattern_id] = pattern

    async def _save_user_profiles(self):
        """Save user profiles to database."""
        with sqlite3.connect(self.db_path) as conn:
            for profile in self.user_profiles.values():
                conn.execute("""
                    INSERT OR REPLACE INTO user_profiles
                    (user_id, created_at, last_active, total_commands, success_rate,
                     preferred_times, common_commands, efficiency_score, learning_progress)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    profile.user_id,
                    profile.created_at,
                    profile.last_active,
                    profile.total_commands,
                    profile.success_rate,
                    json.dumps(profile.preferred_times),
                    json.dumps(profile.common_commands),
                    profile.efficiency_score,
                    profile.learning_progress
                ))

    async def _save_behavior_patterns(self):
        """Save behavior patterns to database."""
        with sqlite3.connect(self.db_path) as conn:
            for pattern in self.detected_patterns.values():
                conn.execute("""
                    INSERT OR REPLACE INTO behavior_patterns
                    (pattern_id, user_id, pattern_type, frequency, confidence,
                     description, suggestions, last_seen, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    pattern.pattern_id,
                    "default",  # For now, all patterns are for default user
                    pattern.pattern_type,
                    pattern.frequency,
                    pattern.confidence,
                    pattern.description,
                    json.dumps(pattern.suggestions),
                    pattern.last_seen,
                    time.time()
                ))

    async def _save_all_data(self):
        """Save all data to database."""
        await self._save_user_profiles()
        await self._save_behavior_patterns()

    async def _cleanup_old_data(self):
        """Clean up old data beyond retention period."""
        cutoff_time = time.time() - (self.max_history_days * 86400)

        with sqlite3.connect(self.db_path) as conn:
            # Clean old actions
            conn.execute("DELETE FROM user_actions WHERE timestamp < ?", (cutoff_time,))

            # Clean old patterns
            conn.execute("DELETE FROM behavior_patterns WHERE last_seen < ?", (cutoff_time,))

        # Clean in-memory caches
        self.recent_actions = deque([a for a in self.recent_actions
                                   if a.timestamp >= cutoff_time], maxlen=1000)

        # Clean prediction cache
        current_time = time.time()
        expired_keys = [k for k, v in self.prediction_cache.items()
                       if current_time - v["timestamp"] > 3600]  # 1 hour
        for key in expired_keys:
            del self.prediction_cache[key]


# Utility functions for external use
async def create_user_action(command: str, intent: str, success: bool,
                           execution_time: float, context: Dict[str, Any] = None,
                           user_id: str = "default") -> UserAction:
    """Create a UserAction object for recording."""
    return UserAction(
        timestamp=time.time(),
        command=command,
        intent=intent,
        success=success,
        execution_time=execution_time,
        context=context or {},
        user_id=user_id
    )
