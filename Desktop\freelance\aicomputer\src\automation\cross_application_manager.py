"""
Cross-Application Integration Manager
Phase 3 - Advanced Automation

This module provides seamless integration and data flow between different applications,
enabling complex cross-application workflows and automation.
"""

import asyncio
import json
import time
import subprocess
import psutil
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from pathlib import Path
import win32gui
import win32con
import win32clipboard
import pyautogui
from enum import Enum

from ..utils.config_manager import ConfigManager


class ApplicationState(Enum):
    """Application states for tracking."""
    UNKNOWN = "unknown"
    RUNNING = "running"
    IDLE = "idle"
    BUSY = "busy"
    ERROR = "error"


class DataFormat(Enum):
    """Supported data formats for cross-application transfer."""
    TEXT = "text"
    JSON = "json"
    CSV = "csv"
    XML = "xml"
    IMAGE = "image"
    FILE_PATH = "file_path"
    CLIPBOARD = "clipboard"


@dataclass
class ApplicationInfo:
    """Information about a registered application."""
    name: str
    process_name: str
    window_title: str
    pid: Optional[int]
    state: ApplicationState
    capabilities: List[str]
    data_formats: List[DataFormat]
    last_interaction: float


@dataclass
class DataTransfer:
    """Represents a data transfer between applications."""
    transfer_id: str
    source_app: str
    target_app: str
    data_format: DataFormat
    data: Any
    timestamp: float
    status: str
    metadata: Dict[str, Any]


@dataclass
class WorkflowStep:
    """Represents a step in a cross-application workflow."""
    step_id: str
    app_name: str
    action: str
    parameters: Dict[str, Any]
    input_data: Optional[Any]
    output_data: Optional[Any]
    dependencies: List[str]
    timeout: float


class CrossApplicationManager:
    """
    Manages integration and data flow between different applications,
    enabling complex cross-application workflows and automation.
    """
    
    def __init__(self, config: ConfigManager):
        self.config = config
        self.applications = {}
        self.active_transfers = {}
        self.workflow_templates = {}
        self.data_converters = {}
        
        # Configuration
        self.scan_interval = config.get("cross_app.scan_interval", 5)
        self.transfer_timeout = config.get("cross_app.transfer_timeout", 30)
        self.max_retries = config.get("cross_app.max_retries", 3)
        
        # State
        self.is_running = False
        self.monitoring_task = None
        
        # Initialize built-in applications
        self._register_builtin_applications()
        self._register_data_converters()
    
    async def start(self):
        """Start the cross-application manager."""
        self.is_running = True
        self.monitoring_task = asyncio.create_task(self._monitor_applications())
        await self._discover_applications()
    
    async def stop(self):
        """Stop the cross-application manager."""
        self.is_running = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
    
    async def register_application(self, app_info: ApplicationInfo):
        """Register a new application for integration."""
        self.applications[app_info.name] = app_info
        
        # Try to find the application process
        await self._update_application_state(app_info.name)
    
    async def transfer_data(self, source_app: str, target_app: str, 
                          data: Any, data_format: DataFormat,
                          metadata: Dict[str, Any] = None) -> str:
        """Transfer data between applications."""
        transfer_id = f"transfer_{int(time.time() * 1000)}"
        
        transfer = DataTransfer(
            transfer_id=transfer_id,
            source_app=source_app,
            target_app=target_app,
            data_format=data_format,
            data=data,
            timestamp=time.time(),
            status="pending",
            metadata=metadata or {}
        )
        
        self.active_transfers[transfer_id] = transfer
        
        try:
            # Validate applications
            if source_app not in self.applications or target_app not in self.applications:
                raise ValueError("Source or target application not registered")
            
            # Convert data if needed
            converted_data = await self._convert_data(data, data_format, 
                                                    self.applications[source_app].data_formats,
                                                    self.applications[target_app].data_formats)
            
            # Perform the transfer
            await self._execute_transfer(transfer, converted_data)
            
            transfer.status = "completed"
            return transfer_id
            
        except Exception as e:
            transfer.status = f"failed: {str(e)}"
            raise
    
    async def execute_workflow(self, workflow_name: str, input_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute a predefined cross-application workflow."""
        if workflow_name not in self.workflow_templates:
            raise ValueError(f"Workflow '{workflow_name}' not found")
        
        workflow = self.workflow_templates[workflow_name]
        results = {}
        
        # Sort steps by dependencies
        sorted_steps = self._sort_workflow_steps(workflow["steps"])
        
        for step in sorted_steps:
            try:
                # Prepare input data
                step_input = input_data.copy() if input_data else {}
                
                # Add outputs from previous steps
                for dep in step.dependencies:
                    if dep in results:
                        step_input[dep] = results[dep]
                
                # Execute step
                result = await self._execute_workflow_step(step, step_input)
                results[step.step_id] = result
                
            except Exception as e:
                results[step.step_id] = {"error": str(e)}
                if workflow.get("stop_on_error", True):
                    break
        
        return results
    
    async def create_workflow_template(self, name: str, steps: List[WorkflowStep], 
                                     description: str = "", stop_on_error: bool = True):
        """Create a new workflow template."""
        self.workflow_templates[name] = {
            "name": name,
            "description": description,
            "steps": steps,
            "stop_on_error": stop_on_error,
            "created_at": time.time()
        }
    
    async def get_application_data(self, app_name: str, data_type: str) -> Any:
        """Get data from a specific application."""
        if app_name not in self.applications:
            raise ValueError(f"Application '{app_name}' not registered")
        
        app = self.applications[app_name]
        
        if data_type == "clipboard":
            return await self._get_clipboard_data()
        elif data_type == "window_text":
            return await self._get_window_text(app.pid)
        elif data_type == "file_content":
            return await self._get_active_file_content(app_name)
        else:
            raise ValueError(f"Unsupported data type: {data_type}")
    
    async def send_data_to_application(self, app_name: str, data: Any, action: str):
        """Send data to a specific application."""
        if app_name not in self.applications:
            raise ValueError(f"Application '{app_name}' not registered")
        
        app = self.applications[app_name]
        
        # Focus the application window
        await self._focus_application(app_name)
        
        if action == "paste":
            await self._set_clipboard_data(data)
            pyautogui.hotkey('ctrl', 'v')
        elif action == "type":
            pyautogui.write(str(data))
        elif action == "hotkey":
            if isinstance(data, list):
                pyautogui.hotkey(*data)
            else:
                pyautogui.press(data)
        else:
            raise ValueError(f"Unsupported action: {action}")
    
    async def get_available_applications(self) -> List[Dict[str, Any]]:
        """Get list of available applications."""
        return [
            {
                "name": app.name,
                "state": app.state.value,
                "capabilities": app.capabilities,
                "data_formats": [fmt.value for fmt in app.data_formats],
                "last_interaction": app.last_interaction
            }
            for app in self.applications.values()
        ]
    
    async def get_workflow_templates(self) -> List[Dict[str, Any]]:
        """Get list of available workflow templates."""
        return [
            {
                "name": name,
                "description": template["description"],
                "steps": len(template["steps"]),
                "created_at": template["created_at"]
            }
            for name, template in self.workflow_templates.items()
        ]
    
    def _register_builtin_applications(self):
        """Register built-in application integrations."""
        builtin_apps = [
            ApplicationInfo(
                name="notepad",
                process_name="notepad.exe",
                window_title="Notepad",
                pid=None,
                state=ApplicationState.UNKNOWN,
                capabilities=["text_editing", "file_operations"],
                data_formats=[DataFormat.TEXT, DataFormat.CLIPBOARD],
                last_interaction=0
            ),
            ApplicationInfo(
                name="calculator",
                process_name="calc.exe",
                window_title="Calculator",
                pid=None,
                state=ApplicationState.UNKNOWN,
                capabilities=["calculations"],
                data_formats=[DataFormat.TEXT, DataFormat.CLIPBOARD],
                last_interaction=0
            ),
            ApplicationInfo(
                name="explorer",
                process_name="explorer.exe",
                window_title="File Explorer",
                pid=None,
                state=ApplicationState.UNKNOWN,
                capabilities=["file_management", "navigation"],
                data_formats=[DataFormat.FILE_PATH, DataFormat.TEXT],
                last_interaction=0
            ),
            ApplicationInfo(
                name="chrome",
                process_name="chrome.exe",
                window_title="Google Chrome",
                pid=None,
                state=ApplicationState.UNKNOWN,
                capabilities=["web_browsing", "data_extraction"],
                data_formats=[DataFormat.TEXT, DataFormat.JSON, DataFormat.CLIPBOARD],
                last_interaction=0
            ),
            ApplicationInfo(
                name="excel",
                process_name="excel.exe",
                window_title="Microsoft Excel",
                pid=None,
                state=ApplicationState.UNKNOWN,
                capabilities=["spreadsheet", "data_analysis", "calculations"],
                data_formats=[DataFormat.CSV, DataFormat.JSON, DataFormat.TEXT],
                last_interaction=0
            ),
            ApplicationInfo(
                name="word",
                process_name="winword.exe",
                window_title="Microsoft Word",
                pid=None,
                state=ApplicationState.UNKNOWN,
                capabilities=["document_editing", "formatting"],
                data_formats=[DataFormat.TEXT, DataFormat.XML],
                last_interaction=0
            )
        ]
        
        for app in builtin_apps:
            self.applications[app.name] = app

    def _register_data_converters(self):
        """Register data format converters."""
        self.data_converters = {
            (DataFormat.TEXT, DataFormat.JSON): self._text_to_json,
            (DataFormat.JSON, DataFormat.TEXT): self._json_to_text,
            (DataFormat.CSV, DataFormat.JSON): self._csv_to_json,
            (DataFormat.JSON, DataFormat.CSV): self._json_to_csv,
            (DataFormat.TEXT, DataFormat.CSV): self._text_to_csv,
            (DataFormat.CSV, DataFormat.TEXT): self._csv_to_text,
        }

    async def _monitor_applications(self):
        """Monitor application states continuously."""
        while self.is_running:
            try:
                for app_name in self.applications.keys():
                    await self._update_application_state(app_name)

                await asyncio.sleep(self.scan_interval)

            except Exception as e:
                print(f"Error monitoring applications: {e}")
                await asyncio.sleep(self.scan_interval)

    async def _discover_applications(self):
        """Discover running applications."""
        for process in psutil.process_iter(['pid', 'name']):
            try:
                process_name = process.info['name']

                for app in self.applications.values():
                    if app.process_name.lower() == process_name.lower():
                        app.pid = process.info['pid']
                        app.state = ApplicationState.RUNNING

            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

    async def _update_application_state(self, app_name: str):
        """Update the state of a specific application."""
        app = self.applications[app_name]

        try:
            if app.pid:
                process = psutil.Process(app.pid)
                if process.is_running():
                    # Check if process is busy
                    cpu_percent = process.cpu_percent()
                    if cpu_percent > 10:
                        app.state = ApplicationState.BUSY
                    else:
                        app.state = ApplicationState.IDLE
                else:
                    app.state = ApplicationState.UNKNOWN
                    app.pid = None
            else:
                # Try to find the process
                for process in psutil.process_iter(['pid', 'name']):
                    try:
                        if process.info['name'].lower() == app.process_name.lower():
                            app.pid = process.info['pid']
                            app.state = ApplicationState.RUNNING
                            break
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
                else:
                    app.state = ApplicationState.UNKNOWN

        except Exception as e:
            app.state = ApplicationState.ERROR

    async def _convert_data(self, data: Any, source_format: DataFormat,
                          source_formats: List[DataFormat], target_formats: List[DataFormat]) -> Any:
        """Convert data between formats if needed."""
        # Find compatible format
        compatible_formats = set(source_formats) & set(target_formats)

        if source_format in compatible_formats:
            return data  # No conversion needed

        # Find conversion path
        for target_format in target_formats:
            converter_key = (source_format, target_format)
            if converter_key in self.data_converters:
                return await self.data_converters[converter_key](data)

        # No direct conversion available
        raise ValueError(f"Cannot convert from {source_format} to any of {target_formats}")

    async def _execute_transfer(self, transfer: DataTransfer, data: Any):
        """Execute the actual data transfer."""
        source_app = self.applications[transfer.source_app]
        target_app = self.applications[transfer.target_app]

        # Ensure both applications are available
        if source_app.state == ApplicationState.UNKNOWN:
            raise RuntimeError(f"Source application '{transfer.source_app}' not available")

        if target_app.state == ApplicationState.UNKNOWN:
            raise RuntimeError(f"Target application '{transfer.target_app}' not available")

        # Focus target application
        await self._focus_application(transfer.target_app)

        # Transfer data based on format
        if transfer.data_format == DataFormat.CLIPBOARD:
            await self._set_clipboard_data(data)
            pyautogui.hotkey('ctrl', 'v')
        elif transfer.data_format == DataFormat.TEXT:
            pyautogui.write(str(data))
        elif transfer.data_format == DataFormat.FILE_PATH:
            # Open file in target application
            await self._open_file_in_app(transfer.target_app, str(data))
        else:
            # Use clipboard as fallback
            await self._set_clipboard_data(str(data))
            pyautogui.hotkey('ctrl', 'v')

        # Update interaction time
        target_app.last_interaction = time.time()

    async def _execute_workflow_step(self, step: WorkflowStep, input_data: Dict[str, Any]) -> Any:
        """Execute a single workflow step."""
        app_name = step.app_name

        if app_name not in self.applications:
            raise ValueError(f"Application '{app_name}' not registered")

        # Focus application
        await self._focus_application(app_name)

        # Execute action based on type
        if step.action == "send_data":
            data = input_data.get("data", step.parameters.get("data"))
            action = step.parameters.get("action", "paste")
            await self.send_data_to_application(app_name, data, action)
            return {"status": "data_sent"}

        elif step.action == "get_data":
            data_type = step.parameters.get("data_type", "clipboard")
            result = await self.get_application_data(app_name, data_type)
            return {"data": result}

        elif step.action == "hotkey":
            keys = step.parameters.get("keys", [])
            pyautogui.hotkey(*keys)
            return {"status": "hotkey_sent"}

        elif step.action == "wait":
            wait_time = step.parameters.get("time", 1)
            await asyncio.sleep(wait_time)
            return {"status": "waited"}

        elif step.action == "click":
            x = step.parameters.get("x")
            y = step.parameters.get("y")
            if x and y:
                pyautogui.click(x, y)
            return {"status": "clicked"}

        else:
            raise ValueError(f"Unknown action: {step.action}")

    def _sort_workflow_steps(self, steps: List[WorkflowStep]) -> List[WorkflowStep]:
        """Sort workflow steps by dependencies."""
        sorted_steps = []
        remaining_steps = steps.copy()

        while remaining_steps:
            # Find steps with no unresolved dependencies
            ready_steps = []
            for step in remaining_steps:
                if all(dep in [s.step_id for s in sorted_steps] for dep in step.dependencies):
                    ready_steps.append(step)

            if not ready_steps:
                # Circular dependency or missing dependency
                raise ValueError("Circular dependency or missing dependency in workflow")

            # Add ready steps to sorted list
            sorted_steps.extend(ready_steps)

            # Remove from remaining
            for step in ready_steps:
                remaining_steps.remove(step)

        return sorted_steps

    async def _focus_application(self, app_name: str):
        """Focus a specific application window."""
        app = self.applications[app_name]

        if not app.pid:
            raise RuntimeError(f"Application '{app_name}' not running")

        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                _, pid = win32gui.GetWindowThreadProcessId(hwnd)
                if pid == app.pid:
                    windows.append(hwnd)
            return True

        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)

        if windows:
            # Focus the first window found
            hwnd = windows[0]
            win32gui.SetForegroundWindow(hwnd)
            win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)

    async def _get_clipboard_data(self) -> str:
        """Get data from clipboard."""
        try:
            win32clipboard.OpenClipboard()
            data = win32clipboard.GetClipboardData()
            win32clipboard.CloseClipboard()
            return data
        except Exception:
            return ""

    async def _set_clipboard_data(self, data: str):
        """Set data to clipboard."""
        try:
            win32clipboard.OpenClipboard()
            win32clipboard.EmptyClipboard()
            win32clipboard.SetClipboardText(str(data))
            win32clipboard.CloseClipboard()
        except Exception as e:
            print(f"Error setting clipboard data: {e}")

    async def _get_window_text(self, pid: int) -> str:
        """Get text from application window."""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                _, window_pid = win32gui.GetWindowThreadProcessId(hwnd)
                if window_pid == pid:
                    windows.append(hwnd)
            return True

        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)

        if windows:
            hwnd = windows[0]
            return win32gui.GetWindowText(hwnd)

        return ""

    async def _get_active_file_content(self, app_name: str) -> str:
        """Get content of active file in application."""
        # This is a simplified implementation
        # In practice, this would need app-specific logic

        # Try to select all and copy
        pyautogui.hotkey('ctrl', 'a')
        await asyncio.sleep(0.1)
        pyautogui.hotkey('ctrl', 'c')
        await asyncio.sleep(0.1)

        return await self._get_clipboard_data()

    async def _open_file_in_app(self, app_name: str, file_path: str):
        """Open a file in the specified application."""
        # Focus application first
        await self._focus_application(app_name)

        # Use Ctrl+O to open file dialog
        pyautogui.hotkey('ctrl', 'o')
        await asyncio.sleep(1)  # Wait for dialog

        # Type file path
        pyautogui.write(file_path)
        await asyncio.sleep(0.5)

        # Press Enter to open
        pyautogui.press('enter')

    # Data converter methods
    async def _text_to_json(self, text: str) -> str:
        """Convert text to JSON format."""
        try:
            # Try to parse as JSON first
            json.loads(text)
            return text
        except json.JSONDecodeError:
            # Wrap in JSON object
            return json.dumps({"text": text})

    async def _json_to_text(self, json_data: str) -> str:
        """Convert JSON to text format."""
        try:
            data = json.loads(json_data)
            if isinstance(data, dict) and "text" in data:
                return data["text"]
            return json.dumps(data, indent=2)
        except json.JSONDecodeError:
            return json_data

    async def _csv_to_json(self, csv_data: str) -> str:
        """Convert CSV to JSON format."""
        lines = csv_data.strip().split('\n')
        if not lines:
            return "[]"

        headers = [h.strip() for h in lines[0].split(',')]
        rows = []

        for line in lines[1:]:
            values = [v.strip() for v in line.split(',')]
            if len(values) == len(headers):
                row = dict(zip(headers, values))
                rows.append(row)

        return json.dumps(rows, indent=2)

    async def _json_to_csv(self, json_data: str) -> str:
        """Convert JSON to CSV format."""
        try:
            data = json.loads(json_data)
            if not isinstance(data, list) or not data:
                return ""

            # Get headers from first object
            headers = list(data[0].keys())
            csv_lines = [','.join(headers)]

            for item in data:
                values = [str(item.get(h, '')) for h in headers]
                csv_lines.append(','.join(values))

            return '\n'.join(csv_lines)

        except (json.JSONDecodeError, KeyError, AttributeError):
            return json_data

    async def _text_to_csv(self, text: str) -> str:
        """Convert text to CSV format."""
        lines = text.strip().split('\n')
        csv_lines = []

        for line in lines:
            # Simple conversion - split by common delimiters
            if '\t' in line:
                values = line.split('\t')
            elif '|' in line:
                values = line.split('|')
            else:
                values = [line]

            csv_lines.append(','.join(v.strip() for v in values))

        return '\n'.join(csv_lines)

    async def _csv_to_text(self, csv_data: str) -> str:
        """Convert CSV to text format."""
        lines = csv_data.strip().split('\n')
        text_lines = []

        for line in lines:
            values = [v.strip() for v in line.split(',')]
            text_lines.append(' | '.join(values))

        return '\n'.join(text_lines)


# Utility functions for creating workflow steps
def create_workflow_step(step_id: str, app_name: str, action: str,
                        parameters: Dict[str, Any] = None,
                        dependencies: List[str] = None,
                        timeout: float = 30.0) -> WorkflowStep:
    """Create a workflow step."""
    return WorkflowStep(
        step_id=step_id,
        app_name=app_name,
        action=action,
        parameters=parameters or {},
        input_data=None,
        output_data=None,
        dependencies=dependencies or [],
        timeout=timeout
    )


# Predefined workflow templates
BUILTIN_WORKFLOWS = {
    "text_to_excel": [
        create_workflow_step("copy_text", "notepad", "get_data", {"data_type": "clipboard"}),
        create_workflow_step("open_excel", "excel", "hotkey", {"keys": ["ctrl", "n"]}),
        create_workflow_step("paste_data", "excel", "send_data", {"action": "paste"}, ["copy_text"])
    ],
    "excel_to_word": [
        create_workflow_step("copy_excel", "excel", "hotkey", {"keys": ["ctrl", "a"]}),
        create_workflow_step("copy_data", "excel", "hotkey", {"keys": ["ctrl", "c"]}),
        create_workflow_step("open_word", "word", "hotkey", {"keys": ["ctrl", "n"]}),
        create_workflow_step("paste_table", "word", "send_data", {"action": "paste"}, ["copy_data"])
    ],
    "web_to_notepad": [
        create_workflow_step("select_web", "chrome", "hotkey", {"keys": ["ctrl", "a"]}),
        create_workflow_step("copy_web", "chrome", "hotkey", {"keys": ["ctrl", "c"]}),
        create_workflow_step("open_notepad", "notepad", "hotkey", {"keys": ["ctrl", "n"]}),
        create_workflow_step("paste_text", "notepad", "send_data", {"action": "paste"}, ["copy_web"])
    ]
}
