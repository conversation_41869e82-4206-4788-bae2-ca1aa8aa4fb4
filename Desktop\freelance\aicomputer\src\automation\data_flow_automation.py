"""
Data Flow Automation System
Phase 3 - Advanced Automation

This module provides automated data flow management, transformation pipelines,
and intelligent data routing between different system components.
"""

import asyncio
import json
import time
import uuid
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import pandas as pd
import numpy as np

from ..utils.config_manager import ConfigManager


class DataType(Enum):
    """Supported data types in the flow system."""
    TEXT = "text"
    JSON = "json"
    CSV = "csv"
    XML = "xml"
    BINARY = "binary"
    IMAGE = "image"
    AUDIO = "audio"
    VIDEO = "video"
    STRUCTURED = "structured"
    UNSTRUCTURED = "unstructured"


class FlowStatus(Enum):
    """Data flow execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"
    CANCELLED = "cancelled"


class TransformationType(Enum):
    """Types of data transformations."""
    FILTER = "filter"
    MAP = "map"
    REDUCE = "reduce"
    AGGREGATE = "aggregate"
    VALIDATE = "validate"
    CONVERT = "convert"
    ENRICH = "enrich"
    CLEAN = "clean"
    NORMALIZE = "normalize"
    CUSTOM = "custom"


@dataclass
class DataPacket:
    """Represents a data packet in the flow system."""
    packet_id: str
    data: Any
    data_type: DataType
    metadata: Dict[str, Any]
    timestamp: float
    source: str
    destination: Optional[str]
    transformations: List[str]


@dataclass
class FlowNode:
    """Represents a node in the data flow pipeline."""
    node_id: str
    name: str
    node_type: str
    input_types: List[DataType]
    output_types: List[DataType]
    configuration: Dict[str, Any]
    processor: Optional[Callable]
    is_active: bool


@dataclass
class FlowConnection:
    """Represents a connection between flow nodes."""
    connection_id: str
    source_node: str
    target_node: str
    data_filter: Optional[Dict[str, Any]]
    transformation: Optional[str]
    is_active: bool


@dataclass
class DataFlowPipeline:
    """Represents a complete data flow pipeline."""
    pipeline_id: str
    name: str
    description: str
    nodes: List[FlowNode]
    connections: List[FlowConnection]
    status: FlowStatus
    created_at: float
    last_executed: Optional[float]
    execution_count: int
    success_rate: float


class DataFlowAutomation:
    """
    Automated data flow management system with transformation pipelines,
    intelligent routing, and real-time processing capabilities.
    """
    
    def __init__(self, config: ConfigManager):
        self.config = config
        self.pipelines = {}
        self.nodes = {}
        self.active_flows = {}
        self.data_queue = asyncio.Queue()
        self.processors = {}
        
        # Configuration
        self.max_concurrent_flows = config.get("data_flow.max_concurrent", 10)
        self.queue_size_limit = config.get("data_flow.queue_size", 1000)
        self.processing_timeout = config.get("data_flow.timeout", 300)
        self.auto_retry = config.get("data_flow.auto_retry", True)
        self.max_retries = config.get("data_flow.max_retries", 3)
        
        # State
        self.is_running = False
        self.processor_task = None
        self.monitor_task = None
        
        # Metrics
        self.metrics = {
            "total_packets": 0,
            "processed_packets": 0,
            "failed_packets": 0,
            "average_processing_time": 0.0,
            "throughput": 0.0,
            "active_pipelines": 0
        }
        
        # Initialize built-in processors
        self._register_builtin_processors()
    
    async def start(self):
        """Start the data flow automation system."""
        self.is_running = True
        self.processor_task = asyncio.create_task(self._process_data_queue())
        self.monitor_task = asyncio.create_task(self._monitor_flows())
        
        # Load saved pipelines
        await self._load_pipelines()
    
    async def stop(self):
        """Stop the data flow automation system."""
        self.is_running = False
        
        # Cancel active flows
        for flow_id in list(self.active_flows.keys()):
            await self.cancel_flow(flow_id)
        
        # Cancel tasks
        if self.processor_task:
            self.processor_task.cancel()
        if self.monitor_task:
            self.monitor_task.cancel()
        
        # Save pipelines
        await self._save_pipelines()
    
    async def create_pipeline(self, name: str, description: str = "") -> str:
        """Create a new data flow pipeline."""
        pipeline_id = str(uuid.uuid4())
        
        pipeline = DataFlowPipeline(
            pipeline_id=pipeline_id,
            name=name,
            description=description,
            nodes=[],
            connections=[],
            status=FlowStatus.PENDING,
            created_at=time.time(),
            last_executed=None,
            execution_count=0,
            success_rate=0.0
        )
        
        self.pipelines[pipeline_id] = pipeline
        return pipeline_id
    
    async def add_node_to_pipeline(self, pipeline_id: str, node: FlowNode):
        """Add a node to a pipeline."""
        if pipeline_id not in self.pipelines:
            raise ValueError(f"Pipeline {pipeline_id} not found")
        
        pipeline = self.pipelines[pipeline_id]
        pipeline.nodes.append(node)
        self.nodes[node.node_id] = node
    
    async def connect_nodes(self, pipeline_id: str, source_node_id: str, 
                          target_node_id: str, data_filter: Dict[str, Any] = None,
                          transformation: str = None) -> str:
        """Connect two nodes in a pipeline."""
        if pipeline_id not in self.pipelines:
            raise ValueError(f"Pipeline {pipeline_id} not found")
        
        connection_id = str(uuid.uuid4())
        
        connection = FlowConnection(
            connection_id=connection_id,
            source_node=source_node_id,
            target_node=target_node_id,
            data_filter=data_filter,
            transformation=transformation,
            is_active=True
        )
        
        pipeline = self.pipelines[pipeline_id]
        pipeline.connections.append(connection)
        
        return connection_id
    
    async def execute_pipeline(self, pipeline_id: str, input_data: Any = None) -> str:
        """Execute a data flow pipeline."""
        if pipeline_id not in self.pipelines:
            raise ValueError(f"Pipeline {pipeline_id} not found")
        
        pipeline = self.pipelines[pipeline_id]
        flow_id = str(uuid.uuid4())
        
        # Create initial data packet
        if input_data is not None:
            packet = DataPacket(
                packet_id=str(uuid.uuid4()),
                data=input_data,
                data_type=self._detect_data_type(input_data),
                metadata={"pipeline_id": pipeline_id, "flow_id": flow_id},
                timestamp=time.time(),
                source="external",
                destination=None,
                transformations=[]
            )
            
            await self.data_queue.put(packet)
        
        # Track active flow
        self.active_flows[flow_id] = {
            "pipeline_id": pipeline_id,
            "status": FlowStatus.RUNNING,
            "start_time": time.time(),
            "packets_processed": 0,
            "packets_failed": 0
        }
        
        pipeline.status = FlowStatus.RUNNING
        pipeline.execution_count += 1
        pipeline.last_executed = time.time()
        
        return flow_id
    
    async def send_data(self, data: Any, source: str, destination: str = None,
                       metadata: Dict[str, Any] = None) -> str:
        """Send data through the flow system."""
        packet = DataPacket(
            packet_id=str(uuid.uuid4()),
            data=data,
            data_type=self._detect_data_type(data),
            metadata=metadata or {},
            timestamp=time.time(),
            source=source,
            destination=destination,
            transformations=[]
        )
        
        await self.data_queue.put(packet)
        self.metrics["total_packets"] += 1
        
        return packet.packet_id
    
    async def register_processor(self, processor_type: str, processor: Callable):
        """Register a custom data processor."""
        self.processors[processor_type] = processor
    
    async def get_pipeline_status(self, pipeline_id: str) -> Dict[str, Any]:
        """Get the status of a pipeline."""
        if pipeline_id not in self.pipelines:
            return {"error": "Pipeline not found"}
        
        pipeline = self.pipelines[pipeline_id]
        
        return {
            "pipeline_id": pipeline_id,
            "name": pipeline.name,
            "status": pipeline.status.value,
            "nodes": len(pipeline.nodes),
            "connections": len(pipeline.connections),
            "execution_count": pipeline.execution_count,
            "success_rate": pipeline.success_rate,
            "last_executed": pipeline.last_executed
        }
    
    async def get_flow_status(self, flow_id: str) -> Dict[str, Any]:
        """Get the status of an active flow."""
        if flow_id not in self.active_flows:
            return {"error": "Flow not found"}
        
        flow = self.active_flows[flow_id]
        
        return {
            "flow_id": flow_id,
            "pipeline_id": flow["pipeline_id"],
            "status": flow["status"].value,
            "start_time": flow["start_time"],
            "duration": time.time() - flow["start_time"],
            "packets_processed": flow["packets_processed"],
            "packets_failed": flow["packets_failed"]
        }
    
    async def cancel_flow(self, flow_id: str) -> bool:
        """Cancel an active flow."""
        if flow_id in self.active_flows:
            self.active_flows[flow_id]["status"] = FlowStatus.CANCELLED
            del self.active_flows[flow_id]
            return True
        return False
    
    async def get_system_metrics(self) -> Dict[str, Any]:
        """Get system-wide metrics."""
        self.metrics["active_pipelines"] = len([p for p in self.pipelines.values() 
                                              if p.status == FlowStatus.RUNNING])
        self.metrics["queue_size"] = self.data_queue.qsize()
        
        return self.metrics.copy()

    async def _process_data_queue(self):
        """Process data packets from the queue."""
        while self.is_running:
            try:
                # Get packet from queue with timeout
                packet = await asyncio.wait_for(
                    self.data_queue.get(),
                    timeout=1.0
                )

                await self._process_packet(packet)

            except asyncio.TimeoutError:
                continue
            except Exception as e:
                print(f"Error processing data queue: {e}")
                await asyncio.sleep(1)

    async def _process_packet(self, packet: DataPacket):
        """Process a single data packet."""
        try:
            start_time = time.time()

            # Find appropriate pipeline or route directly
            if "pipeline_id" in packet.metadata:
                pipeline_id = packet.metadata["pipeline_id"]
                await self._process_through_pipeline(packet, pipeline_id)
            else:
                await self._route_packet(packet)

            # Update metrics
            processing_time = time.time() - start_time
            self._update_processing_metrics(processing_time, True)

        except Exception as e:
            print(f"Error processing packet {packet.packet_id}: {e}")
            self._update_processing_metrics(0, False)

    async def _process_through_pipeline(self, packet: DataPacket, pipeline_id: str):
        """Process packet through a specific pipeline."""
        if pipeline_id not in self.pipelines:
            raise ValueError(f"Pipeline {pipeline_id} not found")

        pipeline = self.pipelines[pipeline_id]
        flow_id = packet.metadata.get("flow_id")

        # Find entry nodes (nodes with no incoming connections)
        entry_nodes = self._find_entry_nodes(pipeline)

        if not entry_nodes:
            raise ValueError("No entry nodes found in pipeline")

        # Process through each entry node
        for node in entry_nodes:
            if self._can_process_data_type(node, packet.data_type):
                await self._process_through_node(packet, node, pipeline)

        # Update flow status
        if flow_id and flow_id in self.active_flows:
            self.active_flows[flow_id]["packets_processed"] += 1

    async def _process_through_node(self, packet: DataPacket, node: FlowNode,
                                  pipeline: DataFlowPipeline):
        """Process packet through a specific node."""
        if not node.is_active:
            return

        try:
            # Get processor for this node type
            processor = node.processor or self.processors.get(node.node_type)

            if not processor:
                raise ValueError(f"No processor found for node type {node.node_type}")

            # Process the data
            result = await processor(packet.data, node.configuration)

            # Create output packet
            output_packet = DataPacket(
                packet_id=str(uuid.uuid4()),
                data=result,
                data_type=self._detect_data_type(result),
                metadata=packet.metadata.copy(),
                timestamp=time.time(),
                source=node.node_id,
                destination=None,
                transformations=packet.transformations + [node.node_id]
            )

            # Route to connected nodes
            await self._route_to_connected_nodes(output_packet, node, pipeline)

        except Exception as e:
            print(f"Error processing through node {node.node_id}: {e}")

            # Update flow failure count
            flow_id = packet.metadata.get("flow_id")
            if flow_id and flow_id in self.active_flows:
                self.active_flows[flow_id]["packets_failed"] += 1

    async def _route_to_connected_nodes(self, packet: DataPacket, source_node: FlowNode,
                                      pipeline: DataFlowPipeline):
        """Route packet to connected nodes."""
        for connection in pipeline.connections:
            if (connection.source_node == source_node.node_id and
                connection.is_active):

                # Apply data filter if specified
                if connection.data_filter:
                    if not self._apply_data_filter(packet, connection.data_filter):
                        continue

                # Apply transformation if specified
                if connection.transformation:
                    packet.data = await self._apply_transformation(
                        packet.data, connection.transformation
                    )

                # Find target node
                target_node = next(
                    (n for n in pipeline.nodes if n.node_id == connection.target_node),
                    None
                )

                if target_node:
                    await self._process_through_node(packet, target_node, pipeline)

    async def _route_packet(self, packet: DataPacket):
        """Route packet to appropriate destination."""
        if packet.destination:
            # Route to specific destination
            if packet.destination in self.nodes:
                node = self.nodes[packet.destination]
                # Find pipeline containing this node
                for pipeline in self.pipelines.values():
                    if any(n.node_id == node.node_id for n in pipeline.nodes):
                        await self._process_through_node(packet, node, pipeline)
                        break
        else:
            # Auto-route based on data type and available processors
            suitable_nodes = self._find_suitable_nodes(packet.data_type)

            for node in suitable_nodes:
                # Find pipeline containing this node
                for pipeline in self.pipelines.values():
                    if any(n.node_id == node.node_id for n in pipeline.nodes):
                        await self._process_through_node(packet, node, pipeline)
                        break

    def _find_entry_nodes(self, pipeline: DataFlowPipeline) -> List[FlowNode]:
        """Find entry nodes in a pipeline."""
        target_nodes = {conn.target_node for conn in pipeline.connections}
        return [node for node in pipeline.nodes if node.node_id not in target_nodes]

    def _find_suitable_nodes(self, data_type: DataType) -> List[FlowNode]:
        """Find nodes that can process the given data type."""
        return [node for node in self.nodes.values()
                if self._can_process_data_type(node, data_type)]

    def _can_process_data_type(self, node: FlowNode, data_type: DataType) -> bool:
        """Check if node can process the given data type."""
        return data_type in node.input_types or not node.input_types

    def _apply_data_filter(self, packet: DataPacket, data_filter: Dict[str, Any]) -> bool:
        """Apply data filter to determine if packet should pass."""
        for key, expected_value in data_filter.items():
            if key == "data_type":
                if packet.data_type.value != expected_value:
                    return False
            elif key == "source":
                if packet.source != expected_value:
                    return False
            elif key in packet.metadata:
                if packet.metadata[key] != expected_value:
                    return False
            else:
                return False

        return True

    async def _apply_transformation(self, data: Any, transformation: str) -> Any:
        """Apply transformation to data."""
        if transformation in self.processors:
            processor = self.processors[transformation]
            return await processor(data, {})

        # Built-in transformations
        if transformation == "to_string":
            return str(data)
        elif transformation == "to_json":
            return json.dumps(data) if not isinstance(data, str) else data
        elif transformation == "to_upper":
            return str(data).upper()
        elif transformation == "to_lower":
            return str(data).lower()
        elif transformation == "strip":
            return str(data).strip()

        return data

    def _detect_data_type(self, data: Any) -> DataType:
        """Detect the type of data."""
        if isinstance(data, str):
            try:
                json.loads(data)
                return DataType.JSON
            except:
                if '\n' in data and (',' in data or '\t' in data):
                    return DataType.CSV
                return DataType.TEXT
        elif isinstance(data, (dict, list)):
            return DataType.STRUCTURED
        elif isinstance(data, bytes):
            return DataType.BINARY
        elif isinstance(data, (int, float)):
            return DataType.STRUCTURED
        else:
            return DataType.UNSTRUCTURED

    def _update_processing_metrics(self, processing_time: float, success: bool):
        """Update processing metrics."""
        if success:
            self.metrics["processed_packets"] += 1
        else:
            self.metrics["failed_packets"] += 1

        # Update average processing time
        total_processed = self.metrics["processed_packets"]
        if total_processed > 0:
            current_avg = self.metrics["average_processing_time"]
            self.metrics["average_processing_time"] = (
                (current_avg * (total_processed - 1) + processing_time) / total_processed
            )

        # Calculate throughput (packets per second)
        total_packets = self.metrics["processed_packets"] + self.metrics["failed_packets"]
        if total_packets > 0:
            self.metrics["throughput"] = total_packets / max(1, time.time() - getattr(self, '_start_time', time.time()))

    async def _monitor_flows(self):
        """Monitor active flows and update pipeline status."""
        while self.is_running:
            try:
                current_time = time.time()
                completed_flows = []

                for flow_id, flow_data in self.active_flows.items():
                    # Check for timeout
                    if current_time - flow_data["start_time"] > self.processing_timeout:
                        flow_data["status"] = FlowStatus.FAILED
                        completed_flows.append(flow_id)

                    # Check if flow is complete (no more packets in queue for this flow)
                    elif flow_data["status"] == FlowStatus.RUNNING:
                        # Simple completion check - in practice this would be more sophisticated
                        if current_time - flow_data["start_time"] > 10:  # 10 seconds
                            flow_data["status"] = FlowStatus.COMPLETED
                            completed_flows.append(flow_id)

                # Clean up completed flows
                for flow_id in completed_flows:
                    await self._complete_flow(flow_id)

                await asyncio.sleep(5)  # Check every 5 seconds

            except Exception as e:
                print(f"Error monitoring flows: {e}")
                await asyncio.sleep(5)

    async def _complete_flow(self, flow_id: str):
        """Complete a flow and update pipeline statistics."""
        if flow_id not in self.active_flows:
            return

        flow_data = self.active_flows[flow_id]
        pipeline_id = flow_data["pipeline_id"]

        if pipeline_id in self.pipelines:
            pipeline = self.pipelines[pipeline_id]

            # Update pipeline success rate
            total_packets = flow_data["packets_processed"] + flow_data["packets_failed"]
            if total_packets > 0:
                flow_success_rate = flow_data["packets_processed"] / total_packets

                # Update overall success rate
                total_executions = pipeline.execution_count
                if total_executions > 1:
                    pipeline.success_rate = (
                        (pipeline.success_rate * (total_executions - 1) + flow_success_rate)
                        / total_executions
                    )
                else:
                    pipeline.success_rate = flow_success_rate

            # Update pipeline status
            if flow_data["status"] == FlowStatus.COMPLETED:
                pipeline.status = FlowStatus.COMPLETED
            elif flow_data["status"] == FlowStatus.FAILED:
                pipeline.status = FlowStatus.FAILED

        # Remove from active flows
        del self.active_flows[flow_id]

    def _register_builtin_processors(self):
        """Register built-in data processors."""
        self.processors = {
            "text_processor": self._process_text,
            "json_processor": self._process_json,
            "csv_processor": self._process_csv,
            "filter": self._filter_data,
            "transform": self._transform_data,
            "validate": self._validate_data,
            "aggregate": self._aggregate_data,
            "enrich": self._enrich_data,
            "clean": self._clean_data,
            "normalize": self._normalize_data
        }

    async def _process_text(self, data: Any, config: Dict[str, Any]) -> str:
        """Process text data."""
        text = str(data)

        if config.get("to_upper"):
            text = text.upper()
        elif config.get("to_lower"):
            text = text.lower()

        if config.get("strip"):
            text = text.strip()

        if config.get("replace"):
            old, new = config["replace"]
            text = text.replace(old, new)

        return text

    async def _process_json(self, data: Any, config: Dict[str, Any]) -> Any:
        """Process JSON data."""
        if isinstance(data, str):
            try:
                json_data = json.loads(data)
            except json.JSONDecodeError:
                return data
        else:
            json_data = data

        # Extract specific fields
        if config.get("extract_fields"):
            fields = config["extract_fields"]
            if isinstance(json_data, dict):
                return {field: json_data.get(field) for field in fields}

        # Transform structure
        if config.get("flatten") and isinstance(json_data, dict):
            return self._flatten_dict(json_data)

        return json_data

    async def _process_csv(self, data: Any, config: Dict[str, Any]) -> Any:
        """Process CSV data."""
        if isinstance(data, str):
            # Parse CSV string
            lines = data.strip().split('\n')
            if not lines:
                return []

            headers = [h.strip() for h in lines[0].split(',')]
            rows = []

            for line in lines[1:]:
                values = [v.strip() for v in line.split(',')]
                if len(values) == len(headers):
                    rows.append(dict(zip(headers, values)))

            return rows

        return data

    async def _filter_data(self, data: Any, config: Dict[str, Any]) -> Any:
        """Filter data based on criteria."""
        criteria = config.get("criteria", {})

        if isinstance(data, list):
            filtered = []
            for item in data:
                if self._matches_criteria(item, criteria):
                    filtered.append(item)
            return filtered
        elif isinstance(data, dict):
            if self._matches_criteria(data, criteria):
                return data
            else:
                return None

        return data

    async def _transform_data(self, data: Any, config: Dict[str, Any]) -> Any:
        """Transform data structure."""
        transformation = config.get("transformation", "identity")

        if transformation == "to_list":
            if isinstance(data, dict):
                return list(data.values())
            return [data]
        elif transformation == "to_dict":
            if isinstance(data, list) and len(data) == 2:
                return {data[0]: data[1]}
            return {"value": data}
        elif transformation == "keys_only":
            if isinstance(data, dict):
                return list(data.keys())
        elif transformation == "values_only":
            if isinstance(data, dict):
                return list(data.values())

        return data

    async def _validate_data(self, data: Any, config: Dict[str, Any]) -> Any:
        """Validate data against schema."""
        schema = config.get("schema", {})

        if not schema:
            return data

        # Simple validation
        if "type" in schema:
            expected_type = schema["type"]
            if expected_type == "string" and not isinstance(data, str):
                raise ValueError("Expected string data")
            elif expected_type == "number" and not isinstance(data, (int, float)):
                raise ValueError("Expected numeric data")
            elif expected_type == "object" and not isinstance(data, dict):
                raise ValueError("Expected object data")
            elif expected_type == "array" and not isinstance(data, list):
                raise ValueError("Expected array data")

        return data

    async def _aggregate_data(self, data: Any, config: Dict[str, Any]) -> Any:
        """Aggregate data."""
        operation = config.get("operation", "count")

        if isinstance(data, list):
            if operation == "count":
                return len(data)
            elif operation == "sum" and all(isinstance(x, (int, float)) for x in data):
                return sum(data)
            elif operation == "average" and all(isinstance(x, (int, float)) for x in data):
                return sum(data) / len(data) if data else 0
            elif operation == "max" and all(isinstance(x, (int, float)) for x in data):
                return max(data) if data else None
            elif operation == "min" and all(isinstance(x, (int, float)) for x in data):
                return min(data) if data else None

        return data

    async def _enrich_data(self, data: Any, config: Dict[str, Any]) -> Any:
        """Enrich data with additional information."""
        enrichments = config.get("enrichments", {})

        if isinstance(data, dict):
            enriched = data.copy()
            enriched.update(enrichments)

            # Add timestamp if requested
            if config.get("add_timestamp"):
                enriched["timestamp"] = time.time()

            return enriched

        return data

    async def _clean_data(self, data: Any, config: Dict[str, Any]) -> Any:
        """Clean data by removing unwanted elements."""
        if isinstance(data, dict):
            cleaned = {}
            remove_keys = config.get("remove_keys", [])

            for key, value in data.items():
                if key not in remove_keys:
                    if isinstance(value, str) and config.get("strip_strings"):
                        value = value.strip()
                    if value is not None or not config.get("remove_null"):
                        cleaned[key] = value

            return cleaned
        elif isinstance(data, list):
            cleaned = []
            for item in data:
                if item is not None or not config.get("remove_null"):
                    cleaned.append(item)
            return cleaned

        return data

    async def _normalize_data(self, data: Any, config: Dict[str, Any]) -> Any:
        """Normalize data format."""
        if isinstance(data, dict):
            normalized = {}

            for key, value in data.items():
                # Normalize key names
                if config.get("lowercase_keys"):
                    key = key.lower()
                if config.get("snake_case_keys"):
                    key = key.replace(' ', '_').replace('-', '_')

                # Normalize values
                if isinstance(value, str):
                    if config.get("trim_strings"):
                        value = value.strip()
                    if config.get("lowercase_strings"):
                        value = value.lower()

                normalized[key] = value

            return normalized

        return data

    def _flatten_dict(self, d: dict, parent_key: str = '', sep: str = '.') -> dict:
        """Flatten a nested dictionary."""
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key, sep=sep).items())
            else:
                items.append((new_key, v))
        return dict(items)

    def _matches_criteria(self, item: Any, criteria: Dict[str, Any]) -> bool:
        """Check if item matches filter criteria."""
        if not isinstance(item, dict):
            return True

        for key, expected_value in criteria.items():
            if key not in item:
                return False

            actual_value = item[key]

            if isinstance(expected_value, dict):
                # Handle operators
                if "$gt" in expected_value:
                    if not (actual_value > expected_value["$gt"]):
                        return False
                if "$lt" in expected_value:
                    if not (actual_value < expected_value["$lt"]):
                        return False
                if "$eq" in expected_value:
                    if actual_value != expected_value["$eq"]:
                        return False
                if "$in" in expected_value:
                    if actual_value not in expected_value["$in"]:
                        return False
            else:
                if actual_value != expected_value:
                    return False

        return True

    async def _save_pipelines(self):
        """Save pipelines to disk."""
        pipelines_file = Path("data/pipelines.json")
        pipelines_file.parent.mkdir(parents=True, exist_ok=True)

        # Convert pipelines to serializable format
        serializable_pipelines = {}
        for pipeline_id, pipeline in self.pipelines.items():
            serializable_pipelines[pipeline_id] = {
                "pipeline_id": pipeline.pipeline_id,
                "name": pipeline.name,
                "description": pipeline.description,
                "nodes": [
                    {
                        "node_id": node.node_id,
                        "name": node.name,
                        "node_type": node.node_type,
                        "input_types": [dt.value for dt in node.input_types],
                        "output_types": [dt.value for dt in node.output_types],
                        "configuration": node.configuration,
                        "is_active": node.is_active
                    }
                    for node in pipeline.nodes
                ],
                "connections": [asdict(conn) for conn in pipeline.connections],
                "status": pipeline.status.value,
                "created_at": pipeline.created_at,
                "last_executed": pipeline.last_executed,
                "execution_count": pipeline.execution_count,
                "success_rate": pipeline.success_rate
            }

        try:
            with open(pipelines_file, 'w') as f:
                json.dump(serializable_pipelines, f, indent=2)
        except Exception as e:
            print(f"Error saving pipelines: {e}")

    async def _load_pipelines(self):
        """Load pipelines from disk."""
        pipelines_file = Path("data/pipelines.json")

        if not pipelines_file.exists():
            return

        try:
            with open(pipelines_file, 'r') as f:
                serializable_pipelines = json.load(f)

            for pipeline_id, pipeline_data in serializable_pipelines.items():
                # Reconstruct nodes
                nodes = []
                for node_data in pipeline_data["nodes"]:
                    node = FlowNode(
                        node_id=node_data["node_id"],
                        name=node_data["name"],
                        node_type=node_data["node_type"],
                        input_types=[DataType(dt) for dt in node_data["input_types"]],
                        output_types=[DataType(dt) for dt in node_data["output_types"]],
                        configuration=node_data["configuration"],
                        processor=None,  # Will be set when needed
                        is_active=node_data["is_active"]
                    )
                    nodes.append(node)
                    self.nodes[node.node_id] = node

                # Reconstruct connections
                connections = []
                for conn_data in pipeline_data["connections"]:
                    connection = FlowConnection(
                        connection_id=conn_data["connection_id"],
                        source_node=conn_data["source_node"],
                        target_node=conn_data["target_node"],
                        data_filter=conn_data["data_filter"],
                        transformation=conn_data["transformation"],
                        is_active=conn_data["is_active"]
                    )
                    connections.append(connection)

                # Reconstruct pipeline
                pipeline = DataFlowPipeline(
                    pipeline_id=pipeline_data["pipeline_id"],
                    name=pipeline_data["name"],
                    description=pipeline_data["description"],
                    nodes=nodes,
                    connections=connections,
                    status=FlowStatus(pipeline_data["status"]),
                    created_at=pipeline_data["created_at"],
                    last_executed=pipeline_data["last_executed"],
                    execution_count=pipeline_data["execution_count"],
                    success_rate=pipeline_data["success_rate"]
                )

                self.pipelines[pipeline_id] = pipeline

        except Exception as e:
            print(f"Error loading pipelines: {e}")


# Utility functions for creating flow components
def create_flow_node(name: str, node_type: str,
                    input_types: List[DataType] = None,
                    output_types: List[DataType] = None,
                    configuration: Dict[str, Any] = None) -> FlowNode:
    """Create a flow node."""
    return FlowNode(
        node_id=str(uuid.uuid4()),
        name=name,
        node_type=node_type,
        input_types=input_types or [],
        output_types=output_types or [],
        configuration=configuration or {},
        processor=None,
        is_active=True
    )


def create_text_processor_node(name: str = "Text Processor") -> FlowNode:
    """Create a text processing node."""
    return create_flow_node(
        name=name,
        node_type="text_processor",
        input_types=[DataType.TEXT],
        output_types=[DataType.TEXT],
        configuration={"strip": True, "to_lower": False}
    )


def create_json_processor_node(name: str = "JSON Processor") -> FlowNode:
    """Create a JSON processing node."""
    return create_flow_node(
        name=name,
        node_type="json_processor",
        input_types=[DataType.JSON, DataType.TEXT],
        output_types=[DataType.JSON, DataType.STRUCTURED],
        configuration={"flatten": False}
    )


def create_filter_node(name: str = "Data Filter", criteria: Dict[str, Any] = None) -> FlowNode:
    """Create a data filter node."""
    return create_flow_node(
        name=name,
        node_type="filter",
        input_types=[DataType.STRUCTURED, DataType.JSON],
        output_types=[DataType.STRUCTURED, DataType.JSON],
        configuration={"criteria": criteria or {}}
    )


def create_transform_node(name: str = "Data Transform",
                         transformation: str = "identity") -> FlowNode:
    """Create a data transformation node."""
    return create_flow_node(
        name=name,
        node_type="transform",
        input_types=[DataType.STRUCTURED, DataType.JSON],
        output_types=[DataType.STRUCTURED, DataType.JSON],
        configuration={"transformation": transformation}
    )
