"""
Intelligent Task Scheduler
Phase 3 - Advanced Automation

This module provides intelligent task scheduling with priority management,
resource optimization, and adaptive scheduling based on user patterns.
"""

import asyncio
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import heapq
from pathlib import Path

from ..utils.config_manager import ConfigManager


class TaskPriority(Enum):
    """Task priority levels."""
    CRITICAL = 1
    HIGH = 2
    NORMAL = 3
    LOW = 4
    BACKGROUND = 5


class TaskStatus(Enum):
    """Task execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


class ScheduleType(Enum):
    """Types of task scheduling."""
    IMMEDIATE = "immediate"
    DELAYED = "delayed"
    RECURRING = "recurring"
    CONDITIONAL = "conditional"
    ADAPTIVE = "adaptive"


@dataclass
class TaskDefinition:
    """Defines a schedulable task."""
    task_id: str
    name: str
    description: str
    priority: TaskPriority
    schedule_type: ScheduleType
    scheduled_time: Optional[float]
    recurrence_pattern: Optional[str]
    conditions: Dict[str, Any]
    parameters: Dict[str, Any]
    estimated_duration: float
    max_retries: int
    timeout: float
    dependencies: List[str]
    resource_requirements: Dict[str, Any]


@dataclass
class TaskExecution:
    """Represents a task execution instance."""
    execution_id: str
    task_id: str
    status: TaskStatus
    start_time: Optional[float]
    end_time: Optional[float]
    result: Optional[Any]
    error: Optional[str]
    retry_count: int
    resource_usage: Dict[str, Any]


@dataclass
class SchedulerMetrics:
    """Scheduler performance metrics."""
    total_tasks: int
    completed_tasks: int
    failed_tasks: int
    average_execution_time: float
    resource_utilization: Dict[str, float]
    queue_length: int
    throughput: float


class IntelligentTaskScheduler:
    """
    Intelligent task scheduler with priority management, resource optimization,
    and adaptive scheduling based on user patterns and system state.
    """
    
    def __init__(self, config: ConfigManager):
        self.config = config
        self.tasks = {}
        self.task_queue = []  # Priority queue
        self.running_tasks = {}
        self.completed_tasks = {}
        self.failed_tasks = {}
        
        # Configuration
        self.max_concurrent_tasks = config.get("scheduler.max_concurrent", 5)
        self.default_timeout = config.get("scheduler.default_timeout", 300)
        self.cleanup_interval = config.get("scheduler.cleanup_interval", 3600)
        self.adaptive_learning = config.get("scheduler.adaptive_learning", True)
        
        # State
        self.is_running = False
        self.scheduler_task = None
        self.cleanup_task = None
        self.next_task_id = 1
        
        # Metrics
        self.metrics = SchedulerMetrics(
            total_tasks=0,
            completed_tasks=0,
            failed_tasks=0,
            average_execution_time=0.0,
            resource_utilization={},
            queue_length=0,
            throughput=0.0
        )
        
        # Resource tracking
        self.available_resources = {
            "cpu": 100.0,
            "memory": 100.0,
            "network": 100.0,
            "disk": 100.0
        }
        
        # User pattern learning
        self.user_patterns = {}
        self.execution_history = []
        
        # Task executors
        self.task_executors = {}
        self._register_builtin_executors()
    
    async def start(self):
        """Start the intelligent task scheduler."""
        self.is_running = True
        self.scheduler_task = asyncio.create_task(self._scheduler_loop())
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        # Load saved tasks and patterns
        await self._load_scheduler_state()
    
    async def stop(self):
        """Stop the task scheduler."""
        self.is_running = False
        
        # Cancel running tasks
        for execution in self.running_tasks.values():
            if hasattr(execution, 'task') and not execution.task.done():
                execution.task.cancel()
        
        # Cancel scheduler tasks
        if self.scheduler_task:
            self.scheduler_task.cancel()
        if self.cleanup_task:
            self.cleanup_task.cancel()
        
        # Save state
        await self._save_scheduler_state()
    
    async def schedule_task(self, task_def: TaskDefinition) -> str:
        """Schedule a new task."""
        # Validate task
        if task_def.task_id in self.tasks:
            raise ValueError(f"Task {task_def.task_id} already exists")
        
        # Check dependencies
        for dep_id in task_def.dependencies:
            if dep_id not in self.tasks and dep_id not in self.completed_tasks:
                raise ValueError(f"Dependency {dep_id} not found")
        
        # Store task
        self.tasks[task_def.task_id] = task_def
        
        # Add to queue based on schedule type
        if task_def.schedule_type == ScheduleType.IMMEDIATE:
            await self._add_to_queue(task_def)
        elif task_def.schedule_type == ScheduleType.DELAYED:
            # Will be added by scheduler loop when time comes
            pass
        elif task_def.schedule_type == ScheduleType.RECURRING:
            await self._schedule_recurring_task(task_def)
        elif task_def.schedule_type == ScheduleType.CONDITIONAL:
            # Will be checked by scheduler loop
            pass
        elif task_def.schedule_type == ScheduleType.ADAPTIVE:
            await self._schedule_adaptive_task(task_def)
        
        self.metrics.total_tasks += 1
        return task_def.task_id
    
    async def cancel_task(self, task_id: str) -> bool:
        """Cancel a scheduled or running task."""
        # Check if task is running
        if task_id in self.running_tasks:
            execution = self.running_tasks[task_id]
            if hasattr(execution, 'task') and not execution.task.done():
                execution.task.cancel()
                execution.status = TaskStatus.CANCELLED
                execution.end_time = time.time()
                del self.running_tasks[task_id]
                return True
        
        # Check if task is in queue
        for i, (priority, scheduled_time, queued_task_id) in enumerate(self.task_queue):
            if queued_task_id == task_id:
                del self.task_queue[i]
                heapq.heapify(self.task_queue)
                return True
        
        # Check if task exists but not scheduled
        if task_id in self.tasks:
            del self.tasks[task_id]
            return True
        
        return False
    
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get the status of a specific task."""
        if task_id in self.running_tasks:
            execution = self.running_tasks[task_id]
            return {
                "status": execution.status.value,
                "start_time": execution.start_time,
                "progress": "running"
            }
        
        if task_id in self.completed_tasks:
            execution = self.completed_tasks[task_id]
            return {
                "status": execution.status.value,
                "start_time": execution.start_time,
                "end_time": execution.end_time,
                "result": execution.result,
                "duration": execution.end_time - execution.start_time if execution.end_time and execution.start_time else 0
            }
        
        if task_id in self.failed_tasks:
            execution = self.failed_tasks[task_id]
            return {
                "status": execution.status.value,
                "start_time": execution.start_time,
                "end_time": execution.end_time,
                "error": execution.error,
                "retry_count": execution.retry_count
            }
        
        # Check if task is queued
        for priority, scheduled_time, queued_task_id in self.task_queue:
            if queued_task_id == task_id:
                return {
                    "status": "queued",
                    "scheduled_time": scheduled_time,
                    "priority": priority
                }
        
        if task_id in self.tasks:
            return {
                "status": "scheduled",
                "schedule_type": self.tasks[task_id].schedule_type.value
            }
        
        return None
    
    async def get_queue_status(self) -> Dict[str, Any]:
        """Get current queue status and metrics."""
        return {
            "queue_length": len(self.task_queue),
            "running_tasks": len(self.running_tasks),
            "metrics": asdict(self.metrics),
            "resource_utilization": self.available_resources,
            "next_tasks": [
                {
                    "task_id": task_id,
                    "priority": priority,
                    "scheduled_time": scheduled_time
                }
                for priority, scheduled_time, task_id in sorted(self.task_queue)[:5]
            ]
        }
    
    async def register_task_executor(self, task_type: str, executor: Callable):
        """Register a custom task executor."""
        self.task_executors[task_type] = executor
    
    async def create_simple_task(self, name: str, task_type: str, parameters: Dict[str, Any] = None,
                                priority: TaskPriority = TaskPriority.NORMAL,
                                schedule_type: ScheduleType = ScheduleType.IMMEDIATE) -> str:
        """Create a simple task with minimal configuration."""
        task_id = f"task_{self.next_task_id}"
        self.next_task_id += 1
        
        task_def = TaskDefinition(
            task_id=task_id,
            name=name,
            description=f"Simple {task_type} task",
            priority=priority,
            schedule_type=schedule_type,
            scheduled_time=None,
            recurrence_pattern=None,
            conditions={},
            parameters={"task_type": task_type, **(parameters or {})},
            estimated_duration=30.0,
            max_retries=3,
            timeout=self.default_timeout,
            dependencies=[],
            resource_requirements={"cpu": 10.0, "memory": 10.0}
        )
        
        return await self.schedule_task(task_def)

    async def _scheduler_loop(self):
        """Main scheduler loop."""
        while self.is_running:
            try:
                current_time = time.time()

                # Check for delayed tasks that are ready
                await self._check_delayed_tasks(current_time)

                # Check conditional tasks
                await self._check_conditional_tasks()

                # Execute ready tasks
                await self._execute_ready_tasks()

                # Update metrics
                await self._update_metrics()

                # Adaptive learning
                if self.adaptive_learning:
                    await self._learn_from_patterns()

                await asyncio.sleep(1)  # Check every second

            except Exception as e:
                print(f"Error in scheduler loop: {e}")
                await asyncio.sleep(5)

    async def _cleanup_loop(self):
        """Cleanup loop for old tasks and data."""
        while self.is_running:
            try:
                await self._cleanup_old_tasks()
                await self._optimize_resources()
                await asyncio.sleep(self.cleanup_interval)

            except Exception as e:
                print(f"Error in cleanup loop: {e}")
                await asyncio.sleep(self.cleanup_interval)

    async def _add_to_queue(self, task_def: TaskDefinition):
        """Add task to priority queue."""
        priority = task_def.priority.value
        scheduled_time = task_def.scheduled_time or time.time()

        # Adjust priority based on dependencies
        if task_def.dependencies:
            # Lower priority if dependencies not met
            priority += 1

        # Adjust priority based on resource requirements
        if not self._check_resource_availability(task_def.resource_requirements):
            priority += 1

        heapq.heappush(self.task_queue, (priority, scheduled_time, task_def.task_id))
        self.metrics.queue_length = len(self.task_queue)

    async def _check_delayed_tasks(self, current_time: float):
        """Check for delayed tasks that are ready to execute."""
        for task_id, task_def in self.tasks.items():
            if (task_def.schedule_type == ScheduleType.DELAYED and
                task_def.scheduled_time and
                task_def.scheduled_time <= current_time and
                task_id not in [tid for _, _, tid in self.task_queue]):

                await self._add_to_queue(task_def)

    async def _check_conditional_tasks(self):
        """Check conditional tasks for execution readiness."""
        for task_id, task_def in self.tasks.items():
            if (task_def.schedule_type == ScheduleType.CONDITIONAL and
                task_id not in [tid for _, _, tid in self.task_queue]):

                if await self._evaluate_conditions(task_def.conditions):
                    await self._add_to_queue(task_def)

    async def _execute_ready_tasks(self):
        """Execute tasks that are ready and resources are available."""
        while (self.task_queue and
               len(self.running_tasks) < self.max_concurrent_tasks):

            # Get highest priority task
            priority, scheduled_time, task_id = heapq.heappop(self.task_queue)

            if task_id not in self.tasks:
                continue  # Task was cancelled

            task_def = self.tasks[task_id]

            # Check dependencies
            if not await self._check_dependencies(task_def):
                # Re-queue with lower priority
                heapq.heappush(self.task_queue, (priority + 1, scheduled_time, task_id))
                continue

            # Check resource availability
            if not self._check_resource_availability(task_def.resource_requirements):
                # Re-queue for later
                heapq.heappush(self.task_queue, (priority, time.time() + 10, task_id))
                continue

            # Execute task
            await self._execute_task(task_def)

        self.metrics.queue_length = len(self.task_queue)

    async def _execute_task(self, task_def: TaskDefinition):
        """Execute a single task."""
        execution_id = f"exec_{task_def.task_id}_{int(time.time())}"

        execution = TaskExecution(
            execution_id=execution_id,
            task_id=task_def.task_id,
            status=TaskStatus.RUNNING,
            start_time=time.time(),
            end_time=None,
            result=None,
            error=None,
            retry_count=0,
            resource_usage={}
        )

        self.running_tasks[task_def.task_id] = execution

        try:
            # Reserve resources
            self._reserve_resources(task_def.resource_requirements)

            # Get task executor
            task_type = task_def.parameters.get("task_type", "default")
            executor = self.task_executors.get(task_type, self._default_executor)

            # Create execution task
            execution.task = asyncio.create_task(
                asyncio.wait_for(
                    executor(task_def, execution),
                    timeout=task_def.timeout
                )
            )

            # Wait for completion
            result = await execution.task

            # Task completed successfully
            execution.status = TaskStatus.COMPLETED
            execution.end_time = time.time()
            execution.result = result

            # Move to completed tasks
            self.completed_tasks[task_def.task_id] = execution
            del self.running_tasks[task_def.task_id]

            # Update metrics
            self.metrics.completed_tasks += 1

            # Learn from execution
            await self._record_execution_pattern(task_def, execution)

        except asyncio.TimeoutError:
            execution.status = TaskStatus.FAILED
            execution.end_time = time.time()
            execution.error = "Task timeout"
            await self._handle_task_failure(task_def, execution)

        except Exception as e:
            execution.status = TaskStatus.FAILED
            execution.end_time = time.time()
            execution.error = str(e)
            await self._handle_task_failure(task_def, execution)

        finally:
            # Release resources
            self._release_resources(task_def.resource_requirements)

            # Remove from running tasks if still there
            if task_def.task_id in self.running_tasks:
                del self.running_tasks[task_def.task_id]

    async def _handle_task_failure(self, task_def: TaskDefinition, execution: TaskExecution):
        """Handle task failure and retry logic."""
        execution.retry_count += 1

        if execution.retry_count <= task_def.max_retries:
            # Retry the task
            retry_delay = min(60, 2 ** execution.retry_count)  # Exponential backoff

            # Re-schedule with delay
            retry_task_def = TaskDefinition(
                task_id=f"{task_def.task_id}_retry_{execution.retry_count}",
                name=f"{task_def.name} (Retry {execution.retry_count})",
                description=task_def.description,
                priority=task_def.priority,
                schedule_type=ScheduleType.DELAYED,
                scheduled_time=time.time() + retry_delay,
                recurrence_pattern=None,
                conditions=task_def.conditions,
                parameters=task_def.parameters,
                estimated_duration=task_def.estimated_duration,
                max_retries=task_def.max_retries - execution.retry_count,
                timeout=task_def.timeout,
                dependencies=task_def.dependencies,
                resource_requirements=task_def.resource_requirements
            )

            await self.schedule_task(retry_task_def)
        else:
            # Max retries reached
            self.failed_tasks[task_def.task_id] = execution
            self.metrics.failed_tasks += 1

    async def _check_dependencies(self, task_def: TaskDefinition) -> bool:
        """Check if task dependencies are satisfied."""
        for dep_id in task_def.dependencies:
            if dep_id not in self.completed_tasks:
                return False
        return True

    def _check_resource_availability(self, requirements: Dict[str, Any]) -> bool:
        """Check if required resources are available."""
        for resource, amount in requirements.items():
            if resource in self.available_resources:
                if self.available_resources[resource] < amount:
                    return False
        return True

    def _reserve_resources(self, requirements: Dict[str, Any]):
        """Reserve resources for task execution."""
        for resource, amount in requirements.items():
            if resource in self.available_resources:
                self.available_resources[resource] -= amount

    def _release_resources(self, requirements: Dict[str, Any]):
        """Release resources after task completion."""
        for resource, amount in requirements.items():
            if resource in self.available_resources:
                self.available_resources[resource] += amount
                # Cap at 100%
                self.available_resources[resource] = min(100.0, self.available_resources[resource])

    async def _evaluate_conditions(self, conditions: Dict[str, Any]) -> bool:
        """Evaluate task conditions."""
        if not conditions:
            return True

        for condition_type, condition_value in conditions.items():
            if condition_type == "time_range":
                current_hour = datetime.now().hour
                start_hour, end_hour = condition_value
                if not (start_hour <= current_hour <= end_hour):
                    return False

            elif condition_type == "system_idle":
                # Check if system is idle (simplified)
                if len(self.running_tasks) >= self.max_concurrent_tasks:
                    return False

            elif condition_type == "resource_threshold":
                resource, threshold = condition_value
                if self.available_resources.get(resource, 0) < threshold:
                    return False

            elif condition_type == "user_active":
                # Check if user is active (simplified)
                # In practice, this would check mouse/keyboard activity
                pass

        return True

    async def _schedule_recurring_task(self, task_def: TaskDefinition):
        """Schedule a recurring task."""
        # Parse recurrence pattern (simplified)
        pattern = task_def.recurrence_pattern

        if pattern == "daily":
            next_run = time.time() + 86400  # 24 hours
        elif pattern == "hourly":
            next_run = time.time() + 3600   # 1 hour
        elif pattern == "weekly":
            next_run = time.time() + 604800 # 7 days
        else:
            # Custom pattern parsing would go here
            next_run = time.time() + 3600   # Default to hourly

        # Create next instance
        next_task_def = TaskDefinition(
            task_id=f"{task_def.task_id}_{int(next_run)}",
            name=task_def.name,
            description=task_def.description,
            priority=task_def.priority,
            schedule_type=ScheduleType.DELAYED,
            scheduled_time=next_run,
            recurrence_pattern=task_def.recurrence_pattern,
            conditions=task_def.conditions,
            parameters=task_def.parameters,
            estimated_duration=task_def.estimated_duration,
            max_retries=task_def.max_retries,
            timeout=task_def.timeout,
            dependencies=task_def.dependencies,
            resource_requirements=task_def.resource_requirements
        )

        self.tasks[next_task_def.task_id] = next_task_def

    async def _schedule_adaptive_task(self, task_def: TaskDefinition):
        """Schedule task using adaptive learning."""
        # Use learned patterns to determine optimal execution time
        optimal_time = await self._predict_optimal_execution_time(task_def)

        task_def.scheduled_time = optimal_time
        task_def.schedule_type = ScheduleType.DELAYED

        await self._add_to_queue(task_def)

    async def _predict_optimal_execution_time(self, task_def: TaskDefinition) -> float:
        """Predict optimal execution time based on patterns."""
        task_type = task_def.parameters.get("task_type", "default")

        if task_type in self.user_patterns:
            pattern = self.user_patterns[task_type]

            # Find best time based on historical success
            best_hour = pattern.get("best_hour", datetime.now().hour)

            # Schedule for next occurrence of best hour
            now = datetime.now()
            target_time = now.replace(hour=best_hour, minute=0, second=0, microsecond=0)

            if target_time <= now:
                target_time += timedelta(days=1)

            return target_time.timestamp()

        # Default to immediate execution
        return time.time()

    async def _record_execution_pattern(self, task_def: TaskDefinition, execution: TaskExecution):
        """Record execution pattern for learning."""
        task_type = task_def.parameters.get("task_type", "default")
        execution_hour = datetime.fromtimestamp(execution.start_time).hour
        duration = execution.end_time - execution.start_time
        success = execution.status == TaskStatus.COMPLETED

        pattern_data = {
            "hour": execution_hour,
            "duration": duration,
            "success": success,
            "timestamp": execution.start_time
        }

        self.execution_history.append(pattern_data)

        # Update user patterns
        if task_type not in self.user_patterns:
            self.user_patterns[task_type] = {
                "executions": [],
                "best_hour": execution_hour,
                "avg_duration": duration,
                "success_rate": 1.0 if success else 0.0
            }

        pattern = self.user_patterns[task_type]
        pattern["executions"].append(pattern_data)

        # Keep only recent executions
        pattern["executions"] = pattern["executions"][-100:]

        # Update statistics
        recent_executions = pattern["executions"]
        if recent_executions:
            # Find best hour
            hour_success = {}
            for exec_data in recent_executions:
                hour = exec_data["hour"]
                if hour not in hour_success:
                    hour_success[hour] = {"success": 0, "total": 0}
                hour_success[hour]["total"] += 1
                if exec_data["success"]:
                    hour_success[hour]["success"] += 1

            best_hour = max(hour_success.keys(),
                          key=lambda h: hour_success[h]["success"] / hour_success[h]["total"])
            pattern["best_hour"] = best_hour

            # Update average duration
            durations = [e["duration"] for e in recent_executions if e["success"]]
            if durations:
                pattern["avg_duration"] = sum(durations) / len(durations)

            # Update success rate
            successes = sum(1 for e in recent_executions if e["success"])
            pattern["success_rate"] = successes / len(recent_executions)

    async def _learn_from_patterns(self):
        """Learn from execution patterns and optimize scheduling."""
        # This would implement more sophisticated learning algorithms
        # For now, just update basic patterns
        pass

    async def _update_metrics(self):
        """Update scheduler metrics."""
        if self.completed_tasks:
            durations = []
            for execution in self.completed_tasks.values():
                if execution.start_time and execution.end_time:
                    durations.append(execution.end_time - execution.start_time)

            if durations:
                self.metrics.average_execution_time = sum(durations) / len(durations)

        # Calculate throughput (tasks per minute)
        if self.execution_history:
            recent_executions = [e for e in self.execution_history
                               if time.time() - e["timestamp"] < 3600]  # Last hour
            self.metrics.throughput = len(recent_executions) / 60.0  # Per minute

        # Update resource utilization
        for resource in self.available_resources:
            utilization = (100.0 - self.available_resources[resource]) / 100.0
            self.metrics.resource_utilization[resource] = utilization

    async def _cleanup_old_tasks(self):
        """Clean up old completed and failed tasks."""
        cutoff_time = time.time() - 86400  # 24 hours

        # Clean completed tasks
        old_completed = [tid for tid, execution in self.completed_tasks.items()
                        if execution.end_time and execution.end_time < cutoff_time]
        for tid in old_completed:
            del self.completed_tasks[tid]

        # Clean failed tasks
        old_failed = [tid for tid, execution in self.failed_tasks.items()
                     if execution.end_time and execution.end_time < cutoff_time]
        for tid in old_failed:
            del self.failed_tasks[tid]

        # Clean execution history
        self.execution_history = [e for e in self.execution_history
                                 if time.time() - e["timestamp"] < 604800]  # 7 days

    async def _optimize_resources(self):
        """Optimize resource allocation and scheduling."""
        # Gradually restore resources (simulate system recovery)
        for resource in self.available_resources:
            if self.available_resources[resource] < 100.0:
                self.available_resources[resource] = min(100.0,
                    self.available_resources[resource] + 1.0)

    def _register_builtin_executors(self):
        """Register built-in task executors."""
        self.task_executors = {
            "voice_command": self._execute_voice_command,
            "file_operation": self._execute_file_operation,
            "system_command": self._execute_system_command,
            "automation": self._execute_automation,
            "analytics": self._execute_analytics,
            "default": self._default_executor
        }

    async def _default_executor(self, task_def: TaskDefinition, execution: TaskExecution) -> Any:
        """Default task executor."""
        # Simulate task execution
        await asyncio.sleep(1)
        return {"status": "completed", "message": f"Executed task {task_def.name}"}

    async def _execute_voice_command(self, task_def: TaskDefinition, execution: TaskExecution) -> Any:
        """Execute voice command task."""
        command = task_def.parameters.get("command", "")

        # Simulate voice command execution
        await asyncio.sleep(0.5)

        return {
            "status": "completed",
            "command": command,
            "result": f"Voice command '{command}' executed successfully"
        }

    async def _execute_file_operation(self, task_def: TaskDefinition, execution: TaskExecution) -> Any:
        """Execute file operation task."""
        operation = task_def.parameters.get("operation", "")
        file_path = task_def.parameters.get("file_path", "")

        # Simulate file operation
        await asyncio.sleep(1)

        return {
            "status": "completed",
            "operation": operation,
            "file_path": file_path,
            "result": f"File operation '{operation}' completed on {file_path}"
        }

    async def _execute_system_command(self, task_def: TaskDefinition, execution: TaskExecution) -> Any:
        """Execute system command task."""
        command = task_def.parameters.get("command", "")

        # Simulate system command execution
        await asyncio.sleep(2)

        return {
            "status": "completed",
            "command": command,
            "result": f"System command '{command}' executed"
        }

    async def _execute_automation(self, task_def: TaskDefinition, execution: TaskExecution) -> Any:
        """Execute automation task."""
        automation_type = task_def.parameters.get("automation_type", "")

        # Simulate automation execution
        await asyncio.sleep(3)

        return {
            "status": "completed",
            "automation_type": automation_type,
            "result": f"Automation '{automation_type}' completed"
        }

    async def _execute_analytics(self, task_def: TaskDefinition, execution: TaskExecution) -> Any:
        """Execute analytics task."""
        analytics_type = task_def.parameters.get("analytics_type", "")

        # Simulate analytics execution
        await asyncio.sleep(2)

        return {
            "status": "completed",
            "analytics_type": analytics_type,
            "result": f"Analytics '{analytics_type}' completed"
        }

    async def _save_scheduler_state(self):
        """Save scheduler state to disk."""
        state_file = Path("data/scheduler_state.json")
        state_file.parent.mkdir(parents=True, exist_ok=True)

        state = {
            "user_patterns": self.user_patterns,
            "execution_history": self.execution_history[-1000:],  # Keep last 1000
            "metrics": asdict(self.metrics),
            "next_task_id": self.next_task_id
        }

        try:
            with open(state_file, 'w') as f:
                json.dump(state, f, indent=2)
        except Exception as e:
            print(f"Error saving scheduler state: {e}")

    async def _load_scheduler_state(self):
        """Load scheduler state from disk."""
        state_file = Path("data/scheduler_state.json")

        if not state_file.exists():
            return

        try:
            with open(state_file, 'r') as f:
                state = json.load(f)

            self.user_patterns = state.get("user_patterns", {})
            self.execution_history = state.get("execution_history", [])
            self.next_task_id = state.get("next_task_id", 1)

            # Restore metrics
            metrics_data = state.get("metrics", {})
            for key, value in metrics_data.items():
                if hasattr(self.metrics, key):
                    setattr(self.metrics, key, value)

        except Exception as e:
            print(f"Error loading scheduler state: {e}")


# Utility functions for creating tasks
def create_voice_command_task(command: str, priority: TaskPriority = TaskPriority.NORMAL,
                             schedule_type: ScheduleType = ScheduleType.IMMEDIATE) -> TaskDefinition:
    """Create a voice command task."""
    task_id = f"voice_{int(time.time() * 1000)}"

    return TaskDefinition(
        task_id=task_id,
        name=f"Voice Command: {command}",
        description=f"Execute voice command: {command}",
        priority=priority,
        schedule_type=schedule_type,
        scheduled_time=None,
        recurrence_pattern=None,
        conditions={},
        parameters={"task_type": "voice_command", "command": command},
        estimated_duration=5.0,
        max_retries=2,
        timeout=30.0,
        dependencies=[],
        resource_requirements={"cpu": 5.0, "memory": 5.0}
    )


def create_file_operation_task(operation: str, file_path: str,
                              priority: TaskPriority = TaskPriority.NORMAL) -> TaskDefinition:
    """Create a file operation task."""
    task_id = f"file_{int(time.time() * 1000)}"

    return TaskDefinition(
        task_id=task_id,
        name=f"File Operation: {operation}",
        description=f"Perform {operation} on {file_path}",
        priority=priority,
        schedule_type=ScheduleType.IMMEDIATE,
        scheduled_time=None,
        recurrence_pattern=None,
        conditions={},
        parameters={"task_type": "file_operation", "operation": operation, "file_path": file_path},
        estimated_duration=10.0,
        max_retries=3,
        timeout=60.0,
        dependencies=[],
        resource_requirements={"cpu": 10.0, "memory": 10.0, "disk": 20.0}
    )


def create_recurring_task(name: str, task_type: str, recurrence: str,
                         parameters: Dict[str, Any] = None) -> TaskDefinition:
    """Create a recurring task."""
    task_id = f"recurring_{task_type}_{int(time.time())}"

    return TaskDefinition(
        task_id=task_id,
        name=name,
        description=f"Recurring {task_type} task",
        priority=TaskPriority.NORMAL,
        schedule_type=ScheduleType.RECURRING,
        scheduled_time=None,
        recurrence_pattern=recurrence,
        conditions={},
        parameters={"task_type": task_type, **(parameters or {})},
        estimated_duration=30.0,
        max_retries=3,
        timeout=300.0,
        dependencies=[],
        resource_requirements={"cpu": 15.0, "memory": 15.0}
    )
