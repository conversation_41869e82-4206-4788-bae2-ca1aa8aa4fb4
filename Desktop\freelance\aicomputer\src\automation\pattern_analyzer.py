"""
Behavioral Pattern Analyzer - Phase 3 Component

Advanced analysis of user behavior patterns for automation and optimization.
"""

import asyncio
import time
import json
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
import statistics

from loguru import logger

from ..utils.config_manager import ConfigManager
from ..core.ai_processor import ProcessedCommand, CommandIntent


@dataclass
class TemporalPattern:
    """Temporal behavior pattern."""
    pattern_id: str
    time_type: str  # 'hourly', 'daily', 'weekly'
    peak_times: List[int]
    frequency_distribution: Dict[int, int]
    confidence: float
    last_updated: float


@dataclass
class SequencePattern:
    """Command sequence pattern."""
    pattern_id: str
    sequence: List[str]
    frequency: int
    success_rate: float
    average_duration: float
    context_triggers: List[str]
    optimization_potential: float


@dataclass
class ContextualPattern:
    """Context-based behavior pattern."""
    pattern_id: str
    context_signature: str
    triggered_actions: List[str]
    frequency: int
    context_variables: Dict[str, Any]
    prediction_accuracy: float


@dataclass
class EfficiencyPattern:
    """User efficiency pattern."""
    pattern_id: str
    task_type: str
    average_completion_time: float
    success_rate: float
    common_errors: List[str]
    optimization_suggestions: List[str]
    improvement_potential: float


class BehavioralPatternAnalyzer:
    """
    Advanced behavioral pattern analysis system.
    
    Features:
    - Temporal pattern recognition (time-based behaviors)
    - Command sequence analysis and optimization
    - Contextual behavior mapping
    - Efficiency pattern identification
    - Automation opportunity detection
    - Performance trend analysis
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        
        # Pattern storage
        self.temporal_patterns: Dict[str, TemporalPattern] = {}
        self.sequence_patterns: Dict[str, SequencePattern] = {}
        self.contextual_patterns: Dict[str, ContextualPattern] = {}
        self.efficiency_patterns: Dict[str, EfficiencyPattern] = {}
        
        # Analysis data
        self.command_timeline: List[Dict[str, Any]] = []
        self.session_data: List[Dict[str, Any]] = []
        self.performance_metrics: Dict[str, List[float]] = defaultdict(list)
        
        # Configuration
        self.analysis_window_days = self.config.get("automation.pattern_analysis.window_days", 30)
        self.min_pattern_frequency = self.config.get("automation.pattern_analysis.min_frequency", 3)
        self.confidence_threshold = self.config.get("automation.pattern_analysis.confidence_threshold", 0.6)
        
        # Load existing patterns
        self._load_patterns()
        
        logger.info("Behavioral Pattern Analyzer initialized")
    
    async def analyze_command_behavior(self, command: ProcessedCommand, 
                                     context: Dict[str, Any], 
                                     execution_time: float,
                                     success: bool):
        """
        Analyze a command for behavioral patterns.
        
        Args:
            command: The processed command
            context: Execution context
            execution_time: Time taken to execute
            success: Whether execution was successful
        """
        
        try:
            # Record command data
            command_data = {
                "timestamp": time.time(),
                "intent": command.intent.value,
                "action": command.action,
                "entities": command.entities,
                "execution_time": execution_time,
                "success": success,
                "context": context,
                "hour": datetime.now().hour,
                "day_of_week": datetime.now().weekday(),
                "confidence": command.confidence
            }
            
            self.command_timeline.append(command_data)
            
            # Update performance metrics
            self._update_performance_metrics(command_data)
            
            # Analyze patterns periodically
            if len(self.command_timeline) % 10 == 0:  # Every 10 commands
                await self._analyze_all_patterns()
            
            # Cleanup old data
            self._cleanup_old_data()
            
        except Exception as e:
            logger.error(f"Error analyzing command behavior: {e}")
    
    async def _analyze_all_patterns(self):
        """Analyze all types of behavioral patterns."""
        
        try:
            await asyncio.gather(
                self._analyze_temporal_patterns(),
                self._analyze_sequence_patterns(),
                self._analyze_contextual_patterns(),
                self._analyze_efficiency_patterns()
            )
            
        except Exception as e:
            logger.error(f"Error in pattern analysis: {e}")
    
    async def _analyze_temporal_patterns(self):
        """Analyze temporal behavior patterns."""
        
        try:
            # Hourly patterns
            hourly_data = defaultdict(list)
            for cmd in self.command_timeline:
                hour = cmd["hour"]
                intent = cmd["intent"]
                hourly_data[intent].append(hour)
            
            for intent, hours in hourly_data.items():
                if len(hours) >= self.min_pattern_frequency:
                    pattern_id = f"temporal_hourly_{intent}"
                    
                    # Calculate frequency distribution
                    hour_counts = Counter(hours)
                    peak_hours = [h for h, count in hour_counts.most_common(3)]
                    
                    # Calculate confidence based on concentration
                    total_commands = len(hours)
                    peak_concentration = sum(hour_counts[h] for h in peak_hours) / total_commands
                    confidence = min(peak_concentration * 1.5, 1.0)
                    
                    if confidence >= self.confidence_threshold:
                        pattern = TemporalPattern(
                            pattern_id=pattern_id,
                            time_type="hourly",
                            peak_times=peak_hours,
                            frequency_distribution=dict(hour_counts),
                            confidence=confidence,
                            last_updated=time.time()
                        )
                        self.temporal_patterns[pattern_id] = pattern
            
            # Daily patterns (day of week)
            daily_data = defaultdict(list)
            for cmd in self.command_timeline:
                day = cmd["day_of_week"]
                intent = cmd["intent"]
                daily_data[intent].append(day)
            
            for intent, days in daily_data.items():
                if len(days) >= self.min_pattern_frequency:
                    pattern_id = f"temporal_daily_{intent}"
                    
                    day_counts = Counter(days)
                    peak_days = [d for d, count in day_counts.most_common(2)]
                    
                    total_commands = len(days)
                    peak_concentration = sum(day_counts[d] for d in peak_days) / total_commands
                    confidence = min(peak_concentration * 1.2, 1.0)
                    
                    if confidence >= self.confidence_threshold:
                        pattern = TemporalPattern(
                            pattern_id=pattern_id,
                            time_type="daily",
                            peak_times=peak_days,
                            frequency_distribution=dict(day_counts),
                            confidence=confidence,
                            last_updated=time.time()
                        )
                        self.temporal_patterns[pattern_id] = pattern
                        
        except Exception as e:
            logger.error(f"Error analyzing temporal patterns: {e}")
    
    async def _analyze_sequence_patterns(self):
        """Analyze command sequence patterns."""
        
        try:
            # Group commands into sessions (commands within 5 minutes of each other)
            sessions = self._group_commands_into_sessions()
            
            # Analyze sequences within sessions
            for session in sessions:
                if len(session) >= 2:
                    await self._analyze_session_sequences(session)
                    
        except Exception as e:
            logger.error(f"Error analyzing sequence patterns: {e}")
    
    def _group_commands_into_sessions(self) -> List[List[Dict[str, Any]]]:
        """Group commands into sessions based on time gaps."""
        
        sessions = []
        current_session = []
        session_timeout = 300  # 5 minutes
        
        for cmd in sorted(self.command_timeline, key=lambda x: x["timestamp"]):
            if (not current_session or 
                cmd["timestamp"] - current_session[-1]["timestamp"] <= session_timeout):
                current_session.append(cmd)
            else:
                if current_session:
                    sessions.append(current_session)
                current_session = [cmd]
        
        if current_session:
            sessions.append(current_session)
        
        return sessions
    
    async def _analyze_session_sequences(self, session: List[Dict[str, Any]]):
        """Analyze sequences within a session."""
        
        try:
            # Look for repeating subsequences
            for length in range(2, min(6, len(session) + 1)):
                for start in range(len(session) - length + 1):
                    subseq = session[start:start + length]
                    
                    # Create sequence signature
                    sequence = [cmd["intent"] for cmd in subseq]
                    sequence_str = "_".join(sequence)
                    pattern_id = f"sequence_{hash(sequence_str) % 10000}"
                    
                    # Calculate metrics
                    duration = subseq[-1]["timestamp"] - subseq[0]["timestamp"]
                    success_rate = sum(1 for cmd in subseq if cmd["success"]) / len(subseq)
                    
                    # Extract context triggers
                    context_triggers = []
                    for cmd in subseq:
                        context = cmd.get("context", {})
                        if context:
                            context_triggers.extend(list(context.keys())[:2])
                    
                    if pattern_id in self.sequence_patterns:
                        # Update existing pattern
                        pattern = self.sequence_patterns[pattern_id]
                        pattern.frequency += 1
                        pattern.average_duration = (pattern.average_duration + duration) / 2
                        pattern.success_rate = (pattern.success_rate + success_rate) / 2
                    else:
                        # Create new pattern
                        pattern = SequencePattern(
                            pattern_id=pattern_id,
                            sequence=sequence,
                            frequency=1,
                            success_rate=success_rate,
                            average_duration=duration,
                            context_triggers=list(set(context_triggers)),
                            optimization_potential=self._calculate_optimization_potential(sequence, duration)
                        )
                        self.sequence_patterns[pattern_id] = pattern
                        
        except Exception as e:
            logger.error(f"Error analyzing session sequences: {e}")
    
    def _calculate_optimization_potential(self, sequence: List[str], duration: float) -> float:
        """Calculate optimization potential for a sequence."""
        
        # Simple heuristic: longer sequences with longer durations have higher optimization potential
        sequence_complexity = len(sequence) / 10.0  # Normalize
        duration_factor = min(duration / 60.0, 1.0)  # Normalize to minutes
        
        return min(sequence_complexity + duration_factor, 1.0)
    
    async def _analyze_contextual_patterns(self):
        """Analyze context-based behavior patterns."""
        
        try:
            # Group commands by context similarity
            context_groups = defaultdict(list)
            
            for cmd in self.command_timeline:
                context = cmd.get("context", {})
                if context:
                    # Create context signature from key context elements
                    context_items = sorted(context.items())[:3]  # Top 3 context items
                    context_sig = str(context_items)
                    context_groups[context_sig].append(cmd)
            
            # Analyze each context group
            for context_sig, commands in context_groups.items():
                if len(commands) >= self.min_pattern_frequency:
                    pattern_id = f"contextual_{hash(context_sig) % 10000}"
                    
                    # Extract triggered actions
                    actions = [cmd["intent"] for cmd in commands]
                    action_counts = Counter(actions)
                    
                    # Calculate prediction accuracy (simplified)
                    most_common_action = action_counts.most_common(1)[0][0]
                    prediction_accuracy = action_counts[most_common_action] / len(actions)
                    
                    if prediction_accuracy >= self.confidence_threshold:
                        pattern = ContextualPattern(
                            pattern_id=pattern_id,
                            context_signature=context_sig,
                            triggered_actions=list(action_counts.keys()),
                            frequency=len(commands),
                            context_variables=commands[0].get("context", {}),
                            prediction_accuracy=prediction_accuracy
                        )
                        self.contextual_patterns[pattern_id] = pattern
                        
        except Exception as e:
            logger.error(f"Error analyzing contextual patterns: {e}")
    
    async def _analyze_efficiency_patterns(self):
        """Analyze user efficiency patterns."""
        
        try:
            # Group commands by intent for efficiency analysis
            intent_groups = defaultdict(list)
            
            for cmd in self.command_timeline:
                intent = cmd["intent"]
                intent_groups[intent].append(cmd)
            
            # Analyze efficiency for each intent
            for intent, commands in intent_groups.items():
                if len(commands) >= self.min_pattern_frequency:
                    pattern_id = f"efficiency_{intent}"
                    
                    # Calculate metrics
                    execution_times = [cmd["execution_time"] for cmd in commands]
                    success_rate = sum(1 for cmd in commands if cmd["success"]) / len(commands)
                    avg_time = statistics.mean(execution_times)
                    
                    # Identify common errors (simplified)
                    failed_commands = [cmd for cmd in commands if not cmd["success"]]
                    common_errors = []
                    if failed_commands:
                        # In a real implementation, this would analyze error patterns
                        common_errors = ["timeout", "permission_denied", "file_not_found"]
                    
                    # Generate optimization suggestions
                    optimization_suggestions = self._generate_optimization_suggestions(
                        intent, avg_time, success_rate
                    )
                    
                    # Calculate improvement potential
                    improvement_potential = self._calculate_improvement_potential(
                        avg_time, success_rate, len(commands)
                    )
                    
                    pattern = EfficiencyPattern(
                        pattern_id=pattern_id,
                        task_type=intent,
                        average_completion_time=avg_time,
                        success_rate=success_rate,
                        common_errors=common_errors,
                        optimization_suggestions=optimization_suggestions,
                        improvement_potential=improvement_potential
                    )
                    self.efficiency_patterns[pattern_id] = pattern
                    
        except Exception as e:
            logger.error(f"Error analyzing efficiency patterns: {e}")
    
    def _generate_optimization_suggestions(self, intent: str, avg_time: float, 
                                         success_rate: float) -> List[str]:
        """Generate optimization suggestions for a task type."""
        
        suggestions = []
        
        if avg_time > 5.0:  # Slow execution
            suggestions.append("Consider creating shortcuts for faster access")
            suggestions.append("Optimize command parameters for better performance")
        
        if success_rate < 0.8:  # Low success rate
            suggestions.append("Review command syntax and parameters")
            suggestions.append("Check for common error patterns")
        
        if intent == "file_operation":
            suggestions.append("Consider using batch operations for multiple files")
            suggestions.append("Organize files for easier access")
        elif intent == "app_control":
            suggestions.append("Create application shortcuts or aliases")
            suggestions.append("Consider automating app startup sequences")
        
        return suggestions
    
    def _calculate_improvement_potential(self, avg_time: float, success_rate: float, 
                                       frequency: int) -> float:
        """Calculate improvement potential for a task type."""
        
        # Higher potential for slow, frequently used, or error-prone tasks
        time_factor = min(avg_time / 10.0, 1.0)  # Normalize
        frequency_factor = min(frequency / 50.0, 1.0)  # Normalize
        error_factor = 1.0 - success_rate
        
        return min((time_factor + frequency_factor + error_factor) / 3.0, 1.0)
    
    def _update_performance_metrics(self, command_data: Dict[str, Any]):
        """Update performance metrics with new command data."""
        
        intent = command_data["intent"]
        execution_time = command_data["execution_time"]
        success = command_data["success"]
        
        # Update execution time metrics
        self.performance_metrics[f"{intent}_execution_time"].append(execution_time)
        
        # Update success rate metrics
        self.performance_metrics[f"{intent}_success_rate"].append(1.0 if success else 0.0)
        
        # Update overall metrics
        self.performance_metrics["overall_execution_time"].append(execution_time)
        self.performance_metrics["overall_success_rate"].append(1.0 if success else 0.0)
        
        # Keep metrics size manageable
        for key, values in self.performance_metrics.items():
            if len(values) > 1000:
                self.performance_metrics[key] = values[-500:]
    
    def _cleanup_old_data(self):
        """Clean up old command timeline data."""
        
        cutoff_time = time.time() - (self.analysis_window_days * 24 * 3600)
        
        # Remove old commands
        self.command_timeline = [
            cmd for cmd in self.command_timeline 
            if cmd["timestamp"] > cutoff_time
        ]
        
        # Remove old patterns that haven't been updated
        for pattern_dict in [self.temporal_patterns, self.sequence_patterns, 
                           self.contextual_patterns, self.efficiency_patterns]:
            expired_patterns = [
                pattern_id for pattern_id, pattern in pattern_dict.items()
                if hasattr(pattern, 'last_updated') and 
                time.time() - pattern.last_updated > (7 * 24 * 3600)  # 7 days
            ]
            for pattern_id in expired_patterns:
                del pattern_dict[pattern_id]
    
    def get_pattern_insights(self) -> Dict[str, Any]:
        """Get comprehensive pattern analysis insights."""
        
        try:
            insights = {
                "temporal_insights": self._get_temporal_insights(),
                "sequence_insights": self._get_sequence_insights(),
                "contextual_insights": self._get_contextual_insights(),
                "efficiency_insights": self._get_efficiency_insights(),
                "automation_opportunities": self._get_automation_opportunities()
            }
            
            return insights
            
        except Exception as e:
            logger.error(f"Error getting pattern insights: {e}")
            return {"error": str(e)}
    
    def _get_temporal_insights(self) -> Dict[str, Any]:
        """Get temporal pattern insights."""
        
        insights = {
            "total_temporal_patterns": len(self.temporal_patterns),
            "peak_activity_hours": [],
            "most_active_days": [],
            "temporal_predictions": []
        }
        
        # Find overall peak hours
        all_hourly_data = defaultdict(int)
        for pattern in self.temporal_patterns.values():
            if pattern.time_type == "hourly":
                for hour, count in pattern.frequency_distribution.items():
                    all_hourly_data[hour] += count
        
        if all_hourly_data:
            peak_hours = sorted(all_hourly_data.items(), key=lambda x: x[1], reverse=True)[:3]
            insights["peak_activity_hours"] = [hour for hour, count in peak_hours]
        
        return insights
    
    def _get_sequence_insights(self) -> Dict[str, Any]:
        """Get sequence pattern insights."""
        
        insights = {
            "total_sequence_patterns": len(self.sequence_patterns),
            "most_frequent_sequences": [],
            "optimization_candidates": [],
            "average_sequence_length": 0
        }
        
        if self.sequence_patterns:
            # Most frequent sequences
            frequent_sequences = sorted(
                self.sequence_patterns.values(), 
                key=lambda p: p.frequency, 
                reverse=True
            )[:5]
            
            insights["most_frequent_sequences"] = [
                {"sequence": p.sequence, "frequency": p.frequency} 
                for p in frequent_sequences
            ]
            
            # Optimization candidates
            optimization_candidates = [
                p for p in self.sequence_patterns.values() 
                if p.optimization_potential > 0.7
            ]
            
            insights["optimization_candidates"] = [
                {"sequence": p.sequence, "potential": p.optimization_potential}
                for p in optimization_candidates
            ]
            
            # Average sequence length
            avg_length = statistics.mean([len(p.sequence) for p in self.sequence_patterns.values()])
            insights["average_sequence_length"] = round(avg_length, 2)
        
        return insights
    
    def _get_contextual_insights(self) -> Dict[str, Any]:
        """Get contextual pattern insights."""
        
        insights = {
            "total_contextual_patterns": len(self.contextual_patterns),
            "high_accuracy_patterns": [],
            "context_triggers": []
        }
        
        if self.contextual_patterns:
            # High accuracy patterns
            high_accuracy = [
                p for p in self.contextual_patterns.values() 
                if p.prediction_accuracy > 0.8
            ]
            
            insights["high_accuracy_patterns"] = len(high_accuracy)
            
            # Common context triggers
            all_triggers = []
            for pattern in self.contextual_patterns.values():
                all_triggers.extend(pattern.context_variables.keys())
            
            trigger_counts = Counter(all_triggers)
            insights["context_triggers"] = [
                {"trigger": trigger, "frequency": count}
                for trigger, count in trigger_counts.most_common(5)
            ]
        
        return insights
    
    def _get_efficiency_insights(self) -> Dict[str, Any]:
        """Get efficiency pattern insights."""
        
        insights = {
            "total_efficiency_patterns": len(self.efficiency_patterns),
            "improvement_opportunities": [],
            "performance_trends": {}
        }
        
        if self.efficiency_patterns:
            # Improvement opportunities
            improvement_ops = [
                {
                    "task_type": p.task_type,
                    "improvement_potential": p.improvement_potential,
                    "suggestions": p.optimization_suggestions[:2]
                }
                for p in self.efficiency_patterns.values()
                if p.improvement_potential > 0.6
            ]
            
            insights["improvement_opportunities"] = improvement_ops
            
            # Performance trends (simplified)
            for intent, times in self.performance_metrics.items():
                if intent.endswith("_execution_time") and len(times) > 10:
                    recent_avg = statistics.mean(times[-10:])
                    overall_avg = statistics.mean(times)
                    trend = "improving" if recent_avg < overall_avg else "stable"
                    insights["performance_trends"][intent.replace("_execution_time", "")] = trend
        
        return insights
    
    def _get_automation_opportunities(self) -> List[Dict[str, Any]]:
        """Identify automation opportunities from patterns."""
        
        opportunities = []
        
        # High-frequency sequences
        for pattern in self.sequence_patterns.values():
            if pattern.frequency >= 5 and pattern.optimization_potential > 0.6:
                opportunities.append({
                    "type": "workflow_automation",
                    "description": f"Automate sequence: {' -> '.join(pattern.sequence)}",
                    "frequency": pattern.frequency,
                    "potential_benefit": pattern.optimization_potential
                })
        
        # Predictable temporal patterns
        for pattern in self.temporal_patterns.values():
            if pattern.confidence > 0.8:
                opportunities.append({
                    "type": "scheduled_automation",
                    "description": f"Schedule {pattern.pattern_id.split('_')[-1]} for peak times",
                    "confidence": pattern.confidence,
                    "potential_benefit": 0.7
                })
        
        return opportunities[:10]  # Top 10 opportunities
    
    def _load_patterns(self):
        """Load patterns from storage."""
        
        try:
            data_dir = Path("data/automation")
            
            # Load each pattern type
            pattern_files = {
                "temporal_patterns.json": self.temporal_patterns,
                "sequence_patterns.json": self.sequence_patterns,
                "contextual_patterns.json": self.contextual_patterns,
                "efficiency_patterns.json": self.efficiency_patterns
            }
            
            for filename, pattern_dict in pattern_files.items():
                file_path = data_dir / filename
                if file_path.exists():
                    with open(file_path, "r") as f:
                        data = json.load(f)
                        # Convert back to dataclass objects
                        for pattern_id, pattern_data in data.items():
                            if "temporal" in filename:
                                pattern_dict[pattern_id] = TemporalPattern(**pattern_data)
                            elif "sequence" in filename:
                                pattern_dict[pattern_id] = SequencePattern(**pattern_data)
                            elif "contextual" in filename:
                                pattern_dict[pattern_id] = ContextualPattern(**pattern_data)
                            elif "efficiency" in filename:
                                pattern_dict[pattern_id] = EfficiencyPattern(**pattern_data)
            
            logger.info("Behavioral patterns loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading patterns: {e}")
    
    def save_patterns(self):
        """Save patterns to storage."""
        
        try:
            data_dir = Path("data/automation")
            data_dir.mkdir(parents=True, exist_ok=True)
            
            # Save each pattern type
            pattern_files = {
                "temporal_patterns.json": self.temporal_patterns,
                "sequence_patterns.json": self.sequence_patterns,
                "contextual_patterns.json": self.contextual_patterns,
                "efficiency_patterns.json": self.efficiency_patterns
            }
            
            for filename, pattern_dict in pattern_files.items():
                file_path = data_dir / filename
                with open(file_path, "w") as f:
                    # Convert dataclass objects to dict
                    data = {k: asdict(v) for k, v in pattern_dict.items()}
                    json.dump(data, f, indent=2)
            
            logger.info("Behavioral patterns saved successfully")
            
        except Exception as e:
            logger.error(f"Error saving patterns: {e}")


# Factory functions and aliases
def create_temporal_pattern(pattern_id: str, pattern_type: str, frequency: int) -> TemporalPattern:
    """Create a new temporal pattern."""
    return TemporalPattern(
        pattern_id=pattern_id,
        pattern_type=pattern_type,
        frequency=frequency,
        time_windows=[],
        confidence=0.0,
        last_occurrence=time.time(),
        metadata={}
    )

# Alias for compatibility
PatternAnalyzer = BehavioralPatternAnalyzer
