"""
Predictive Intelligence Engine - Phase 3 Component

Advanced predictive capabilities for proactive assistance and intelligent automation.
"""

import asyncio
import json
import time
import pickle
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
from datetime import datetime, timedelta
import numpy as np
from pathlib import Path

from loguru import logger

from ..utils.config_manager import ConfigManager
from ..core.ai_processor import ProcessedCommand, CommandIntent


@dataclass
class Prediction:
    """Prediction data structure."""
    prediction_id: str
    prediction_type: str
    confidence: float
    predicted_action: str
    context: Dict[str, Any]
    timestamp: float
    expires_at: float
    metadata: Dict[str, Any] = None


@dataclass
class BehaviorPattern:
    """User behavior pattern."""
    pattern_id: str
    pattern_type: str
    triggers: List[str]
    actions: List[str]
    frequency: int
    success_rate: float
    time_patterns: Dict[str, Any]
    context_patterns: Dict[str, Any]
    last_occurrence: float


@dataclass
class ProactiveAction:
    """Proactive action recommendation."""
    action_id: str
    action_type: str
    description: str
    priority: int
    confidence: float
    estimated_benefit: float
    prerequisites: List[str]
    execution_time: float


class PredictiveIntelligenceEngine:
    """
    Advanced predictive intelligence engine for proactive assistance.
    
    Features:
    - Behavioral pattern recognition and prediction
    - Proactive action recommendations
    - Context-aware predictions
    - Time-based pattern analysis
    - Intelligent automation suggestions
    - Performance optimization predictions
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        
        # Prediction storage
        self.active_predictions: Dict[str, Prediction] = {}
        self.behavior_patterns: Dict[str, BehaviorPattern] = {}
        self.proactive_actions: List[ProactiveAction] = []
        
        # Analysis data
        self.command_sequences: List[List[Dict[str, Any]]] = []
        self.time_patterns: Dict[str, List[float]] = defaultdict(list)
        self.context_patterns: Dict[str, Dict[str, Any]] = {}
        
        # Prediction models
        self.sequence_model: Optional[Any] = None
        self.time_model: Optional[Any] = None
        self.context_model: Optional[Any] = None
        
        # Configuration
        self.prediction_horizon = self.config.get("automation.prediction.horizon_minutes", 60)
        self.min_pattern_frequency = self.config.get("automation.prediction.min_frequency", 5)
        self.confidence_threshold = self.config.get("automation.prediction.confidence_threshold", 0.7)
        self.max_predictions = self.config.get("automation.prediction.max_active", 20)
        
        # Load existing patterns
        self._load_behavior_patterns()
        
        logger.info("Predictive Intelligence Engine initialized")
    
    async def analyze_command_for_prediction(self, command: ProcessedCommand, 
                                           context: Dict[str, Any]) -> List[Prediction]:
        """
        Analyze a command and generate predictions for future actions.
        
        Args:
            command: The processed command
            context: Current system context
            
        Returns:
            List of predictions for future actions
        """
        
        try:
            predictions = []
            
            # Update behavior patterns
            await self._update_behavior_patterns(command, context)
            
            # Generate sequence-based predictions
            sequence_predictions = await self._predict_from_sequences(command, context)
            predictions.extend(sequence_predictions)
            
            # Generate time-based predictions
            time_predictions = await self._predict_from_time_patterns(command, context)
            predictions.extend(time_predictions)
            
            # Generate context-based predictions
            context_predictions = await self._predict_from_context(command, context)
            predictions.extend(context_predictions)
            
            # Filter and rank predictions
            filtered_predictions = self._filter_and_rank_predictions(predictions)
            
            # Store active predictions
            for prediction in filtered_predictions:
                self.active_predictions[prediction.prediction_id] = prediction
            
            # Clean up expired predictions
            self._cleanup_expired_predictions()
            
            return filtered_predictions
            
        except Exception as e:
            logger.error(f"Error in predictive analysis: {e}")
            return []
    
    async def _update_behavior_patterns(self, command: ProcessedCommand, context: Dict[str, Any]):
        """Update behavior patterns based on new command."""
        
        try:
            current_time = time.time()
            hour = datetime.fromtimestamp(current_time).hour
            
            # Create command record
            command_record = {
                "intent": command.intent.value,
                "action": command.action,
                "entities": command.entities,
                "timestamp": current_time,
                "hour": hour,
                "context": context
            }
            
            # Add to current sequence
            if not self.command_sequences or len(self.command_sequences[-1]) >= 10:
                self.command_sequences.append([])
            
            self.command_sequences[-1].append(command_record)
            
            # Update time patterns
            intent_key = command.intent.value
            self.time_patterns[intent_key].append(current_time)
            
            # Keep only recent time data
            cutoff_time = current_time - (30 * 24 * 3600)  # 30 days
            self.time_patterns[intent_key] = [
                t for t in self.time_patterns[intent_key] if t > cutoff_time
            ]
            
            # Analyze for new patterns
            await self._detect_new_patterns()
            
        except Exception as e:
            logger.error(f"Error updating behavior patterns: {e}")
    
    async def _detect_new_patterns(self):
        """Detect new behavior patterns from recent data."""
        
        try:
            # Analyze command sequences for patterns
            for sequence in self.command_sequences[-10:]:  # Recent sequences
                if len(sequence) >= 3:
                    await self._analyze_sequence_pattern(sequence)
            
            # Analyze time patterns
            await self._analyze_time_patterns()
            
            # Analyze context patterns
            await self._analyze_context_patterns()
            
        except Exception as e:
            logger.error(f"Error detecting new patterns: {e}")
    
    async def _analyze_sequence_pattern(self, sequence: List[Dict[str, Any]]):
        """Analyze a command sequence for patterns."""
        
        try:
            if len(sequence) < 3:
                return
            
            # Look for repeating subsequences
            for length in range(2, min(5, len(sequence))):
                for start in range(len(sequence) - length + 1):
                    subseq = sequence[start:start + length]
                    
                    # Create pattern signature
                    pattern_sig = "_".join([cmd["intent"] for cmd in subseq])
                    pattern_id = f"seq_{hash(pattern_sig) % 10000}"
                    
                    if pattern_id in self.behavior_patterns:
                        # Update existing pattern
                        pattern = self.behavior_patterns[pattern_id]
                        pattern.frequency += 1
                        pattern.last_occurrence = time.time()
                    else:
                        # Create new pattern
                        pattern = BehaviorPattern(
                            pattern_id=pattern_id,
                            pattern_type="sequence",
                            triggers=[cmd["intent"] for cmd in subseq[:-1]],
                            actions=[subseq[-1]["intent"]],
                            frequency=1,
                            success_rate=1.0,
                            time_patterns={},
                            context_patterns={},
                            last_occurrence=time.time()
                        )
                        self.behavior_patterns[pattern_id] = pattern
                        
        except Exception as e:
            logger.error(f"Error analyzing sequence pattern: {e}")
    
    async def _analyze_time_patterns(self):
        """Analyze time-based patterns in user behavior."""
        
        try:
            current_time = time.time()
            
            for intent, timestamps in self.time_patterns.items():
                if len(timestamps) < self.min_pattern_frequency:
                    continue
                
                # Analyze hourly patterns
                hours = [datetime.fromtimestamp(ts).hour for ts in timestamps]
                hour_counts = Counter(hours)
                
                # Find peak hours
                if hour_counts:
                    peak_hour = hour_counts.most_common(1)[0][0]
                    pattern_id = f"time_{intent}_{peak_hour}"
                    
                    if pattern_id not in self.behavior_patterns:
                        pattern = BehaviorPattern(
                            pattern_id=pattern_id,
                            pattern_type="temporal",
                            triggers=[f"hour_{peak_hour}"],
                            actions=[intent],
                            frequency=hour_counts[peak_hour],
                            success_rate=1.0,
                            time_patterns={"peak_hour": peak_hour, "frequency": hour_counts[peak_hour]},
                            context_patterns={},
                            last_occurrence=current_time
                        )
                        self.behavior_patterns[pattern_id] = pattern
                        
        except Exception as e:
            logger.error(f"Error analyzing time patterns: {e}")
    
    async def _analyze_context_patterns(self):
        """Analyze context-based patterns."""
        
        try:
            # Analyze recent command sequences for context patterns
            for sequence in self.command_sequences[-20:]:
                for i, cmd in enumerate(sequence):
                    context = cmd.get("context", {})
                    
                    # Look for context triggers
                    if context and i < len(sequence) - 1:
                        next_cmd = sequence[i + 1]
                        
                        # Create context pattern
                        context_key = str(sorted(context.items())[:3])  # Top 3 context items
                        pattern_id = f"ctx_{hash(context_key) % 10000}_{next_cmd['intent']}"
                        
                        if pattern_id not in self.behavior_patterns:
                            pattern = BehaviorPattern(
                                pattern_id=pattern_id,
                                pattern_type="contextual",
                                triggers=[context_key],
                                actions=[next_cmd["intent"]],
                                frequency=1,
                                success_rate=1.0,
                                time_patterns={},
                                context_patterns=context,
                                last_occurrence=time.time()
                            )
                            self.behavior_patterns[pattern_id] = pattern
                            
        except Exception as e:
            logger.error(f"Error analyzing context patterns: {e}")
    
    async def _predict_from_sequences(self, command: ProcessedCommand, 
                                    context: Dict[str, Any]) -> List[Prediction]:
        """Generate predictions based on command sequences."""
        
        predictions = []
        
        try:
            current_intent = command.intent.value
            
            # Look for patterns that start with current command
            for pattern in self.behavior_patterns.values():
                if (pattern.pattern_type == "sequence" and 
                    pattern.triggers and 
                    pattern.triggers[-1] == current_intent and
                    pattern.frequency >= self.min_pattern_frequency):
                    
                    confidence = min(pattern.frequency / 10.0, 0.95)
                    
                    if confidence >= self.confidence_threshold:
                        prediction = Prediction(
                            prediction_id=f"seq_pred_{int(time.time())}_{pattern.pattern_id}",
                            prediction_type="sequence",
                            confidence=confidence,
                            predicted_action=pattern.actions[0],
                            context={"pattern_id": pattern.pattern_id, "trigger_sequence": pattern.triggers},
                            timestamp=time.time(),
                            expires_at=time.time() + (self.prediction_horizon * 60),
                            metadata={"frequency": pattern.frequency, "success_rate": pattern.success_rate}
                        )
                        predictions.append(prediction)
                        
        except Exception as e:
            logger.error(f"Error predicting from sequences: {e}")
        
        return predictions
    
    async def _predict_from_time_patterns(self, command: ProcessedCommand, 
                                        context: Dict[str, Any]) -> List[Prediction]:
        """Generate predictions based on time patterns."""
        
        predictions = []
        
        try:
            current_hour = datetime.now().hour
            
            # Look for time-based patterns
            for pattern in self.behavior_patterns.values():
                if (pattern.pattern_type == "temporal" and 
                    pattern.time_patterns.get("peak_hour") == current_hour and
                    pattern.frequency >= self.min_pattern_frequency):
                    
                    confidence = min(pattern.frequency / 20.0, 0.9)
                    
                    if confidence >= self.confidence_threshold:
                        prediction = Prediction(
                            prediction_id=f"time_pred_{int(time.time())}_{pattern.pattern_id}",
                            prediction_type="temporal",
                            confidence=confidence,
                            predicted_action=pattern.actions[0],
                            context={"pattern_id": pattern.pattern_id, "peak_hour": current_hour},
                            timestamp=time.time(),
                            expires_at=time.time() + (self.prediction_horizon * 60),
                            metadata={"peak_hour": current_hour, "frequency": pattern.frequency}
                        )
                        predictions.append(prediction)
                        
        except Exception as e:
            logger.error(f"Error predicting from time patterns: {e}")
        
        return predictions
    
    async def _predict_from_context(self, command: ProcessedCommand, 
                                  context: Dict[str, Any]) -> List[Prediction]:
        """Generate predictions based on context patterns."""
        
        predictions = []
        
        try:
            if not context:
                return predictions
            
            context_key = str(sorted(context.items())[:3])
            
            # Look for context-based patterns
            for pattern in self.behavior_patterns.values():
                if (pattern.pattern_type == "contextual" and 
                    pattern.triggers and 
                    context_key in pattern.triggers[0] and
                    pattern.frequency >= self.min_pattern_frequency):
                    
                    confidence = min(pattern.frequency / 15.0, 0.85)
                    
                    if confidence >= self.confidence_threshold:
                        prediction = Prediction(
                            prediction_id=f"ctx_pred_{int(time.time())}_{pattern.pattern_id}",
                            prediction_type="contextual",
                            confidence=confidence,
                            predicted_action=pattern.actions[0],
                            context={"pattern_id": pattern.pattern_id, "context_trigger": context_key},
                            timestamp=time.time(),
                            expires_at=time.time() + (self.prediction_horizon * 60),
                            metadata={"context_match": True, "frequency": pattern.frequency}
                        )
                        predictions.append(prediction)
                        
        except Exception as e:
            logger.error(f"Error predicting from context: {e}")
        
        return predictions
    
    def _filter_and_rank_predictions(self, predictions: List[Prediction]) -> List[Prediction]:
        """Filter and rank predictions by confidence and relevance."""
        
        # Remove duplicates
        unique_predictions = {}
        for pred in predictions:
            key = f"{pred.prediction_type}_{pred.predicted_action}"
            if key not in unique_predictions or pred.confidence > unique_predictions[key].confidence:
                unique_predictions[key] = pred
        
        # Sort by confidence
        sorted_predictions = sorted(unique_predictions.values(), key=lambda p: p.confidence, reverse=True)
        
        # Return top predictions
        return sorted_predictions[:self.max_predictions]
    
    def _cleanup_expired_predictions(self):
        """Remove expired predictions."""
        
        current_time = time.time()
        expired_ids = [
            pred_id for pred_id, pred in self.active_predictions.items()
            if pred.expires_at < current_time
        ]
        
        for pred_id in expired_ids:
            del self.active_predictions[pred_id]
    
    async def generate_proactive_actions(self, current_context: Dict[str, Any]) -> List[ProactiveAction]:
        """Generate proactive action recommendations."""
        
        try:
            actions = []
            
            # Analyze current predictions for proactive opportunities
            for prediction in self.active_predictions.values():
                if prediction.confidence >= 0.8:  # High confidence predictions
                    action = await self._create_proactive_action(prediction, current_context)
                    if action:
                        actions.append(action)
            
            # Generate optimization suggestions
            optimization_actions = await self._generate_optimization_actions(current_context)
            actions.extend(optimization_actions)
            
            # Generate maintenance suggestions
            maintenance_actions = await self._generate_maintenance_actions(current_context)
            actions.extend(maintenance_actions)
            
            # Sort by priority and confidence
            actions.sort(key=lambda a: (a.priority, a.confidence), reverse=True)
            
            return actions[:10]  # Top 10 actions
            
        except Exception as e:
            logger.error(f"Error generating proactive actions: {e}")
            return []
    
    async def _create_proactive_action(self, prediction: Prediction, 
                                     context: Dict[str, Any]) -> Optional[ProactiveAction]:
        """Create a proactive action from a prediction."""
        
        try:
            action_id = f"proactive_{prediction.prediction_id}"
            
            # Determine action based on predicted intent
            if prediction.predicted_action == "file_operation":
                return ProactiveAction(
                    action_id=action_id,
                    action_type="file_preparation",
                    description="Prepare file operations workspace",
                    priority=3,
                    confidence=prediction.confidence,
                    estimated_benefit=0.7,
                    prerequisites=[],
                    execution_time=2.0
                )
            elif prediction.predicted_action == "app_control":
                return ProactiveAction(
                    action_id=action_id,
                    action_type="app_preload",
                    description="Preload frequently used applications",
                    priority=2,
                    confidence=prediction.confidence,
                    estimated_benefit=0.8,
                    prerequisites=[],
                    execution_time=5.0
                )
            elif prediction.predicted_action == "system_info":
                return ProactiveAction(
                    action_id=action_id,
                    action_type="system_monitoring",
                    description="Prepare system information dashboard",
                    priority=1,
                    confidence=prediction.confidence,
                    estimated_benefit=0.6,
                    prerequisites=[],
                    execution_time=1.0
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error creating proactive action: {e}")
            return None
    
    async def _generate_optimization_actions(self, context: Dict[str, Any]) -> List[ProactiveAction]:
        """Generate optimization-based proactive actions."""
        
        actions = []
        
        try:
            # Suggest workflow optimizations
            if len(self.command_sequences) > 5:
                actions.append(ProactiveAction(
                    action_id="optimize_workflows",
                    action_type="workflow_optimization",
                    description="Optimize frequently used command sequences",
                    priority=4,
                    confidence=0.8,
                    estimated_benefit=0.9,
                    prerequisites=[],
                    execution_time=10.0
                ))
            
            # Suggest shortcut creation
            frequent_patterns = [p for p in self.behavior_patterns.values() if p.frequency >= 10]
            if frequent_patterns:
                actions.append(ProactiveAction(
                    action_id="create_shortcuts",
                    action_type="shortcut_creation",
                    description="Create shortcuts for frequent command patterns",
                    priority=3,
                    confidence=0.75,
                    estimated_benefit=0.8,
                    prerequisites=[],
                    execution_time=5.0
                ))
                
        except Exception as e:
            logger.error(f"Error generating optimization actions: {e}")
        
        return actions
    
    async def _generate_maintenance_actions(self, context: Dict[str, Any]) -> List[ProactiveAction]:
        """Generate maintenance-based proactive actions."""
        
        actions = []
        
        try:
            current_time = time.time()
            
            # Suggest data cleanup
            if len(self.command_sequences) > 100:
                actions.append(ProactiveAction(
                    action_id="cleanup_data",
                    action_type="data_maintenance",
                    description="Clean up old command history and optimize storage",
                    priority=2,
                    confidence=0.9,
                    estimated_benefit=0.7,
                    prerequisites=[],
                    execution_time=3.0
                ))
            
            # Suggest pattern analysis update
            last_analysis = context.get("last_pattern_analysis", 0)
            if current_time - last_analysis > 86400:  # 24 hours
                actions.append(ProactiveAction(
                    action_id="update_patterns",
                    action_type="pattern_analysis",
                    description="Update behavior pattern analysis",
                    priority=1,
                    confidence=0.85,
                    estimated_benefit=0.8,
                    prerequisites=[],
                    execution_time=5.0
                ))
                
        except Exception as e:
            logger.error(f"Error generating maintenance actions: {e}")
        
        return actions
    
    def get_prediction_statistics(self) -> Dict[str, Any]:
        """Get comprehensive prediction statistics."""
        
        try:
            total_patterns = len(self.behavior_patterns)
            active_predictions = len(self.active_predictions)
            
            # Pattern type distribution
            pattern_types = Counter([p.pattern_type for p in self.behavior_patterns.values()])
            
            # Prediction accuracy (simplified)
            high_confidence_predictions = len([p for p in self.active_predictions.values() if p.confidence >= 0.8])
            
            return {
                "total_behavior_patterns": total_patterns,
                "active_predictions": active_predictions,
                "high_confidence_predictions": high_confidence_predictions,
                "pattern_type_distribution": dict(pattern_types),
                "command_sequences_analyzed": len(self.command_sequences),
                "prediction_horizon_minutes": self.prediction_horizon,
                "average_pattern_frequency": np.mean([p.frequency for p in self.behavior_patterns.values()]) if self.behavior_patterns else 0
            }
            
        except Exception as e:
            logger.error(f"Error getting prediction statistics: {e}")
            return {"error": str(e)}
    
    def _load_behavior_patterns(self):
        """Load behavior patterns from storage."""
        
        try:
            patterns_file = Path("data/automation/behavior_patterns.pkl")
            if patterns_file.exists():
                with open(patterns_file, "rb") as f:
                    self.behavior_patterns = pickle.load(f)
                logger.info("Behavior patterns loaded successfully")
        except Exception as e:
            logger.error(f"Error loading behavior patterns: {e}")
    
    def save_behavior_patterns(self):
        """Save behavior patterns to storage."""
        
        try:
            data_dir = Path("data/automation")
            data_dir.mkdir(parents=True, exist_ok=True)
            
            with open(data_dir / "behavior_patterns.pkl", "wb") as f:
                pickle.dump(self.behavior_patterns, f)
            
            logger.info("Behavior patterns saved successfully")
        except Exception as e:
            logger.error(f"Error saving behavior patterns: {e}")
