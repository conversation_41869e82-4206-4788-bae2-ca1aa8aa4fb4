"""
Proactive Assistant System - Phase 3 Component

Intelligent proactive assistance that anticipates user needs and provides contextual help.
"""

import asyncio
import time
import json
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path

from loguru import logger

from ..utils.config_manager import ConfigManager
from .predictive_intelligence import PredictiveIntelligenceEngine, Prediction, ProactiveAction


class AssistanceType(Enum):
    """Types of proactive assistance."""
    SUGGESTION = "suggestion"
    WARNING = "warning"
    OPTIMIZATION = "optimization"
    AUTOMATION = "automation"
    INFORMATION = "information"


class Priority(Enum):
    """Priority levels for assistance."""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5


@dataclass
class AssistanceItem:
    """Proactive assistance item."""
    item_id: str
    assistance_type: AssistanceType
    priority: Priority
    title: str
    description: str
    action_required: bool
    auto_executable: bool
    confidence: float
    estimated_benefit: float
    context: Dict[str, Any]
    created_at: float
    expires_at: Optional[float] = None
    executed: bool = False
    user_feedback: Optional[str] = None


@dataclass
class AssistanceRule:
    """Rule for generating proactive assistance."""
    rule_id: str
    name: str
    description: str
    triggers: List[str]
    conditions: Dict[str, Any]
    assistance_template: Dict[str, Any]
    enabled: bool
    success_rate: float
    usage_count: int


@dataclass
class AssistanceSuggestion:
    """Assistance suggestion data structure (alias for AssistanceItem)."""
    suggestion_id: str
    suggestion_type: str
    description: str
    confidence: float
    priority: Priority
    context: Dict[str, Any]
    actions: List[str]
    estimated_time: float
    created_at: float


class ProactiveAssistantSystem:
    """
    Intelligent proactive assistant that anticipates user needs.
    
    Features:
    - Contextual assistance recommendations
    - Predictive help and suggestions
    - Automated task optimization
    - Intelligent warnings and alerts
    - Adaptive assistance based on user behavior
    - Learning from user feedback
    """
    
    def __init__(self, config_manager: ConfigManager, 
                 predictive_engine: PredictiveIntelligenceEngine):
        self.config = config_manager
        self.predictive_engine = predictive_engine
        
        # Assistance storage
        self.active_assistance: Dict[str, AssistanceItem] = {}
        self.assistance_history: List[AssistanceItem] = []
        self.assistance_rules: Dict[str, AssistanceRule] = {}
        
        # User preferences
        self.user_preferences: Dict[str, Any] = {}
        self.feedback_history: List[Dict[str, Any]] = []
        
        # Configuration
        self.max_active_assistance = self.config.get("automation.assistant.max_active", 10)
        self.assistance_timeout = self.config.get("automation.assistant.timeout_minutes", 30)
        self.auto_execute_threshold = self.config.get("automation.assistant.auto_execute_threshold", 0.9)
        
        # Callbacks
        self.on_assistance_generated: Optional[Callable[[AssistanceItem], None]] = None
        self.on_assistance_executed: Optional[Callable[[AssistanceItem, bool], None]] = None
        self.on_user_feedback: Optional[Callable[[AssistanceItem, str], None]] = None
        
        # Initialize assistance rules
        self._initialize_assistance_rules()
        
        # Load user preferences
        self._load_user_preferences()
        
        logger.info("Proactive Assistant System initialized")
    
    def _initialize_assistance_rules(self):
        """Initialize predefined assistance rules."""
        
        # File organization assistance
        self.assistance_rules["file_organization"] = AssistanceRule(
            rule_id="file_organization",
            name="File Organization Assistant",
            description="Suggests file organization when desktop/downloads get cluttered",
            triggers=["file_operation", "list_files"],
            conditions={"file_count_threshold": 20, "location": ["desktop", "downloads"]},
            assistance_template={
                "type": AssistanceType.SUGGESTION,
                "priority": Priority.MEDIUM,
                "title": "Organize Files",
                "description": "Your {location} has many files. Would you like me to organize them by type?",
                "auto_executable": False
            },
            enabled=True,
            success_rate=0.8,
            usage_count=0
        )
        
        # Performance optimization assistance
        self.assistance_rules["performance_optimization"] = AssistanceRule(
            rule_id="performance_optimization",
            name="Performance Optimization",
            description="Suggests optimizations when system performance is low",
            triggers=["system_info", "slow_response"],
            conditions={"cpu_threshold": 80, "memory_threshold": 85},
            assistance_template={
                "type": AssistanceType.WARNING,
                "priority": Priority.HIGH,
                "title": "System Performance Warning",
                "description": "System resources are running high. Consider closing unused applications.",
                "auto_executable": False
            },
            enabled=True,
            success_rate=0.9,
            usage_count=0
        )
        
        # Workflow automation assistance
        self.assistance_rules["workflow_automation"] = AssistanceRule(
            rule_id="workflow_automation",
            name="Workflow Automation",
            description="Suggests workflow creation for repeated command sequences",
            triggers=["repeated_sequence"],
            conditions={"sequence_frequency": 3, "sequence_length": 3},
            assistance_template={
                "type": AssistanceType.OPTIMIZATION,
                "priority": Priority.MEDIUM,
                "title": "Create Workflow",
                "description": "You've performed this sequence {frequency} times. Create a workflow?",
                "auto_executable": False
            },
            enabled=True,
            success_rate=0.7,
            usage_count=0
        )
        
        # Backup reminder assistance
        self.assistance_rules["backup_reminder"] = AssistanceRule(
            rule_id="backup_reminder",
            name="Backup Reminder",
            description="Reminds user to backup important files",
            triggers=["file_creation", "file_modification"],
            conditions={"days_since_backup": 7, "important_files_modified": 5},
            assistance_template={
                "type": AssistanceType.INFORMATION,
                "priority": Priority.MEDIUM,
                "title": "Backup Reminder",
                "description": "You've modified several important files. Consider creating a backup.",
                "auto_executable": False
            },
            enabled=True,
            success_rate=0.6,
            usage_count=0
        )
        
        # Shortcut suggestion assistance
        self.assistance_rules["shortcut_suggestion"] = AssistanceRule(
            rule_id="shortcut_suggestion",
            name="Shortcut Suggestion",
            description="Suggests creating shortcuts for frequently used commands",
            triggers=["frequent_command"],
            conditions={"command_frequency": 10, "command_length": 5},
            assistance_template={
                "type": AssistanceType.OPTIMIZATION,
                "priority": Priority.LOW,
                "title": "Create Shortcut",
                "description": "Create a shortcut for '{command}' to save time?",
                "auto_executable": True
            },
            enabled=True,
            success_rate=0.8,
            usage_count=0
        )
    
    async def analyze_for_assistance(self, context: Dict[str, Any], 
                                   predictions: List[Prediction]) -> List[AssistanceItem]:
        """
        Analyze current context and predictions to generate assistance.
        
        Args:
            context: Current system context
            predictions: Active predictions from predictive engine
            
        Returns:
            List of assistance items
        """
        
        try:
            assistance_items = []
            
            # Generate assistance from predictions
            prediction_assistance = await self._generate_prediction_assistance(predictions, context)
            assistance_items.extend(prediction_assistance)
            
            # Generate assistance from rules
            rule_assistance = await self._generate_rule_assistance(context)
            assistance_items.extend(rule_assistance)
            
            # Generate proactive suggestions
            proactive_assistance = await self._generate_proactive_assistance(context)
            assistance_items.extend(proactive_assistance)
            
            # Filter and prioritize assistance
            filtered_assistance = self._filter_and_prioritize_assistance(assistance_items)
            
            # Store active assistance
            for item in filtered_assistance:
                self.active_assistance[item.item_id] = item
                
                # Trigger callback
                if self.on_assistance_generated:
                    self.on_assistance_generated(item)
            
            # Auto-execute high-confidence items
            await self._auto_execute_assistance()
            
            # Clean up expired assistance
            self._cleanup_expired_assistance()
            
            return filtered_assistance
            
        except Exception as e:
            logger.error(f"Error analyzing for assistance: {e}")
            return []
    
    async def _generate_prediction_assistance(self, predictions: List[Prediction], 
                                            context: Dict[str, Any]) -> List[AssistanceItem]:
        """Generate assistance based on predictions."""
        
        assistance_items = []
        
        try:
            for prediction in predictions:
                if prediction.confidence >= 0.8:  # High confidence predictions
                    item = AssistanceItem(
                        item_id=f"pred_assist_{prediction.prediction_id}",
                        assistance_type=AssistanceType.SUGGESTION,
                        priority=Priority.MEDIUM,
                        title="Predicted Action Available",
                        description=f"Based on your patterns, you might want to: {prediction.predicted_action}",
                        action_required=False,
                        auto_executable=False,
                        confidence=prediction.confidence,
                        estimated_benefit=0.7,
                        context={"prediction": asdict(prediction)},
                        created_at=time.time(),
                        expires_at=prediction.expires_at
                    )
                    assistance_items.append(item)
                    
        except Exception as e:
            logger.error(f"Error generating prediction assistance: {e}")
        
        return assistance_items
    
    async def _generate_rule_assistance(self, context: Dict[str, Any]) -> List[AssistanceItem]:
        """Generate assistance based on rules."""
        
        assistance_items = []
        
        try:
            for rule in self.assistance_rules.values():
                if not rule.enabled:
                    continue
                
                # Check if rule conditions are met
                if await self._check_rule_conditions(rule, context):
                    item = await self._create_assistance_from_rule(rule, context)
                    if item:
                        assistance_items.append(item)
                        rule.usage_count += 1
                        
        except Exception as e:
            logger.error(f"Error generating rule assistance: {e}")
        
        return assistance_items
    
    async def _check_rule_conditions(self, rule: AssistanceRule, context: Dict[str, Any]) -> bool:
        """Check if rule conditions are satisfied."""
        
        try:
            conditions = rule.conditions
            
            # Check file count threshold
            if "file_count_threshold" in conditions:
                file_count = context.get("file_count", 0)
                if file_count < conditions["file_count_threshold"]:
                    return False
            
            # Check location conditions
            if "location" in conditions:
                current_location = context.get("current_location", "")
                if current_location not in conditions["location"]:
                    return False
            
            # Check system resource thresholds
            if "cpu_threshold" in conditions:
                cpu_usage = context.get("cpu_usage", 0)
                if cpu_usage < conditions["cpu_threshold"]:
                    return False
            
            if "memory_threshold" in conditions:
                memory_usage = context.get("memory_usage", 0)
                if memory_usage < conditions["memory_threshold"]:
                    return False
            
            # Check sequence frequency
            if "sequence_frequency" in conditions:
                sequence_freq = context.get("sequence_frequency", 0)
                if sequence_freq < conditions["sequence_frequency"]:
                    return False
            
            # Check command frequency
            if "command_frequency" in conditions:
                cmd_freq = context.get("command_frequency", 0)
                if cmd_freq < conditions["command_frequency"]:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking rule conditions: {e}")
            return False
    
    async def _create_assistance_from_rule(self, rule: AssistanceRule, 
                                         context: Dict[str, Any]) -> Optional[AssistanceItem]:
        """Create assistance item from rule template."""
        
        try:
            template = rule.assistance_template
            
            # Format description with context
            description = template["description"].format(**context)
            
            item = AssistanceItem(
                item_id=f"rule_assist_{rule.rule_id}_{int(time.time())}",
                assistance_type=template["type"],
                priority=template["priority"],
                title=template["title"],
                description=description,
                action_required=True,
                auto_executable=template.get("auto_executable", False),
                confidence=rule.success_rate,
                estimated_benefit=0.8,
                context={"rule_id": rule.rule_id, "context": context},
                created_at=time.time(),
                expires_at=time.time() + (self.assistance_timeout * 60)
            )
            
            return item
            
        except Exception as e:
            logger.error(f"Error creating assistance from rule: {e}")
            return None
    
    async def _generate_proactive_assistance(self, context: Dict[str, Any]) -> List[AssistanceItem]:
        """Generate proactive assistance suggestions."""
        
        assistance_items = []
        
        try:
            # Get proactive actions from predictive engine
            proactive_actions = await self.predictive_engine.generate_proactive_actions(context)
            
            for action in proactive_actions:
                item = AssistanceItem(
                    item_id=f"proactive_assist_{action.action_id}",
                    assistance_type=AssistanceType.AUTOMATION,
                    priority=Priority(action.priority),
                    title=f"Proactive Action: {action.action_type.replace('_', ' ').title()}",
                    description=action.description,
                    action_required=True,
                    auto_executable=action.confidence >= self.auto_execute_threshold,
                    confidence=action.confidence,
                    estimated_benefit=action.estimated_benefit,
                    context={"proactive_action": asdict(action)},
                    created_at=time.time(),
                    expires_at=time.time() + (action.execution_time * 60)
                )
                assistance_items.append(item)
                
        except Exception as e:
            logger.error(f"Error generating proactive assistance: {e}")
        
        return assistance_items
    
    def _filter_and_prioritize_assistance(self, assistance_items: List[AssistanceItem]) -> List[AssistanceItem]:
        """Filter and prioritize assistance items."""
        
        # Remove duplicates based on title and type
        unique_items = {}
        for item in assistance_items:
            key = f"{item.assistance_type.value}_{item.title}"
            if key not in unique_items or item.confidence > unique_items[key].confidence:
                unique_items[key] = item
        
        # Sort by priority and confidence
        sorted_items = sorted(
            unique_items.values(),
            key=lambda x: (x.priority.value, x.confidence),
            reverse=True
        )
        
        # Apply user preferences
        filtered_items = self._apply_user_preferences(sorted_items)
        
        # Limit to max active assistance
        return filtered_items[:self.max_active_assistance]
    
    def _apply_user_preferences(self, assistance_items: List[AssistanceItem]) -> List[AssistanceItem]:
        """Apply user preferences to filter assistance."""
        
        filtered_items = []
        
        for item in assistance_items:
            # Check if user has disabled this type of assistance
            pref_key = f"disable_{item.assistance_type.value}"
            if self.user_preferences.get(pref_key, False):
                continue
            
            # Check minimum confidence preference
            min_confidence = self.user_preferences.get("min_confidence", 0.5)
            if item.confidence < min_confidence:
                continue
            
            # Check priority preference
            min_priority = self.user_preferences.get("min_priority", Priority.LOW.value)
            if item.priority.value < min_priority:
                continue
            
            filtered_items.append(item)
        
        return filtered_items
    
    async def _auto_execute_assistance(self):
        """Auto-execute high-confidence assistance items."""
        
        try:
            for item in self.active_assistance.values():
                if (item.auto_executable and 
                    not item.executed and 
                    item.confidence >= self.auto_execute_threshold):
                    
                    success = await self._execute_assistance_item(item)
                    item.executed = True
                    
                    # Trigger callback
                    if self.on_assistance_executed:
                        self.on_assistance_executed(item, success)
                        
        except Exception as e:
            logger.error(f"Error in auto-execution: {e}")
    
    async def _execute_assistance_item(self, item: AssistanceItem) -> bool:
        """Execute an assistance item."""
        
        try:
            logger.info(f"Executing assistance: {item.title}")
            
            # Simulate execution based on assistance type
            if item.assistance_type == AssistanceType.OPTIMIZATION:
                # Execute optimization
                await asyncio.sleep(1)  # Simulate work
                return True
            elif item.assistance_type == AssistanceType.AUTOMATION:
                # Execute automation
                await asyncio.sleep(2)  # Simulate work
                return True
            elif item.assistance_type == AssistanceType.SUGGESTION:
                # Log suggestion (no actual execution needed)
                logger.info(f"Suggestion logged: {item.description}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error executing assistance item: {e}")
            return False
    
    def _cleanup_expired_assistance(self):
        """Remove expired assistance items."""
        
        current_time = time.time()
        expired_ids = []
        
        for item_id, item in self.active_assistance.items():
            if item.expires_at and item.expires_at < current_time:
                expired_ids.append(item_id)
                # Move to history
                self.assistance_history.append(item)
        
        for item_id in expired_ids:
            del self.active_assistance[item_id]
        
        # Keep history size manageable
        if len(self.assistance_history) > 1000:
            self.assistance_history = self.assistance_history[-500:]
    
    async def provide_user_feedback(self, item_id: str, feedback: str):
        """Process user feedback on assistance item."""
        
        try:
            if item_id in self.active_assistance:
                item = self.active_assistance[item_id]
                item.user_feedback = feedback
                
                # Record feedback for learning
                feedback_record = {
                    "item_id": item_id,
                    "assistance_type": item.assistance_type.value,
                    "feedback": feedback,
                    "timestamp": time.time(),
                    "confidence": item.confidence
                }
                self.feedback_history.append(feedback_record)
                
                # Update rule success rates based on feedback
                await self._update_rule_success_rates(item, feedback)
                
                # Trigger callback
                if self.on_user_feedback:
                    self.on_user_feedback(item, feedback)
                
                logger.info(f"User feedback recorded for {item_id}: {feedback}")
                
        except Exception as e:
            logger.error(f"Error processing user feedback: {e}")
    
    async def _update_rule_success_rates(self, item: AssistanceItem, feedback: str):
        """Update rule success rates based on user feedback."""
        
        try:
            rule_id = item.context.get("rule_id")
            if rule_id and rule_id in self.assistance_rules:
                rule = self.assistance_rules[rule_id]
                
                # Simple feedback processing
                if feedback.lower() in ["helpful", "good", "yes", "accept"]:
                    # Positive feedback
                    rule.success_rate = min(rule.success_rate + 0.1, 1.0)
                elif feedback.lower() in ["not helpful", "bad", "no", "reject"]:
                    # Negative feedback
                    rule.success_rate = max(rule.success_rate - 0.1, 0.0)
                
                logger.debug(f"Updated rule {rule_id} success rate to {rule.success_rate}")
                
        except Exception as e:
            logger.error(f"Error updating rule success rates: {e}")
    
    def get_assistance_statistics(self) -> Dict[str, Any]:
        """Get comprehensive assistance statistics."""
        
        try:
            total_assistance = len(self.assistance_history) + len(self.active_assistance)
            active_count = len(self.active_assistance)
            
            # Assistance type distribution
            type_distribution = {}
            for item in list(self.active_assistance.values()) + self.assistance_history:
                assist_type = item.assistance_type.value
                type_distribution[assist_type] = type_distribution.get(assist_type, 0) + 1
            
            # Feedback analysis
            positive_feedback = len([f for f in self.feedback_history 
                                   if f["feedback"].lower() in ["helpful", "good", "yes", "accept"]])
            total_feedback = len(self.feedback_history)
            satisfaction_rate = positive_feedback / total_feedback if total_feedback > 0 else 0
            
            # Rule performance
            rule_performance = {
                rule_id: {"usage_count": rule.usage_count, "success_rate": rule.success_rate}
                for rule_id, rule in self.assistance_rules.items()
            }
            
            return {
                "total_assistance_provided": total_assistance,
                "active_assistance_count": active_count,
                "assistance_type_distribution": type_distribution,
                "user_satisfaction_rate": satisfaction_rate,
                "total_feedback_received": total_feedback,
                "rule_performance": rule_performance,
                "auto_execution_threshold": self.auto_execute_threshold
            }
            
        except Exception as e:
            logger.error(f"Error getting assistance statistics: {e}")
            return {"error": str(e)}
    
    def _load_user_preferences(self):
        """Load user preferences from storage."""
        
        try:
            prefs_file = Path("data/automation/user_preferences.json")
            if prefs_file.exists():
                with open(prefs_file, "r") as f:
                    self.user_preferences = json.load(f)
                logger.info("User preferences loaded successfully")
        except Exception as e:
            logger.error(f"Error loading user preferences: {e}")
    
    def save_user_preferences(self):
        """Save user preferences to storage."""
        
        try:
            data_dir = Path("data/automation")
            data_dir.mkdir(parents=True, exist_ok=True)
            
            with open(data_dir / "user_preferences.json", "w") as f:
                json.dump(self.user_preferences, f, indent=2)
            
            logger.info("User preferences saved successfully")
        except Exception as e:
            logger.error(f"Error saving user preferences: {e}")
    
    def update_user_preference(self, key: str, value: Any):
        """Update a user preference."""
        
        self.user_preferences[key] = value
        self.save_user_preferences()
        logger.info(f"User preference updated: {key} = {value}")
    
    def get_active_assistance(self) -> List[AssistanceItem]:
        """Get all active assistance items."""

        return list(self.active_assistance.values())


# Factory functions and aliases
def create_assistance_suggestion(suggestion_id: str, suggestion_type: str, description: str) -> AssistanceItem:
    """Create a new assistance suggestion."""
    return AssistanceItem(
        item_id=suggestion_id,
        assistance_type=AssistanceType.SUGGESTION,
        priority=Priority.MEDIUM,
        title=suggestion_type,
        description=description,
        action_required=False,
        auto_executable=False,
        confidence=0.8,
        estimated_benefit=0.7,
        context={},
        created_at=time.time()
    )

# Alias for compatibility
ProactiveAssistant = ProactiveAssistantSystem
