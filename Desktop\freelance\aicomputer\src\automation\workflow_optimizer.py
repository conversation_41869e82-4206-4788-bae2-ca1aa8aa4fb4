"""
Automated Workflow Optimizer - Phase 3 Component

Intelligent optimization of workflows and command sequences for maximum efficiency.
"""

import asyncio
import time
import json
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict
from pathlib import Path
import statistics

from loguru import logger

from ..utils.config_manager import ConfigManager
from ..intelligence.workflow_engine import WorkflowEngine, Workflow, WorkflowStep
from .pattern_analyzer import BehavioralPatternAnaly<PERSON>, SequencePattern


@dataclass
class OptimizationSuggestion:
    """Workflow optimization suggestion."""
    suggestion_id: str
    optimization_type: str
    target_workflow: str
    description: str
    estimated_improvement: float
    confidence: float
    implementation_effort: str
    parameters: Dict[str, Any]


@dataclass
class PerformanceMetric:
    """Workflow performance metric."""
    metric_id: str
    workflow_id: str
    metric_type: str
    value: float
    timestamp: float
    context: Dict[str, Any]


@dataclass
class OptimizationResult:
    """Result of workflow optimization."""
    optimization_id: str
    original_workflow_id: str
    optimized_workflow_id: str
    improvements: Dict[str, float]
    success: bool
    details: str


class WorkflowOptimizer:
    """
    Intelligent workflow optimization system.
    
    Features:
    - Automatic workflow performance analysis
    - Optimization suggestion generation
    - Workflow restructuring and improvement
    - Performance monitoring and comparison
    - Adaptive optimization based on usage patterns
    - Bottleneck identification and resolution
    """
    
    def __init__(self, config_manager: ConfigManager, 
                 workflow_engine: WorkflowEngine,
                 pattern_analyzer: BehavioralPatternAnalyzer):
        self.config = config_manager
        self.workflow_engine = workflow_engine
        self.pattern_analyzer = pattern_analyzer
        
        # Optimization data
        self.optimization_suggestions: Dict[str, OptimizationSuggestion] = {}
        self.performance_metrics: List[PerformanceMetric] = []
        self.optimization_history: List[OptimizationResult] = []
        
        # Analysis cache
        self.workflow_analysis_cache: Dict[str, Dict[str, Any]] = {}
        self.bottleneck_cache: Dict[str, List[str]] = {}
        
        # Configuration
        self.analysis_interval = self.config.get("automation.optimization.analysis_interval", 3600)
        self.min_executions_for_analysis = self.config.get("automation.optimization.min_executions", 5)
        self.optimization_threshold = self.config.get("automation.optimization.threshold", 0.2)
        
        # Start background optimization
        asyncio.create_task(self._optimization_loop())
        
        logger.info("Workflow Optimizer initialized")
    
    async def _optimization_loop(self):
        """Background optimization analysis loop."""
        
        while True:
            try:
                await asyncio.sleep(self.analysis_interval)
                await self._perform_optimization_analysis()
            except Exception as e:
                logger.error(f"Error in optimization loop: {e}")
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _perform_optimization_analysis(self):
        """Perform comprehensive optimization analysis."""
        
        try:
            logger.info("Starting workflow optimization analysis")
            
            # Get all workflows for analysis
            active_workflows = self.workflow_engine.get_active_workflows()
            workflow_history = self.workflow_engine.workflow_history
            
            # Analyze completed workflows
            for workflow in workflow_history:
                if workflow.status.value == "completed":
                    await self._analyze_workflow_performance(workflow)
            
            # Generate optimization suggestions
            await self._generate_optimization_suggestions()
            
            # Auto-apply high-confidence optimizations
            await self._auto_apply_optimizations()
            
            logger.info("Optimization analysis completed")
            
        except Exception as e:
            logger.error(f"Error in optimization analysis: {e}")
    
    async def _analyze_workflow_performance(self, workflow: Workflow):
        """Analyze performance of a completed workflow."""
        
        try:
            workflow_id = workflow.workflow_id
            
            # Skip if already analyzed recently
            if (workflow_id in self.workflow_analysis_cache and 
                time.time() - self.workflow_analysis_cache[workflow_id].get("last_analyzed", 0) < 3600):
                return
            
            # Calculate performance metrics
            total_duration = workflow.end_time - workflow.start_time if workflow.end_time else 0
            step_durations = []
            step_success_rates = []
            
            for step in workflow.steps:
                if step.start_time and step.end_time:
                    duration = step.end_time - step.start_time
                    step_durations.append(duration)
                    step_success_rates.append(1.0 if step.status.value == "completed" else 0.0)
            
            # Identify bottlenecks
            bottlenecks = self._identify_bottlenecks(workflow)
            
            # Calculate efficiency metrics
            avg_step_duration = statistics.mean(step_durations) if step_durations else 0
            overall_success_rate = statistics.mean(step_success_rates) if step_success_rates else 0
            
            # Store analysis results
            analysis = {
                "workflow_id": workflow_id,
                "total_duration": total_duration,
                "avg_step_duration": avg_step_duration,
                "success_rate": overall_success_rate,
                "bottlenecks": bottlenecks,
                "step_count": len(workflow.steps),
                "last_analyzed": time.time()
            }
            
            self.workflow_analysis_cache[workflow_id] = analysis
            
            # Record performance metrics
            metrics = [
                PerformanceMetric(
                    metric_id=f"{workflow_id}_duration_{int(time.time())}",
                    workflow_id=workflow_id,
                    metric_type="total_duration",
                    value=total_duration,
                    timestamp=time.time(),
                    context={"step_count": len(workflow.steps)}
                ),
                PerformanceMetric(
                    metric_id=f"{workflow_id}_success_rate_{int(time.time())}",
                    workflow_id=workflow_id,
                    metric_type="success_rate",
                    value=overall_success_rate,
                    timestamp=time.time(),
                    context={"total_steps": len(workflow.steps)}
                )
            ]
            
            self.performance_metrics.extend(metrics)
            
        except Exception as e:
            logger.error(f"Error analyzing workflow performance: {e}")
    
    def _identify_bottlenecks(self, workflow: Workflow) -> List[str]:
        """Identify bottlenecks in workflow execution."""
        
        bottlenecks = []
        
        try:
            step_durations = []
            for step in workflow.steps:
                if step.start_time and step.end_time:
                    duration = step.end_time - step.start_time
                    step_durations.append((step.step_id, step.name, duration))
            
            if len(step_durations) < 2:
                return bottlenecks
            
            # Calculate average duration
            avg_duration = statistics.mean([d[2] for d in step_durations])
            
            # Identify steps that take significantly longer than average
            for step_id, step_name, duration in step_durations:
                if duration > avg_duration * 2:  # More than 2x average
                    bottlenecks.append(f"{step_name} (ID: {step_id})")
            
            # Check for failed steps
            for step in workflow.steps:
                if step.status.value == "failed":
                    bottlenecks.append(f"Failed step: {step.name}")
            
        except Exception as e:
            logger.error(f"Error identifying bottlenecks: {e}")
        
        return bottlenecks
    
    async def _generate_optimization_suggestions(self):
        """Generate optimization suggestions based on analysis."""
        
        try:
            # Analyze workflow patterns from pattern analyzer
            pattern_insights = self.pattern_analyzer.get_pattern_insights()
            sequence_insights = pattern_insights.get("sequence_insights", {})
            
            # Generate suggestions for optimization candidates
            optimization_candidates = sequence_insights.get("optimization_candidates", [])
            
            for candidate in optimization_candidates:
                await self._create_sequence_optimization_suggestion(candidate)
            
            # Generate suggestions based on workflow analysis
            for workflow_id, analysis in self.workflow_analysis_cache.items():
                await self._create_workflow_optimization_suggestions(workflow_id, analysis)
            
            # Generate suggestions based on performance trends
            await self._create_performance_optimization_suggestions()
            
        except Exception as e:
            logger.error(f"Error generating optimization suggestions: {e}")
    
    async def _create_sequence_optimization_suggestion(self, candidate: Dict[str, Any]):
        """Create optimization suggestion for a sequence pattern."""
        
        try:
            sequence = candidate["sequence"]
            potential = candidate["potential"]
            
            suggestion_id = f"seq_opt_{hash('_'.join(sequence)) % 10000}"
            
            suggestion = OptimizationSuggestion(
                suggestion_id=suggestion_id,
                optimization_type="sequence_workflow",
                target_workflow="new",
                description=f"Create optimized workflow for sequence: {' -> '.join(sequence)}",
                estimated_improvement=potential,
                confidence=0.8,
                implementation_effort="medium",
                parameters={
                    "sequence": sequence,
                    "optimization_type": "workflow_creation"
                }
            )
            
            self.optimization_suggestions[suggestion_id] = suggestion
            
        except Exception as e:
            logger.error(f"Error creating sequence optimization suggestion: {e}")
    
    async def _create_workflow_optimization_suggestions(self, workflow_id: str, 
                                                      analysis: Dict[str, Any]):
        """Create optimization suggestions for a specific workflow."""
        
        try:
            bottlenecks = analysis.get("bottlenecks", [])
            success_rate = analysis.get("success_rate", 1.0)
            total_duration = analysis.get("total_duration", 0)
            
            # Suggest bottleneck resolution
            if bottlenecks:
                suggestion_id = f"bottleneck_opt_{workflow_id}"
                suggestion = OptimizationSuggestion(
                    suggestion_id=suggestion_id,
                    optimization_type="bottleneck_resolution",
                    target_workflow=workflow_id,
                    description=f"Resolve bottlenecks: {', '.join(bottlenecks[:2])}",
                    estimated_improvement=0.3,
                    confidence=0.7,
                    implementation_effort="high",
                    parameters={
                        "bottlenecks": bottlenecks,
                        "optimization_type": "step_optimization"
                    }
                )
                self.optimization_suggestions[suggestion_id] = suggestion
            
            # Suggest parallelization for long workflows
            if total_duration > 300 and analysis.get("step_count", 0) > 3:  # 5+ minutes, 3+ steps
                suggestion_id = f"parallel_opt_{workflow_id}"
                suggestion = OptimizationSuggestion(
                    suggestion_id=suggestion_id,
                    optimization_type="parallelization",
                    target_workflow=workflow_id,
                    description="Parallelize independent workflow steps",
                    estimated_improvement=0.4,
                    confidence=0.6,
                    implementation_effort="high",
                    parameters={
                        "optimization_type": "step_parallelization",
                        "estimated_speedup": 0.4
                    }
                )
                self.optimization_suggestions[suggestion_id] = suggestion
            
            # Suggest error handling improvement for low success rates
            if success_rate < 0.8:
                suggestion_id = f"error_handling_opt_{workflow_id}"
                suggestion = OptimizationSuggestion(
                    suggestion_id=suggestion_id,
                    optimization_type="error_handling",
                    target_workflow=workflow_id,
                    description="Improve error handling and retry mechanisms",
                    estimated_improvement=0.2,
                    confidence=0.8,
                    implementation_effort="medium",
                    parameters={
                        "optimization_type": "error_handling_improvement",
                        "current_success_rate": success_rate
                    }
                )
                self.optimization_suggestions[suggestion_id] = suggestion
                
        except Exception as e:
            logger.error(f"Error creating workflow optimization suggestions: {e}")
    
    async def _create_performance_optimization_suggestions(self):
        """Create suggestions based on performance trends."""
        
        try:
            # Analyze performance metrics for trends
            workflow_metrics = defaultdict(list)
            
            for metric in self.performance_metrics[-100:]:  # Recent metrics
                workflow_metrics[metric.workflow_id].append(metric)
            
            for workflow_id, metrics in workflow_metrics.items():
                if len(metrics) >= 3:  # Need multiple data points
                    duration_metrics = [m for m in metrics if m.metric_type == "total_duration"]
                    
                    if len(duration_metrics) >= 3:
                        # Check for performance degradation
                        recent_avg = statistics.mean([m.value for m in duration_metrics[-3:]])
                        overall_avg = statistics.mean([m.value for m in duration_metrics])
                        
                        if recent_avg > overall_avg * 1.2:  # 20% slower
                            suggestion_id = f"perf_degradation_{workflow_id}"
                            suggestion = OptimizationSuggestion(
                                suggestion_id=suggestion_id,
                                optimization_type="performance_restoration",
                                target_workflow=workflow_id,
                                description="Address performance degradation",
                                estimated_improvement=0.25,
                                confidence=0.7,
                                implementation_effort="medium",
                                parameters={
                                    "optimization_type": "performance_analysis",
                                    "degradation_factor": recent_avg / overall_avg
                                }
                            )
                            self.optimization_suggestions[suggestion_id] = suggestion
                            
        except Exception as e:
            logger.error(f"Error creating performance optimization suggestions: {e}")
    
    async def _auto_apply_optimizations(self):
        """Automatically apply high-confidence, low-effort optimizations."""
        
        try:
            auto_apply_candidates = [
                suggestion for suggestion in self.optimization_suggestions.values()
                if (suggestion.confidence >= 0.8 and 
                    suggestion.implementation_effort == "low" and
                    suggestion.optimization_type in ["sequence_workflow"])
            ]
            
            for suggestion in auto_apply_candidates:
                success = await self._apply_optimization(suggestion)
                if success:
                    logger.info(f"Auto-applied optimization: {suggestion.description}")
                    
        except Exception as e:
            logger.error(f"Error in auto-applying optimizations: {e}")
    
    async def _apply_optimization(self, suggestion: OptimizationSuggestion) -> bool:
        """Apply a specific optimization suggestion."""
        
        try:
            optimization_id = f"opt_{suggestion.suggestion_id}_{int(time.time())}"
            
            if suggestion.optimization_type == "sequence_workflow":
                # Create optimized workflow from sequence
                success = await self._create_optimized_sequence_workflow(suggestion)
            elif suggestion.optimization_type == "bottleneck_resolution":
                # Optimize workflow bottlenecks
                success = await self._optimize_workflow_bottlenecks(suggestion)
            elif suggestion.optimization_type == "parallelization":
                # Implement step parallelization
                success = await self._implement_step_parallelization(suggestion)
            else:
                logger.warning(f"Unknown optimization type: {suggestion.optimization_type}")
                success = False
            
            # Record optimization result
            result = OptimizationResult(
                optimization_id=optimization_id,
                original_workflow_id=suggestion.target_workflow,
                optimized_workflow_id="",  # Would be set by specific optimization
                improvements={"estimated": suggestion.estimated_improvement},
                success=success,
                details=suggestion.description
            )
            
            self.optimization_history.append(result)
            
            return success
            
        except Exception as e:
            logger.error(f"Error applying optimization: {e}")
            return False
    
    async def _create_optimized_sequence_workflow(self, suggestion: OptimizationSuggestion) -> bool:
        """Create an optimized workflow from a command sequence."""
        
        try:
            sequence = suggestion.parameters.get("sequence", [])
            if not sequence:
                return False
            
            # Create workflow name
            workflow_name = f"Optimized: {' -> '.join(sequence[:2])}"
            if len(sequence) > 2:
                workflow_name += "..."
            
            # Convert sequence to command strings
            commands = []
            for intent in sequence:
                # Map intents to example commands (simplified)
                if intent == "file_operation":
                    commands.append("list files in current directory")
                elif intent == "app_control":
                    commands.append("open notepad")
                elif intent == "system_info":
                    commands.append("show system status")
                else:
                    commands.append(f"execute {intent}")
            
            # Create optimized workflow
            workflow_id = await self.workflow_engine.create_custom_workflow(
                workflow_name,
                f"Optimized workflow for sequence: {' -> '.join(sequence)}",
                commands
            )
            
            if workflow_id:
                logger.info(f"Created optimized workflow: {workflow_name}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error creating optimized sequence workflow: {e}")
            return False
    
    async def _optimize_workflow_bottlenecks(self, suggestion: OptimizationSuggestion) -> bool:
        """Optimize workflow bottlenecks."""
        
        try:
            # This would involve analyzing and modifying existing workflows
            # For now, we'll simulate the optimization
            logger.info(f"Optimizing bottlenecks for workflow: {suggestion.target_workflow}")
            
            # In a real implementation, this would:
            # 1. Analyze bottleneck steps
            # 2. Optimize step parameters
            # 3. Add retry mechanisms
            # 4. Implement caching where appropriate
            
            return True
            
        except Exception as e:
            logger.error(f"Error optimizing workflow bottlenecks: {e}")
            return False
    
    async def _implement_step_parallelization(self, suggestion: OptimizationSuggestion) -> bool:
        """Implement step parallelization in workflow."""
        
        try:
            # This would involve modifying workflow execution to run independent steps in parallel
            logger.info(f"Implementing parallelization for workflow: {suggestion.target_workflow}")
            
            # In a real implementation, this would:
            # 1. Analyze step dependencies
            # 2. Identify parallelizable steps
            # 3. Modify workflow execution engine
            # 4. Test parallel execution
            
            return True
            
        except Exception as e:
            logger.error(f"Error implementing step parallelization: {e}")
            return False
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """Get comprehensive optimization report."""
        
        try:
            # Calculate optimization statistics
            total_suggestions = len(self.optimization_suggestions)
            applied_optimizations = len(self.optimization_history)
            successful_optimizations = len([r for r in self.optimization_history if r.success])
            
            # Optimization type distribution
            suggestion_types = defaultdict(int)
            for suggestion in self.optimization_suggestions.values():
                suggestion_types[suggestion.optimization_type] += 1
            
            # Performance improvements
            total_estimated_improvement = sum(
                s.estimated_improvement for s in self.optimization_suggestions.values()
            )
            
            # Recent performance trends
            recent_metrics = self.performance_metrics[-50:] if self.performance_metrics else []
            avg_recent_duration = statistics.mean([
                m.value for m in recent_metrics if m.metric_type == "total_duration"
            ]) if recent_metrics else 0
            
            report = {
                "optimization_summary": {
                    "total_suggestions": total_suggestions,
                    "applied_optimizations": applied_optimizations,
                    "success_rate": successful_optimizations / applied_optimizations if applied_optimizations > 0 else 0,
                    "total_estimated_improvement": total_estimated_improvement
                },
                "suggestion_distribution": dict(suggestion_types),
                "performance_metrics": {
                    "workflows_analyzed": len(self.workflow_analysis_cache),
                    "average_recent_duration": avg_recent_duration,
                    "total_performance_metrics": len(self.performance_metrics)
                },
                "top_suggestions": [
                    {
                        "type": s.optimization_type,
                        "description": s.description,
                        "improvement": s.estimated_improvement,
                        "confidence": s.confidence
                    }
                    for s in sorted(
                        self.optimization_suggestions.values(),
                        key=lambda x: x.estimated_improvement * x.confidence,
                        reverse=True
                    )[:5]
                ]
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating optimization report: {e}")
            return {"error": str(e)}
    
    def get_workflow_recommendations(self, workflow_id: str) -> List[OptimizationSuggestion]:
        """Get optimization recommendations for a specific workflow."""
        
        recommendations = [
            suggestion for suggestion in self.optimization_suggestions.values()
            if suggestion.target_workflow == workflow_id
        ]
        
        # Sort by potential impact
        recommendations.sort(
            key=lambda s: s.estimated_improvement * s.confidence,
            reverse=True
        )
        
        return recommendations
    
    def save_optimization_data(self):
        """Save optimization data to storage."""
        
        try:
            data_dir = Path("data/automation")
            data_dir.mkdir(parents=True, exist_ok=True)
            
            # Save optimization suggestions
            with open(data_dir / "optimization_suggestions.json", "w") as f:
                suggestions_data = {k: asdict(v) for k, v in self.optimization_suggestions.items()}
                json.dump(suggestions_data, f, indent=2)
            
            # Save optimization history
            with open(data_dir / "optimization_history.json", "w") as f:
                history_data = [asdict(result) for result in self.optimization_history]
                json.dump(history_data, f, indent=2)
            
            logger.info("Optimization data saved successfully")
            
        except Exception as e:
            logger.error(f"Error saving optimization data: {e}")
