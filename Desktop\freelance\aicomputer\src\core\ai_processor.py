"""
Ultimate Voice-Controlled AI Computer System
AI Processing Engine - Core Component

This module handles natural language understanding and command interpretation:
- Integration with multiple LLM providers (OpenAI, Claude, Local models)
- Intent classification and entity extraction
- Context management and conversation memory
- Command generation and validation
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

import openai
from loguru import logger

from ..utils.config_manager import ConfigManager
from ..utils.context_manager import ContextManager


class CommandIntent(Enum):
    """Enumeration of supported command intents."""
    FILE_OPERATION = "file_operation"
    APP_CONTROL = "app_control"
    SYSTEM_INFO = "system_info"
    SYSTEM_CONTROL = "system_control"
    SEARCH = "search"
    HELP = "help"
    UNKNOWN = "unknown"


@dataclass
class ProcessedCommand:
    """Structured representation of a processed voice command."""
    intent: CommandIntent
    action: str
    entities: Dict[str, Any]
    confidence: float
    raw_text: str
    requires_confirmation: bool = False
    is_safe: bool = True


class AIProcessor:
    """
    Advanced AI processor for natural language understanding and command interpretation.
    
    Features:
    - Multi-model LLM integration
    - Intent classification with high accuracy
    - Entity extraction and relationship mapping
    - Context-aware command interpretation
    - Safety validation and confirmation requirements
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        self.context_manager = ContextManager(config_manager)
        
        # Initialize OpenAI client
        self.openai_client = openai.OpenAI(
            api_key=self.config.get("openai.api_key")
        )
        
        # Command patterns and safety rules
        self.dangerous_patterns = [
            "delete", "remove", "uninstall", "format", "shutdown", 
            "restart", "kill", "terminate", "destroy", "wipe"
        ]
        
        self.confirmation_required_patterns = [
            "delete", "remove", "uninstall", "shutdown", "restart"
        ]
        
        logger.info("AI Processor initialized")
    
    async def process_command(self, text: str, user_context: Optional[Dict] = None) -> ProcessedCommand:
        """
        Process a voice command and extract intent, entities, and actions.
        
        Args:
            text: Raw voice command text
            user_context: Additional context information
            
        Returns:
            ProcessedCommand object with structured command information
        """
        logger.info(f"Processing command: {text}")
        
        try:
            # Add to context
            self.context_manager.add_user_input(text)
            
            # Get AI interpretation
            interpretation = await self._get_ai_interpretation(text, user_context)
            
            # Extract structured command
            command = self._extract_command_structure(text, interpretation)
            
            # Validate safety
            command.is_safe = self._validate_command_safety(command)
            command.requires_confirmation = self._requires_confirmation(command)
            
            # Add to context
            self.context_manager.add_ai_response(command.__dict__)
            
            logger.info(f"Command processed: {command.intent.value} - {command.action}")
            return command
            
        except openai.RateLimitError as e:
            logger.error(f"OpenAI rate limit exceeded: {e}")
            return ProcessedCommand(
                intent=CommandIntent.UNKNOWN,
                action="rate_limit_error",
                entities={"error": "API rate limit exceeded, please try again later"},
                confidence=0.0,
                raw_text=text,
                is_safe=False
            )
        except openai.AuthenticationError as e:
            logger.error(f"OpenAI authentication error: {e}")
            return ProcessedCommand(
                intent=CommandIntent.UNKNOWN,
                action="auth_error",
                entities={"error": "API authentication failed"},
                confidence=0.0,
                raw_text=text,
                is_safe=False
            )
        except ConnectionError as e:
            logger.error(f"Network connection error: {e}")
            # Try fallback interpretation
            try:
                interpretation = self._fallback_interpretation(text)
                command = self._extract_command_structure(text, interpretation)
                command.is_safe = self._validate_command_safety(command)
                command.requires_confirmation = self._requires_confirmation(command)
                logger.info(f"Using fallback interpretation for: {text}")
                return command
            except Exception as fallback_error:
                logger.error(f"Fallback interpretation also failed: {fallback_error}")
                return ProcessedCommand(
                    intent=CommandIntent.UNKNOWN,
                    action="connection_error",
                    entities={"error": "Network connection failed"},
                    confidence=0.0,
                    raw_text=text,
                    is_safe=False
                )
        except Exception as e:
            logger.error(f"Unexpected error processing command: {e}")
            logger.debug(f"Full error details: {repr(e)}")
            return ProcessedCommand(
                intent=CommandIntent.UNKNOWN,
                action="error",
                entities={"error": str(e)},
                confidence=0.0,
                raw_text=text,
                is_safe=False
            )
    
    async def _get_ai_interpretation(self, text: str, user_context: Optional[Dict] = None) -> Dict:
        """Get AI interpretation of the command using LLM."""
        
        # Build context for the AI
        context = self._build_ai_context(user_context)
        
        # Create the prompt
        prompt = self._create_interpretation_prompt(text, context)
        
        try:
            response = await self._call_openai_api(prompt)
            return json.loads(response)
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse AI response as JSON: {e}")
            return self._fallback_interpretation(text)
            
        except Exception as e:
            logger.error(f"AI interpretation failed: {e}")
            return self._fallback_interpretation(text)
    
    def _create_interpretation_prompt(self, text: str, context: Dict) -> str:
        """Create a structured prompt for command interpretation."""
        
        prompt = f"""
You are an AI assistant that interprets voice commands for computer control. 
Analyze the following voice command and provide a structured JSON response.

Voice Command: "{text}"

Context:
- Current directory: {context.get('current_directory', 'Unknown')}
- Recent commands: {context.get('recent_commands', [])}
- Available applications: {context.get('available_apps', [])}

Respond with a JSON object containing:
{{
    "intent": "one of: file_operation, app_control, system_info, system_control, search, help, unknown",
    "action": "specific action to perform (e.g., 'open', 'create', 'delete', 'list')",
    "entities": {{
        "target": "main target of the command (file, app, etc.)",
        "location": "path or location if specified",
        "parameters": "additional parameters or options"
    }},
    "confidence": 0.95,
    "reasoning": "brief explanation of the interpretation"
}}

Examples:
- "open notepad" -> {{"intent": "app_control", "action": "open", "entities": {{"target": "notepad.exe"}}, "confidence": 0.95}}
- "list files in documents" -> {{"intent": "file_operation", "action": "list", "entities": {{"location": "Documents"}}, "confidence": 0.90}}
- "what's my CPU usage" -> {{"intent": "system_info", "action": "get_cpu_usage", "entities": {{}}, "confidence": 0.95}}

Provide only the JSON response, no additional text.
"""
        return prompt
    
    async def _call_openai_api(self, prompt: str) -> str:
        """Call OpenAI API for command interpretation."""
        
        model = self.config.get("ai.primary_model", "gpt-4-turbo-preview")
        max_tokens = self.config.get("ai.max_tokens", 1000)
        temperature = self.config.get("ai.temperature", 0.3)
        
        try:
            response = await asyncio.to_thread(
                self.openai_client.chat.completions.create,
                model=model,
                messages=[
                    {"role": "system", "content": "You are a precise command interpreter. Always respond with valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=max_tokens,
                temperature=temperature
            )

            content = response.choices[0].message.content
            if not content or content.strip() == "":
                logger.warning("OpenAI returned empty response")
                raise Exception("Empty response from OpenAI API")

            return content.strip()

        except Exception as e:
            logger.error(f"OpenAI API call failed: {e}")
            logger.debug(f"Model: {model}, Max tokens: {max_tokens}")
            raise
    
    def _build_ai_context(self, user_context: Optional[Dict] = None) -> Dict:
        """Build context information for AI interpretation."""
        
        context = {
            "current_directory": "C:\\Users\\<USER>