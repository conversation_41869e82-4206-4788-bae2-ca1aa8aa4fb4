"""
Ultimate Voice-Controlled AI Computer System
Command Execution Engine - Core Component

This module handles the execution of interpreted voice commands:
- Safe command execution with validation
- File system operations
- Application control and automation
- System information queries
- Error handling and recovery
"""

import asyncio
import os
import subprocess
import psutil
import shutil
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from dataclasses import dataclass
from enum import Enum

from loguru import logger

from .ai_processor import ProcessedCommand, CommandIntent
from ..utils.config_manager import ConfigManager
from ..utils.safety_manager import SafetyManager


@dataclass
class ExecutionResult:
    """Result of command execution."""
    success: bool
    message: str
    data: Optional[Any] = None
    error: Optional[str] = None


class CommandExecutor:
    """
    Command execution engine with safety validation and comprehensive system control.
    
    Features:
    - Safe command execution with validation
    - File system operations (create, delete, move, copy, list)
    - Application launching and control
    - System information queries
    - Error handling and recovery mechanisms
    - Audit logging for all operations
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        self.safety_manager = SafetyManager(config_manager)
        
        # Execution statistics
        self.commands_executed = 0
        self.commands_failed = 0
        self.last_execution_time = None
        
        logger.info("Command Executor initialized")
    
    async def execute_command(self, command: ProcessedCommand, user_confirmed: bool = False) -> ExecutionResult:
        """
        Execute a processed voice command.
        
        Args:
            command: Processed command to execute
            user_confirmed: Whether user has confirmed dangerous operations
            
        Returns:
            ExecutionResult with success status and details
        """
        logger.info(f"Executing command: {command.intent.value} - {command.action}")
        
        try:
            # Safety validation
            if not self._validate_execution_safety(command, user_confirmed):
                return ExecutionResult(
                    success=False,
                    message="Command blocked by safety validation",
                    error="Safety validation failed"
                )
            
            # Route to appropriate handler
            if command.intent == CommandIntent.FILE_OPERATION:
                result = await self._execute_file_operation(command)
            elif command.intent == CommandIntent.APP_CONTROL:
                result = await self._execute_app_control(command)
            elif command.intent == CommandIntent.SYSTEM_INFO:
                result = await self._execute_system_info(command)
            elif command.intent == CommandIntent.SYSTEM_CONTROL:
                result = await self._execute_system_control(command)
            elif command.intent == CommandIntent.SEARCH:
                result = await self._execute_search(command)
            elif command.intent == CommandIntent.HELP:
                result = await self._execute_help(command)
            else:
                result = ExecutionResult(
                    success=False,
                    message=f"Unknown command intent: {command.intent.value}",
                    error="Unsupported command type"
                )
            
            # Update statistics
            if result.success:
                self.commands_executed += 1
            else:
                self.commands_failed += 1
            
            self.last_execution_time = asyncio.get_event_loop().time()
            
            logger.info(f"Command execution result: {result.success} - {result.message}")
            return result
            
        except Exception as e:
            logger.error(f"Command execution failed: {e}")
            self.commands_failed += 1
            
            return ExecutionResult(
                success=False,
                message="Command execution failed due to internal error",
                error=str(e)
            )
    
    def _validate_execution_safety(self, command: ProcessedCommand, user_confirmed: bool) -> bool:
        """Validate if command execution is safe."""
        
        # Check if command is marked as unsafe
        if not command.is_safe:
            logger.warning(f"Unsafe command blocked: {command.raw_text}")
            return False
        
        # Check if confirmation is required but not provided
        if command.requires_confirmation and not user_confirmed:
            logger.info(f"Command requires confirmation: {command.raw_text}")
            return False
        
        # Additional safety checks
        return self.safety_manager.validate_command(command)
    
    async def _execute_file_operation(self, command: ProcessedCommand) -> ExecutionResult:
        """Execute file system operations."""
        
        action = command.action.lower()
        entities = command.entities
        
        try:
            if action == "list":
                return await self._list_files(entities)
            elif action == "create":
                return await self._create_file_or_folder(entities)
            elif action == "delete" or action == "remove":
                return await self._delete_file_or_folder(entities)
            elif action == "copy":
                return await self._copy_file_or_folder(entities)
            elif action == "move":
                return await self._move_file_or_folder(entities)
            elif action == "open":
                return await self._open_file_or_folder(entities)
            else:
                return ExecutionResult(
                    success=False,
                    message=f"Unknown file operation: {action}",
                    error="Unsupported file operation"
                )
                
        except Exception as e:
            return ExecutionResult(
                success=False,
                message=f"File operation failed: {str(e)}",
                error=str(e)
            )
    
    async def _list_files(self, entities: Dict) -> ExecutionResult:
        """List files in specified directory."""
        
        location = entities.get("location", "current")
        
        # Resolve path
        if location == "current":
            path = Path.cwd()
        elif location.lower() == "documents":
            path = Path.home() / "Documents"
        elif location.lower() == "desktop":
            path = Path.home() / "Desktop"
        elif location.lower() == "downloads":
            path = Path.home() / "Downloads"
        else:
            path = Path(location)
        
        if not path.exists():
            return ExecutionResult(
                success=False,
                message=f"Directory not found: {path}",
                error="Directory does not exist"
            )
        
        if not path.is_dir():
            return ExecutionResult(
                success=False,
                message=f"Path is not a directory: {path}",
                error="Not a directory"
            )
        
        # List files
        files = []
        folders = []
        
        for item in path.iterdir():
            if item.is_file():
                files.append({
                    "name": item.name,
                    "size": item.stat().st_size,
                    "modified": item.stat().st_mtime
                })
            elif item.is_dir():
                folders.append({
                    "name": item.name,
                    "type": "folder"
                })
        
        return ExecutionResult(
            success=True,
            message=f"Found {len(files)} files and {len(folders)} folders in {path}",
            data={"files": files, "folders": folders, "path": str(path)}
        )
    
    async def _create_file_or_folder(self, entities: Dict) -> ExecutionResult:
        """Create a new file or folder."""
        
        target = entities.get("target", "")
        location = entities.get("location", "current")
        file_type = entities.get("type", "file")
        
        if not target:
            return ExecutionResult(
                success=False,
                message="No target name specified for creation",
                error="Missing target name"
            )
        
        # Resolve base path
        if location == "current":
            base_path = Path.cwd()
        else:
            base_path = Path(location)
        
        target_path = base_path / target
        
        try:
            if file_type.lower() == "folder" or "folder" in target.lower():
                target_path.mkdir(parents=True, exist_ok=False)
                return ExecutionResult(
                    success=True,
                    message=f"Folder created: {target_path}",
                    data={"path": str(target_path), "type": "folder"}
                )
            else:
                target_path.touch(exist_ok=False)
                return ExecutionResult(
                    success=True,
                    message=f"File created: {target_path}",
                    data={"path": str(target_path), "type": "file"}
                )
                
        except FileExistsError:
            return ExecutionResult(
                success=False,
                message=f"File or folder already exists: {target_path}",
                error="Already exists"
            )
    
    async def _open_file_or_folder(self, entities: Dict) -> ExecutionResult:
        """Open a file or folder with default application."""
        
        target = entities.get("target", "")
        location = entities.get("location", "current")
        
        if not target:
            return ExecutionResult(
                success=False,
                message="No target specified to open",
                error="Missing target"
            )
        
        # Resolve path
        if location == "current":
            target_path = Path.cwd() / target
        else:
            target_path = Path(location) / target
        
        if not target_path.exists():
            return ExecutionResult(
                success=False,
                message=f"File or folder not found: {target_path}",
                error="File not found"
            )
        
        try:
            # Open with default application
            os.startfile(str(target_path))
            
            return ExecutionResult(
                success=True,
                message=f"Opened: {target_path}",
                data={"path": str(target_path)}
            )
            
        except Exception as e:
            return ExecutionResult(
                success=False,
                message=f"Failed to open {target_path}: {str(e)}",
                error=str(e)
            )
    
    async def _execute_app_control(self, command: ProcessedCommand) -> ExecutionResult:
        """Execute application control commands."""
        
        action = command.action.lower()
        entities = command.entities
        
        if action == "open" or action == "start" or action == "launch":
            return await self._launch_application(entities)
        elif action == "close" or action == "kill":
            return await self._close_application(entities)
        elif action == "list":
            return await self._list_running_applications()
        else:
            return ExecutionResult(
                success=False,
                message=f"Unknown application action: {action}",
                error="Unsupported application action"
            )
    
    async def _launch_application(self, entities: Dict) -> ExecutionResult:
        """Launch an application."""
        
        target = entities.get("target", "")
        
        if not target:
            return ExecutionResult(
                success=False,
                message="No application specified to launch",
                error="Missing application name"
            )
        
        # Check if application is in allowed list
        allowed_apps = self.config.get("applications.allowed_apps", [])
        if allowed_apps and target not in allowed_apps:
            return ExecutionResult(
                success=False,
                message=f"Application not in allowed list: {target}",
                error="Application not permitted"
            )
        
        try:
            # Launch application
            process = subprocess.Popen(target, shell=True)
            
            return ExecutionResult(
                success=True,
                message=f"Application launched: {target}",
                data={"application": target, "pid": process.pid}
            )
            
        except Exception as e:
            return ExecutionResult(
                success=False,
                message=f"Failed to launch {target}: {str(e)}",
                error=str(e)
            )
    
    async def _execute_system_info(self, command: ProcessedCommand) -> ExecutionResult:
        """Execute system information queries."""
        
        action = command.action.lower()
        entities = command.entities
        info_type = entities.get("type", "general")
        
        try:
            if "cpu" in action or info_type == "cpu":
                return await self._get_cpu_info()
            elif "memory" in action or info_type == "memory":
                return await self._get_memory_info()
            elif "disk" in action or info_type == "disk":
                return await self._get_disk_info()
            else:
                return await self._get_general_system_info()
                
        except Exception as e:
            return ExecutionResult(
                success=False,
                message=f"Failed to get system information: {str(e)}",
                error=str(e)
            )
    
    async def _get_cpu_info(self) -> ExecutionResult:
        """Get CPU usage information."""
        
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        cpu_freq = psutil.cpu_freq()
        
        data = {
            "usage_percent": cpu_percent,
            "core_count": cpu_count,
            "frequency_mhz": cpu_freq.current if cpu_freq else None
        }
        
        return ExecutionResult(
            success=True,
            message=f"CPU usage: {cpu_percent}% ({cpu_count} cores)",
            data=data
        )
    
    async def _get_memory_info(self) -> ExecutionResult:
        """Get memory usage information."""
        
        memory = psutil.virtual_memory()
        
        data = {
            "total_gb": round(memory.total / (1024**3), 2),
            "available_gb": round(memory.available / (1024**3), 2),
            "used_gb": round(memory.used / (1024**3), 2),
            "percent_used": memory.percent
        }
        
        return ExecutionResult(
            success=True,
            message=f"Memory: {data['used_gb']}GB used of {data['total_gb']}GB ({data['percent_used']}%)",
            data=data
        )
    
    async def _execute_help(self, command: ProcessedCommand) -> ExecutionResult:
        """Provide help information."""
        
        help_text = """
Available voice commands:

File Operations:
- "list files in documents"
- "create new folder called [name]"
- "open file [filename]"

Application Control:
- "open notepad"
- "launch calculator"
- "start chrome"

System Information:
- "what's my CPU usage"
- "show memory information"
- "check disk space"

Say "computer" followed by your command to activate the system.
        """
        
        return ExecutionResult(
            success=True,
            message="Help information displayed",
            data={"help_text": help_text.strip()}
        )

    async def _delete_file_or_folder(self, entities: Dict) -> ExecutionResult:
        """Delete a file or folder."""

        target = entities.get("target", "")
        location = entities.get("location", "current")

        if not target:
            return ExecutionResult(
                success=False,
                message="No target specified for deletion",
                error="Missing target"
            )

        # Resolve path
        if location == "current":
            target_path = Path.cwd() / target
        else:
            target_path = Path(location) / target

        if not target_path.exists():
            return ExecutionResult(
                success=False,
                message=f"File or folder not found: {target_path}",
                error="File not found"
            )

        try:
            if target_path.is_file():
                target_path.unlink()
                return ExecutionResult(
                    success=True,
                    message=f"File deleted: {target_path}",
                    data={"path": str(target_path), "type": "file"}
                )
            elif target_path.is_dir():
                shutil.rmtree(target_path)
                return ExecutionResult(
                    success=True,
                    message=f"Folder deleted: {target_path}",
                    data={"path": str(target_path), "type": "folder"}
                )
            else:
                return ExecutionResult(
                    success=False,
                    message=f"Unknown file type: {target_path}",
                    error="Unknown file type"
                )

        except PermissionError:
            return ExecutionResult(
                success=False,
                message=f"Permission denied: {target_path}",
                error="Permission denied"
            )
        except Exception as e:
            return ExecutionResult(
                success=False,
                message=f"Failed to delete {target_path}: {str(e)}",
                error=str(e)
            )

    async def _copy_file_or_folder(self, entities: Dict) -> ExecutionResult:
        """Copy a file or folder."""

        source = entities.get("source", entities.get("target", ""))
        destination = entities.get("destination", "")

        if not source or not destination:
            return ExecutionResult(
                success=False,
                message="Source and destination must be specified for copy operation",
                error="Missing source or destination"
            )

        source_path = Path(source)
        dest_path = Path(destination)

        if not source_path.exists():
            return ExecutionResult(
                success=False,
                message=f"Source not found: {source_path}",
                error="Source not found"
            )

        try:
            if source_path.is_file():
                shutil.copy2(source_path, dest_path)
                return ExecutionResult(
                    success=True,
                    message=f"File copied from {source_path} to {dest_path}",
                    data={"source": str(source_path), "destination": str(dest_path), "type": "file"}
                )
            elif source_path.is_dir():
                shutil.copytree(source_path, dest_path)
                return ExecutionResult(
                    success=True,
                    message=f"Folder copied from {source_path} to {dest_path}",
                    data={"source": str(source_path), "destination": str(dest_path), "type": "folder"}
                )

        except Exception as e:
            return ExecutionResult(
                success=False,
                message=f"Failed to copy {source_path}: {str(e)}",
                error=str(e)
            )

    async def _move_file_or_folder(self, entities: Dict) -> ExecutionResult:
        """Move a file or folder."""

        source = entities.get("source", entities.get("target", ""))
        destination = entities.get("destination", "")

        if not source or not destination:
            return ExecutionResult(
                success=False,
                message="Source and destination must be specified for move operation",
                error="Missing source or destination"
            )

        source_path = Path(source)
        dest_path = Path(destination)

        if not source_path.exists():
            return ExecutionResult(
                success=False,
                message=f"Source not found: {source_path}",
                error="Source not found"
            )

        try:
            shutil.move(str(source_path), str(dest_path))
            return ExecutionResult(
                success=True,
                message=f"Moved from {source_path} to {dest_path}",
                data={"source": str(source_path), "destination": str(dest_path)}
            )

        except Exception as e:
            return ExecutionResult(
                success=False,
                message=f"Failed to move {source_path}: {str(e)}",
                error=str(e)
            )

    async def _close_application(self, entities: Dict) -> ExecutionResult:
        """Close a running application."""

        target = entities.get("target", "")

        if not target:
            return ExecutionResult(
                success=False,
                message="No application specified to close",
                error="Missing application name"
            )

        try:
            # Find and terminate processes by name
            terminated_count = 0
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if target.lower() in proc.info['name'].lower():
                        proc.terminate()
                        terminated_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if terminated_count > 0:
                return ExecutionResult(
                    success=True,
                    message=f"Terminated {terminated_count} instances of {target}",
                    data={"application": target, "terminated_count": terminated_count}
                )
            else:
                return ExecutionResult(
                    success=False,
                    message=f"No running instances of {target} found",
                    error="Application not running"
                )

        except Exception as e:
            return ExecutionResult(
                success=False,
                message=f"Failed to close {target}: {str(e)}",
                error=str(e)
            )

    async def _list_running_applications(self) -> ExecutionResult:
        """List currently running applications."""

        try:
            applications = []
            for proc in psutil.process_iter(['pid', 'name', 'memory_info']):
                try:
                    applications.append({
                        "pid": proc.info['pid'],
                        "name": proc.info['name'],
                        "memory_mb": round(proc.info['memory_info'].rss / 1024 / 1024, 2)
                    })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            # Sort by memory usage
            applications.sort(key=lambda x: x['memory_mb'], reverse=True)

            return ExecutionResult(
                success=True,
                message=f"Found {len(applications)} running applications",
                data={"applications": applications[:20]}  # Top 20 by memory
            )

        except Exception as e:
            return ExecutionResult(
                success=False,
                message=f"Failed to list applications: {str(e)}",
                error=str(e)
            )

    async def _get_disk_info(self) -> ExecutionResult:
        """Get disk usage information."""

        try:
            disk_usage = psutil.disk_usage('/')

            data = {
                "total_gb": round(disk_usage.total / (1024**3), 2),
                "used_gb": round(disk_usage.used / (1024**3), 2),
                "free_gb": round(disk_usage.free / (1024**3), 2),
                "percent_used": round((disk_usage.used / disk_usage.total) * 100, 2)
            }

            return ExecutionResult(
                success=True,
                message=f"Disk: {data['used_gb']}GB used of {data['total_gb']}GB ({data['percent_used']}%)",
                data=data
            )

        except Exception as e:
            return ExecutionResult(
                success=False,
                message=f"Failed to get disk information: {str(e)}",
                error=str(e)
            )

    async def _get_general_system_info(self) -> ExecutionResult:
        """Get general system information."""

        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')

            data = {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "disk_percent": round((disk.used / disk.total) * 100, 2),
                "uptime_seconds": time.time() - psutil.boot_time()
            }

            return ExecutionResult(
                success=True,
                message=f"System: CPU {data['cpu_percent']}%, Memory {data['memory_percent']}%, Disk {data['disk_percent']}%",
                data=data
            )

        except Exception as e:
            return ExecutionResult(
                success=False,
                message=f"Failed to get system information: {str(e)}",
                error=str(e)
            )

    async def _execute_search(self, command: ProcessedCommand) -> ExecutionResult:
        """Execute search operations."""

        entities = command.entities
        query = entities.get("query", entities.get("target", ""))
        location = entities.get("location", "current")

        if not query:
            return ExecutionResult(
                success=False,
                message="No search query specified",
                error="Missing search query"
            )

        try:
            # Simple file search implementation
            if location == "current":
                search_path = Path.cwd()
            else:
                search_path = Path(location)

            results = []
            for file_path in search_path.rglob(f"*{query}*"):
                if file_path.is_file():
                    results.append({
                        "name": file_path.name,
                        "path": str(file_path),
                        "size": file_path.stat().st_size,
                        "type": "file"
                    })
                elif file_path.is_dir():
                    results.append({
                        "name": file_path.name,
                        "path": str(file_path),
                        "type": "folder"
                    })

            return ExecutionResult(
                success=True,
                message=f"Found {len(results)} items matching '{query}'",
                data={"query": query, "results": results[:50]}  # Limit to 50 results
            )

        except Exception as e:
            return ExecutionResult(
                success=False,
                message=f"Search failed: {str(e)}",
                error=str(e)
            )

    async def _execute_system_control(self, command: ProcessedCommand) -> ExecutionResult:
        """Execute system control commands."""

        action = command.action.lower()

        # For Phase 1, we only support read-only system operations
        if action in ["shutdown", "restart", "sleep", "hibernate"]:
            return ExecutionResult(
                success=False,
                message="System control operations are disabled in Phase 1 for safety",
                error="Operation not permitted"
            )

        # Allow system information queries
        return await self._get_general_system_info()

    def get_execution_stats(self) -> Dict[str, Any]:
        """Get command execution statistics."""
        total_commands = self.commands_executed + self.commands_failed
        success_rate = (self.commands_executed / total_commands * 100) if total_commands > 0 else 0

        return {
            "total_commands": total_commands,
            "successful_executions": self.commands_executed,
            "failed_executions": self.commands_failed,
            "success_rate": success_rate,
            "last_execution_time": self.last_execution_time,
            "executor_status": "active"
        }

    def get_supported_commands(self) -> Dict[str, List[str]]:
        """Get list of supported commands by category."""
        return {
            "file_operations": [
                "create file", "delete file", "copy file", "move file",
                "list files", "search files", "open file"
            ],
            "application_control": [
                "open application", "close application", "switch application",
                "minimize window", "maximize window"
            ],
            "system_info": [
                "show system info", "check cpu usage", "check memory usage",
                "check disk space", "show processes"
            ],
            "web_search": [
                "search web", "google search", "find information",
                "lookup definition", "search for"
            ],
            "information": [
                "what is", "how to", "explain", "define", "help with"
            ]
        }

    def validate_execution_context(self, command: ProcessedCommand) -> Dict[str, Any]:
        """Validate if command can be executed in current context."""
        try:
            validation = {
                "can_execute": True,
                "safety_level": "safe",
                "required_permissions": [],
                "warnings": [],
                "estimated_duration": "instant"
            }

            # Check command safety
            if command.intent == CommandIntent.SYSTEM_CONTROL:
                validation["can_execute"] = False
                validation["safety_level"] = "dangerous"
                validation["warnings"].append("System control commands require elevated permissions")

            # Check file operations
            if command.intent == CommandIntent.FILE_OPERATION:
                action = command.action.lower()
                if action in ["delete", "remove"]:
                    validation["safety_level"] = "moderate"
                    validation["warnings"].append("File deletion is irreversible")
                    validation["required_permissions"].append("file_write")

                if "system" in command.entities.get("target", "").lower():
                    validation["can_execute"] = False
                    validation["safety_level"] = "dangerous"
                    validation["warnings"].append("System file operations are restricted")

            # Estimate duration
            if command.intent == CommandIntent.WEB_SEARCH:
                validation["estimated_duration"] = "2-5 seconds"
            elif command.intent == CommandIntent.APPLICATION_CONTROL:
                validation["estimated_duration"] = "1-3 seconds"
            elif command.intent == CommandIntent.FILE_OPERATION:
                validation["estimated_duration"] = "instant to 1 second"

            return validation

        except Exception as e:
            logger.error(f"Error validating execution context: {e}")
            return {
                "can_execute": False,
                "safety_level": "unknown",
                "warnings": [f"Validation error: {str(e)}"]
            }

    def get_command_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent command execution history."""
        try:
            # In a real implementation, this would return actual history
            # For now, return a placeholder structure
            history = []

            # This would be populated from actual execution logs
            sample_commands = [
                {
                    "command_text": "show system info",
                    "intent": "system_info",
                    "executed_at": time.time() - 300,
                    "success": True,
                    "duration": 0.5
                },
                {
                    "command_text": "open notepad",
                    "intent": "application_control",
                    "executed_at": time.time() - 600,
                    "success": True,
                    "duration": 1.2
                }
            ]

            return sample_commands[:limit]

        except Exception as e:
            logger.error(f"Error getting command history: {e}")
            return []

    def cancel_execution(self, execution_id: str = None) -> bool:
        """Cancel ongoing command execution."""
        try:
            # In a real implementation, this would cancel specific executions
            # For now, return a simple response
            logger.info(f"Cancellation requested for execution: {execution_id or 'current'}")
            return True

        except Exception as e:
            logger.error(f"Error cancelling execution: {e}")
            return False

    def set_execution_mode(self, mode: str) -> bool:
        """Set command execution mode (safe, normal, advanced)."""
        try:
            valid_modes = ["safe", "normal", "advanced"]
            if mode not in valid_modes:
                logger.warning(f"Invalid execution mode: {mode}")
                return False

            self.execution_mode = mode
            logger.info(f"Execution mode set to: {mode}")
            return True

        except Exception as e:
            logger.error(f"Error setting execution mode: {e}")
            return False

    def get_execution_capabilities(self) -> Dict[str, Any]:
        """Get current execution capabilities and limitations."""
        return {
            "supported_intents": [intent.value for intent in CommandIntent],
            "safety_features": {
                "confirmation_required": True,
                "dangerous_commands_blocked": True,
                "system_protection": True,
                "file_protection": True
            },
            "current_mode": getattr(self, 'execution_mode', 'safe'),
            "limitations": [
                "System control operations disabled in Phase 1",
                "Administrative operations require confirmation",
                "Network operations have timeout limits",
                "File operations restricted to user directories"
            ],
            "performance": {
                "average_execution_time": "< 2 seconds",
                "concurrent_executions": 1,
                "queue_capacity": 10
            }
        }
