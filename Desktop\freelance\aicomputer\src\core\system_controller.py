"""
System Controller - Core Component

Manages overall system state, component coordination, and system-level operations.
This is the central coordinator for all Voice AI Controller components.
"""

import asyncio
import time
import threading
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum
from pathlib import Path

from loguru import logger

from .voice_engine import VoiceEngine
from .ai_processor import AIProcessor, ProcessedCommand
from .command_executor import CommandExecutor
from ..utils.config_manager import ConfigManager
from ..utils.context_manager import ContextManager
from ..utils.safety_manager import SafetyManager
from ..ui.system_tray import create_ui_interface
from ..intelligence.advanced_context_engine import AdvancedContextEngine
from ..intelligence.workflow_engine import WorkflowEngine
from ..intelligence.learning_engine import LearningEngine
from ..intelligence.error_recovery import ErrorRecoverySystem
from ..automation.predictive_intelligence import PredictiveIntelligenceEngine
from ..automation.proactive_assistant import ProactiveAssistantSystem
from ..automation.pattern_analyzer import Behavioral<PERSON>atternA<PERSON>yzer
from ..automation.workflow_optimizer import WorkflowOptimizer
from ..experience.multimodal_interface import MultiModalInterfaceManager
from ..experience.security_framework import AdvancedSecurityFramework
from ..experience.iot_integration import IoTIntegrationHub
from ..experience.ar_overlay import AugmentedRealityOverlay
from ..ecosystem.plugin_framework import PluginFramework
from ..ecosystem.developer_sdk import DeveloperSDK
from ..ecosystem.marketplace import Marketplace
from ..ecosystem.enterprise_tools import EnterpriseManager
from ..ecosystem.analytics_dashboard import AnalyticsDashboard
from ..analytics.user_behavior import UserBehaviorAnalytics
from ..automation.cross_application_manager import CrossApplicationManager
from ..automation.intelligent_scheduler import IntelligentTaskScheduler
from ..automation.data_flow_automation import DataFlowAutomation


class SystemState(Enum):
    """System state enumeration."""
    INITIALIZING = "initializing"
    IDLE = "idle"
    LISTENING = "listening"
    PROCESSING = "processing"
    EXECUTING = "executing"
    ERROR = "error"
    SHUTDOWN = "shutdown"


@dataclass
class SystemStats:
    """System statistics and metrics."""
    uptime_seconds: float
    commands_processed: int
    commands_failed: int
    voice_recognition_accuracy: float
    average_response_time: float
    memory_usage_mb: float
    cpu_usage_percent: float


class SystemController:
    """
    Central system controller that coordinates all components.
    
    Features:
    - Component lifecycle management
    - System state coordination
    - Performance monitoring
    - Error handling and recovery
    - User interface management
    - Statistics collection
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        self.state = SystemState.INITIALIZING
        
        # Core components
        self.voice_engine: Optional[VoiceEngine] = None
        self.ai_processor: Optional[AIProcessor] = None
        self.command_executor: Optional[CommandExecutor] = None
        self.context_manager: Optional[ContextManager] = None
        self.safety_manager: Optional[SafetyManager] = None
        self.ui_interface = None

        # Phase 2 Intelligence components
        self.advanced_context_engine: Optional[AdvancedContextEngine] = None
        self.workflow_engine: Optional[WorkflowEngine] = None
        self.learning_engine: Optional[LearningEngine] = None
        self.error_recovery: Optional[ErrorRecoverySystem] = None

        # Phase 3 Automation components
        self.predictive_engine: Optional[PredictiveIntelligenceEngine] = None
        self.proactive_assistant: Optional[ProactiveAssistantSystem] = None
        self.pattern_analyzer: Optional[BehavioralPatternAnalyzer] = None
        self.workflow_optimizer: Optional[WorkflowOptimizer] = None

        # Phase 4 Experience components
        self.multimodal_interface: Optional[MultiModalInterfaceManager] = None
        self.security_framework: Optional[AdvancedSecurityFramework] = None
        self.iot_hub: Optional[IoTIntegrationHub] = None
        self.ar_overlay: Optional[AugmentedRealityOverlay] = None

        # Phase 5 Ecosystem components
        self.plugin_framework = None
        self.developer_sdk = None
        self.marketplace = None
        self.enterprise_manager = None
        self.analytics_dashboard = None

        # Missing components from Phase 8
        self.user_behavior_analytics: Optional[UserBehaviorAnalytics] = None
        self.cross_app_manager: Optional[CrossApplicationManager] = None
        self.intelligent_scheduler: Optional[IntelligentTaskScheduler] = None
        self.data_flow_automation: Optional[DataFlowAutomation] = None
        
        # System state
        self.start_time = time.time()
        self.is_running = False
        self.shutdown_requested = False
        
        # Statistics
        self.stats = SystemStats(
            uptime_seconds=0,
            commands_processed=0,
            commands_failed=0,
            voice_recognition_accuracy=0.0,
            average_response_time=0.0,
            memory_usage_mb=0.0,
            cpu_usage_percent=0.0
        )
        
        # Performance tracking
        self.response_times: List[float] = []
        self.recognition_attempts = 0
        self.recognition_successes = 0
        
        # Event callbacks
        self.on_state_changed: Optional[Callable[[SystemState], None]] = None
        self.on_command_processed: Optional[Callable[[ProcessedCommand], None]] = None
        self.on_error: Optional[Callable[[Exception], None]] = None
        
        logger.info("System Controller initialized")
    
    async def initialize(self) -> bool:
        """Initialize all system components."""
        
        logger.info("Initializing Voice AI Controller system...")
        self._set_state(SystemState.INITIALIZING)
        
        try:
            # Initialize core components
            self.context_manager = ContextManager(self.config)
            self.safety_manager = SafetyManager(self.config)
            self.ai_processor = AIProcessor(self.config)
            self.command_executor = CommandExecutor(self.config)
            self.voice_engine = VoiceEngine(self.config)

            # Initialize Phase 2 Intelligence components
            if self.config.get("intelligence.enabled", True):
                self.advanced_context_engine = AdvancedContextEngine(self.config)
                self.workflow_engine = WorkflowEngine(self.config, self.command_executor)
                self.learning_engine = LearningEngine(self.config)
                self.error_recovery = ErrorRecoverySystem(self.config)

                # Setup error recovery callbacks
                self.error_recovery.on_error_detected = self._on_error_detected
                self.error_recovery.on_recovery_completed = self._on_recovery_completed

                # Initialize Phase 3 Automation components
                if self.config.get("automation.enabled", True):
                    self.predictive_engine = PredictiveIntelligenceEngine(self.config)
                    self.pattern_analyzer = BehavioralPatternAnalyzer(self.config)
                    self.proactive_assistant = ProactiveAssistantSystem(self.config, self.predictive_engine)
                    self.workflow_optimizer = WorkflowOptimizer(self.config, self.workflow_engine, self.pattern_analyzer)

                    # Setup proactive assistant callbacks
                    self.proactive_assistant.on_assistance_generated = self._on_assistance_generated
                    self.proactive_assistant.on_assistance_executed = self._on_assistance_executed

                # Initialize Phase 4 Experience components
                if self.config.get("experience.enabled", True):
                    self.multimodal_interface = MultiModalInterfaceManager(self.config)
                    self.security_framework = AdvancedSecurityFramework(self.config)
                    self.iot_hub = IoTIntegrationHub(self.config)
                    self.ar_overlay = AugmentedRealityOverlay(self.config)

                    # Setup multimodal interface callbacks
                    self.multimodal_interface.on_gesture_detected = self._on_gesture_detected
                    self.multimodal_interface.on_multimodal_command = self._on_multimodal_command

                    # Setup security framework callbacks
                    self.security_framework.on_security_event = self._on_security_event
                    self.security_framework.on_threat_detected = self._on_threat_detected

                    # Setup IoT hub callbacks
                    self.iot_hub.on_device_discovered = self._on_device_discovered
                    self.iot_hub.on_device_status_changed = self._on_device_status_changed

                # Initialize Phase 5 Ecosystem components
                if self.config.get("ecosystem.enabled", True):
                    self.plugin_framework = PluginFramework(self)
                    self.developer_sdk = DeveloperSDK(self)
                    self.marketplace = Marketplace(self)
                    self.enterprise_manager = EnterpriseManager(self)
                    self.analytics_dashboard = AnalyticsDashboard(self)

                    # Connect plugin framework with SDK
                    self.plugin_framework.api_manager = self.developer_sdk.api_manager

                    # Setup analytics tracking
                    self.analytics_dashboard.track_system_event(
                        "system_initialization",
                        {"phase": "5", "components": ["plugin_framework", "developer_sdk", "marketplace", "enterprise_tools", "analytics"]},
                        "system_controller"
                    )

                # Initialize missing Phase 8 components
                if self.config.get("analytics.user_behavior.enabled", True):
                    self.user_behavior_analytics = UserBehaviorAnalytics(self.config)
                    await self.user_behavior_analytics.start()

                if self.config.get("automation.cross_app.enabled", True):
                    self.cross_app_manager = CrossApplicationManager(self.config)
                    await self.cross_app_manager.start()

                if self.config.get("automation.scheduler.enabled", True):
                    self.intelligent_scheduler = IntelligentTaskScheduler(self.config)
                    await self.intelligent_scheduler.start()

                if self.config.get("automation.data_flow.enabled", True):
                    self.data_flow_automation = DataFlowAutomation(self.config)
                    await self.data_flow_automation.start()
            
            # Setup voice engine callbacks
            self.voice_engine.on_wake_word_detected = self._on_wake_word_detected
            self.voice_engine.on_speech_recognized = self._on_speech_recognized
            self.voice_engine.on_error = self._on_voice_error
            
            # Initialize UI interface
            if self.config.get("ui.enabled", True):
                self.ui_interface = create_ui_interface(self.config)
                self._setup_ui_callbacks()
            
            # Create necessary directories
            self._create_directories()
            
            # Start background tasks
            asyncio.create_task(self._monitoring_loop())
            asyncio.create_task(self._maintenance_loop())
            
            self._set_state(SystemState.IDLE)
            logger.info("System initialization completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"System initialization failed: {e}")
            self._set_state(SystemState.ERROR)
            return False
    
    async def start(self) -> bool:
        """Start the Voice AI Controller system."""
        
        if not await self.initialize():
            return False
        
        try:
            self.is_running = True
            self.start_time = time.time()
            
            # Start UI interface
            if self.ui_interface:
                self.ui_interface.start()
            
            # Start voice recognition if enabled
            if self.config.get("voice.auto_start", True):
                await self.start_voice_recognition()
            
            logger.info("Voice AI Controller system started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start system: {e}")
            return False
    
    async def stop(self):
        """Stop the Voice AI Controller system."""
        
        logger.info("Stopping Voice AI Controller system...")
        self.shutdown_requested = True
        self._set_state(SystemState.SHUTDOWN)
        
        try:
            # Stop voice recognition
            if self.voice_engine:
                self.voice_engine.stop_listening()
            
            # Stop UI interface
            if self.ui_interface:
                self.ui_interface.stop()
            
            # Stop Phase 8 components
            if self.user_behavior_analytics:
                await self.user_behavior_analytics.stop()

            if self.cross_app_manager:
                await self.cross_app_manager.stop()

            if self.intelligent_scheduler:
                await self.intelligent_scheduler.stop()

            if self.data_flow_automation:
                await self.data_flow_automation.stop()

            # Save context and cleanup
            if self.context_manager:
                self.context_manager._save_session_context()

            self.is_running = False
            logger.info("System stopped successfully")
            
        except Exception as e:
            logger.error(f"Error during system shutdown: {e}")
    
    async def start_voice_recognition(self):
        """Start voice recognition."""
        
        if not self.voice_engine:
            logger.error("Voice engine not initialized")
            return False
        
        try:
            self.voice_engine.start_listening()
            self._set_state(SystemState.LISTENING)
            
            if self.ui_interface:
                self.ui_interface.update_status(listening=True)
                self.ui_interface.show_notification(
                    "Voice AI", 
                    f"Voice recognition started. Say '{self.config.get('voice.wake_word.word', 'computer')}' to activate."
                )
            
            logger.info("Voice recognition started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start voice recognition: {e}")
            return False
    
    async def stop_voice_recognition(self):
        """Stop voice recognition."""
        
        if not self.voice_engine:
            return
        
        try:
            self.voice_engine.stop_listening()
            self._set_state(SystemState.IDLE)
            
            if self.ui_interface:
                self.ui_interface.update_status(listening=False)
                self.ui_interface.show_notification("Voice AI", "Voice recognition stopped")
            
            logger.info("Voice recognition stopped")
            
        except Exception as e:
            logger.error(f"Failed to stop voice recognition: {e}")
    
    async def process_text_command(self, text: str) -> bool:
        """Process a text command directly (for testing or manual input)."""

        try:
            self._set_state(SystemState.PROCESSING)
            start_time = time.time()

            # Get conversation history for context
            conversation_history = []
            if self.context_manager:
                recent_commands = self.context_manager.get_recent_commands(5)
                conversation_history = recent_commands

            # Security validation (Phase 4)
            if self.security_framework:
                # For now, use a default user ID - in production this would be from authentication
                user_id = "default_user"
                if not await self.security_framework.validate_command_security(text, user_id):
                    logger.warning(f"Command blocked by security framework: {text}")
                    return False

            # Process with AI
            command = await self.ai_processor.process_command(text)

            # Register voice event with multimodal interface (Phase 4)
            if self.multimodal_interface:
                await self.multimodal_interface.register_voice_event({
                    "text": text,
                    "confidence": command.confidence,
                    "intent": command.intent.value
                })

            # Enhanced context analysis (Phase 2)
            context_analysis = None
            if self.advanced_context_engine:
                context_analysis = await self.advanced_context_engine.analyze_command_context(
                    command, conversation_history
                )

            # Execute command
            result = await self._execute_command(command, context_analysis)

            # Track performance
            response_time = time.time() - start_time
            self.response_times.append(response_time)

            # Learning feedback (Phase 2)
            if self.learning_engine:
                await self.learning_engine.process_command_feedback(
                    command, result, response_time
                )

            # Behavioral pattern analysis (Phase 3)
            if self.pattern_analyzer:
                await self.pattern_analyzer.analyze_command_behavior(
                    command, context_analysis or {}, response_time, result
                )

            # Predictive analysis (Phase 3)
            if self.predictive_engine:
                predictions = await self.predictive_engine.analyze_command_for_prediction(
                    command, context_analysis or {}
                )

                # Generate proactive assistance
                if self.proactive_assistant and predictions:
                    await self.proactive_assistant.analyze_for_assistance(
                        context_analysis or {}, predictions
                    )

            # AR overlay feedback (Phase 4)
            if self.ar_overlay:
                status = "completed" if result else "failed"
                await self.ar_overlay.show_command_feedback(
                    command.action, status, command.confidence, 3.0
                )

            # IoT device control (Phase 4)
            if self.iot_hub and "device" in command.action.lower():
                # Simple IoT command detection - in production would be more sophisticated
                if "turn on" in text.lower() or "turn off" in text.lower():
                    action = "turn_on" if "turn on" in text.lower() else "turn_off"
                    # Find matching devices and control them
                    devices = self.iot_hub.get_devices()
                    for device in devices[:1]:  # Control first matching device
                        await self.iot_hub.control_device(device.device_id, action)

            # Analytics tracking (Phase 5)
            if self.analytics_dashboard:
                self.analytics_dashboard.track_voice_command(
                    command=text,
                    user_id="default_user",  # In production, get from authentication
                    session_id="default_session",  # In production, get from session manager
                    response_time=response_time * 1000,  # Convert to milliseconds
                    success=bool(result)
                )

            # User behavior analytics (Phase 8)
            if self.user_behavior_analytics:
                from ..analytics.user_behavior import create_user_action
                user_action = await create_user_action(
                    command=text,
                    intent=command.intent.value,
                    success=bool(result),
                    execution_time=response_time,
                    context=context_analysis or {}
                )
                await self.user_behavior_analytics.record_action(user_action)

            # Cross-application integration (Phase 8)
            if self.cross_app_manager and "transfer" in text.lower():
                # Simple cross-app command detection
                if "to" in text.lower():
                    # Example: "transfer data to excel"
                    parts = text.lower().split("to")
                    if len(parts) > 1:
                        target_app = parts[1].strip().split()[0]
                        await self.cross_app_manager.transfer_data(
                            "system", target_app, result,
                            self.cross_app_manager.DataFormat.TEXT
                        )

            # Intelligent scheduling (Phase 8)
            if self.intelligent_scheduler and ("schedule" in text.lower() or "remind" in text.lower()):
                # Simple scheduling command detection
                task_name = f"Voice command: {text}"
                await self.intelligent_scheduler.create_simple_task(
                    task_name, "voice_command", {"command": text}
                )

            # Data flow automation (Phase 8)
            if self.data_flow_automation and "process" in text.lower():
                # Send command result through data flow system
                await self.data_flow_automation.send_data(
                    data={"command": text, "result": result, "timestamp": time.time()},
                    source="voice_command",
                    metadata={"user_id": "default_user", "session_id": "default_session"}
                )

            if result:
                self.stats.commands_processed += 1
            else:
                self.stats.commands_failed += 1

            self._set_state(SystemState.LISTENING if self.voice_engine.is_listening else SystemState.IDLE)
            return result

        except Exception as e:
            logger.error(f"Error processing text command: {e}")
            self.stats.commands_failed += 1

            # Enhanced error recovery (Phase 2)
            if self.error_recovery:
                recovery_success = await self.error_recovery.handle_error(
                    e, "system_controller", command if 'command' in locals() else None
                )
                if recovery_success:
                    logger.info("Error recovery successful, retrying command")
                    return await self.process_text_command(text)

            return False
    
    def _on_wake_word_detected(self):
        """Handle wake word detection."""

        logger.info("Wake word detected")
        self._set_state(SystemState.LISTENING)
        
        if self.ui_interface:
            self.ui_interface.show_notification("Voice AI", "Wake word detected - listening for command")
    
    async def _on_speech_recognized(self, text: str):
        """Handle recognized speech."""
        
        logger.info(f"Speech recognized: {text}")
        self.recognition_attempts += 1
        
        if text.strip():
            self.recognition_successes += 1
            await self.process_text_command(text)
        
        # Update recognition accuracy
        if self.recognition_attempts > 0:
            self.stats.voice_recognition_accuracy = (self.recognition_successes / self.recognition_attempts) * 100
    
    async def _on_voice_error(self, error: Exception):
        """Handle voice recognition errors."""
        
        logger.error(f"Voice recognition error: {error}")
        
        if self.on_error:
            self.on_error(error)
        
        # Try to recover
        await asyncio.sleep(1)
        if self.voice_engine and not self.voice_engine.is_listening and self.state != SystemState.SHUTDOWN:
            logger.info("Attempting to restart voice recognition")
            await self.start_voice_recognition()
    
    async def _execute_command(self, command: ProcessedCommand,
                             context_analysis: Optional[Dict[str, Any]] = None) -> bool:
        """Execute a processed command with safety checks and enhanced intelligence."""

        try:
            self._set_state(SystemState.EXECUTING)

            # Safety validation
            if not self.safety_manager.validate_command(command):
                logger.warning(f"Command blocked by safety manager: {command.raw_text}")
                return False

            # Check for workflow continuation (Phase 2)
            if self.workflow_engine and context_analysis:
                workflow_context = context_analysis.get("workflow_context")
                if workflow_context:
                    logger.info("Command is part of an active workflow")

            # Check for confirmation requirement
            requires_confirmation = self.safety_manager.requires_confirmation(command)

            # Enhanced confirmation with context suggestions (Phase 2)
            if requires_confirmation and context_analysis:
                suggestions = context_analysis.get("suggestions", [])
                if suggestions:
                    logger.info(f"Context suggestions available: {suggestions[:2]}")

            if requires_confirmation:
                # In a full implementation, this would show a confirmation dialog
                # For now, we'll log and assume confirmation
                logger.info(f"Command requires confirmation: {command.raw_text}")
                confirmed = True  # TODO: Implement actual confirmation dialog
            else:
                confirmed = True

            if not confirmed:
                logger.info("Command cancelled by user")
                return False

            # Execute command
            result = await self.command_executor.execute_command(command, confirmed)

            # Update UI and context
            if self.ui_interface:
                self.ui_interface.update_status(
                    last_command=command.raw_text,
                    commands_count=self.stats.commands_processed + 1
                )

            if self.context_manager:
                self.context_manager.add_ai_response(result.__dict__)

            # Trigger callback
            if self.on_command_processed:
                self.on_command_processed(command)

            if result.success:
                logger.info(f"Command executed successfully: {result.message}")
                return True
            else:
                logger.warning(f"Command execution failed: {result.message}")
                return False

        except Exception as e:
            logger.error(f"Error executing command: {e}")

            # Enhanced error recovery (Phase 2)
            if self.error_recovery:
                await self.error_recovery.handle_error(e, "command_executor", command)

            return False
    
    def _set_state(self, new_state: SystemState):
        """Set system state and trigger callbacks."""
        
        if self.state != new_state:
            old_state = self.state
            self.state = new_state
            
            logger.debug(f"System state changed: {old_state.value} -> {new_state.value}")
            
            if self.on_state_changed:
                self.on_state_changed(new_state)
    
    def _setup_ui_callbacks(self):
        """Setup UI interface callbacks."""
        
        if not self.ui_interface:
            return
        
        self.ui_interface.on_start_requested = lambda: asyncio.create_task(self.start_voice_recognition())
        self.ui_interface.on_stop_requested = lambda: asyncio.create_task(self.stop_voice_recognition())
        self.ui_interface.on_exit_requested = lambda: asyncio.create_task(self.stop())
    
    def _create_directories(self):
        """Create necessary directories."""
        
        directories = [
            "logs",
            "data",
            "data/context",
            "data/models",
            "backups",
            "temp"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    async def _monitoring_loop(self):
        """Background monitoring loop."""
        
        while self.is_running and not self.shutdown_requested:
            try:
                await self._update_system_stats()
                await asyncio.sleep(5)  # Update every 5 seconds
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(10)
    
    async def _maintenance_loop(self):
        """Background maintenance loop."""
        
        while self.is_running and not self.shutdown_requested:
            try:
                await self._perform_maintenance()
                await asyncio.sleep(300)  # Maintenance every 5 minutes
            except Exception as e:
                logger.error(f"Error in maintenance loop: {e}")
                await asyncio.sleep(600)
    
    async def _update_system_stats(self):
        """Update system statistics."""
        
        try:
            import psutil
            
            # Update basic stats
            self.stats.uptime_seconds = time.time() - self.start_time
            self.stats.memory_usage_mb = psutil.Process().memory_info().rss / 1024 / 1024
            self.stats.cpu_usage_percent = psutil.Process().cpu_percent()
            
            # Update average response time
            if self.response_times:
                self.stats.average_response_time = sum(self.response_times) / len(self.response_times)
                
                # Keep only recent response times
                if len(self.response_times) > 100:
                    self.response_times = self.response_times[-50:]
            
        except Exception as e:
            logger.debug(f"Error updating system stats: {e}")

    async def _perform_maintenance(self):
        """Perform periodic maintenance tasks."""

        try:
            # Clean up old context
            if self.context_manager:
                self.context_manager.cleanup_old_context()

            # Phase 2 maintenance tasks
            if self.learning_engine:
                # Save learning data periodically
                self.learning_engine._save_learning_data()

            if self.error_recovery:
                # Save error patterns
                self.error_recovery.save_error_patterns()

            # Log system statistics
            logger.info(
                f"System stats - Uptime: {self.stats.uptime_seconds:.0f}s, "
                f"Commands: {self.stats.commands_processed}, "
                f"Memory: {self.stats.memory_usage_mb:.1f}MB, "
                f"Accuracy: {self.stats.voice_recognition_accuracy:.1f}%"
            )

            # Check for session timeout
            if self.context_manager and self.context_manager.is_session_expired():
                logger.info("Session expired, starting new session")
                self.context_manager.start_new_session()

                # Start new learning session
                if self.learning_engine:
                    self.learning_engine.start_session()

        except Exception as e:
            logger.error(f"Error in maintenance: {e}")

    def _on_error_detected(self, error_context):
        """Handle error detection from error recovery system."""
        logger.warning(f"Error detected by recovery system: {error_context.error_message}")

    def _on_recovery_completed(self, error_context, success: bool):
        """Handle recovery completion from error recovery system."""
        if success:
            logger.info(f"Error recovery successful for: {error_context.error_id}")
        else:
            logger.error(f"Error recovery failed for: {error_context.error_id}")

    def _on_assistance_generated(self, assistance_item):
        """Handle assistance generation from proactive assistant."""
        logger.info(f"Proactive assistance generated: {assistance_item.title}")

        # Show notification if UI is available
        if self.ui_interface:
            self.ui_interface.show_notification(
                "Proactive Assistant",
                assistance_item.description
            )

    def _on_assistance_executed(self, assistance_item, success: bool):
        """Handle assistance execution from proactive assistant."""
        if success:
            logger.info(f"Proactive assistance executed successfully: {assistance_item.title}")
        else:
            logger.warning(f"Proactive assistance execution failed: {assistance_item.title}")

    def _on_gesture_detected(self, gesture_event):
        """Handle gesture detection from multimodal interface."""
        logger.info(f"Gesture detected: {gesture_event.gesture_type.value} at {gesture_event.coordinates}")

        # Show AR feedback
        if self.ar_overlay:
            asyncio.create_task(self.ar_overlay.show_command_feedback(
                f"Gesture: {gesture_event.gesture_type.value}",
                "detected",
                gesture_event.confidence,
                2.0
            ))

    def _on_multimodal_command(self, events):
        """Handle multimodal command from interface manager."""
        modalities = [event.modality.value for event in events]
        logger.info(f"Multimodal command detected: {', '.join(modalities)}")

        # Show AR notification
        if self.ar_overlay:
            asyncio.create_task(self.ar_overlay.show_notification(
                "Multimodal Command",
                f"Combined input: {', '.join(modalities)}",
                "normal",
                3.0
            ))

    def _on_security_event(self, security_event):
        """Handle security event from security framework."""
        logger.warning(f"Security event: {security_event.threat_type.value} - {security_event.description}")

        # Show critical security notifications in AR
        if self.ar_overlay and security_event.severity.value in ["high", "critical"]:
            asyncio.create_task(self.ar_overlay.show_notification(
                "Security Alert",
                security_event.description,
                security_event.severity.value,
                10.0
            ))

    def _on_threat_detected(self, threat_type, description):
        """Handle threat detection from security framework."""
        logger.error(f"Threat detected: {threat_type.value} - {description}")

    def _on_device_discovered(self, device):
        """Handle IoT device discovery."""
        logger.info(f"IoT device discovered: {device.name} ({device.device_type.value})")

        # Show AR notification for new devices
        if self.ar_overlay:
            asyncio.create_task(self.ar_overlay.show_notification(
                "New Device",
                f"Discovered: {device.name}",
                "normal",
                5.0
            ))

    def _on_device_status_changed(self, device_id, status):
        """Handle IoT device status change."""
        logger.info(f"Device {device_id} status changed to {status.value}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""

        status = {
            "state": self.state.value,
            "running": self.is_running,
            "voice_listening": self.voice_engine.is_listening if self.voice_engine else False,
            "uptime_seconds": time.time() - self.start_time,
            "stats": {
                "commands_processed": self.stats.commands_processed,
                "commands_failed": self.stats.commands_failed,
                "voice_recognition_accuracy": self.stats.voice_recognition_accuracy,
                "average_response_time": self.stats.average_response_time,
                "memory_usage_mb": self.stats.memory_usage_mb,
                "cpu_usage_percent": self.stats.cpu_usage_percent
            },
            "configuration": {
                "wake_word": self.config.get("voice.wake_word.word", "computer"),
                "language": self.config.get("voice.recognition.language", "en-US"),
                "safe_mode": self.config.get("app.safe_mode", True),
                "intelligence_enabled": self.config.get("intelligence.enabled", True),
                "automation_enabled": self.config.get("automation.enabled", True),
                "experience_enabled": self.config.get("experience.enabled", True)
            }
        }

        # Add Phase 2 intelligence status
        if self.config.get("intelligence.enabled", True):
            intelligence_status = {}

            if self.advanced_context_engine:
                intelligence_status["context_engine"] = self.advanced_context_engine.get_context_summary()

            if self.workflow_engine:
                intelligence_status["workflows"] = {
                    "active_workflows": self.workflow_engine.get_active_workflows(),
                    "available_templates": self.workflow_engine.get_workflow_templates()
                }

            if self.learning_engine:
                intelligence_status["learning"] = self.learning_engine.get_user_profile()

            if self.error_recovery:
                intelligence_status["error_recovery"] = self.error_recovery.get_error_statistics()

            status["intelligence"] = intelligence_status

        # Add Phase 3 automation status
        if self.config.get("automation.enabled", True):
            automation_status = {}

            if self.predictive_engine:
                automation_status["predictive_intelligence"] = self.predictive_engine.get_prediction_statistics()

            if self.proactive_assistant:
                automation_status["proactive_assistant"] = self.proactive_assistant.get_assistance_statistics()

            if self.pattern_analyzer:
                automation_status["pattern_analysis"] = self.pattern_analyzer.get_pattern_insights()

            if self.workflow_optimizer:
                automation_status["workflow_optimization"] = self.workflow_optimizer.get_optimization_report()

            status["automation"] = automation_status

        # Add Phase 4 experience status
        if self.config.get("experience.enabled", True):
            experience_status = {}

            if self.multimodal_interface:
                experience_status["multimodal_interface"] = self.multimodal_interface.get_interface_statistics()

            if self.security_framework:
                experience_status["security"] = self.security_framework.get_security_status()

            if self.iot_hub:
                experience_status["iot_integration"] = self.iot_hub.get_iot_status()

            if self.ar_overlay:
                experience_status["ar_overlay"] = self.ar_overlay.get_overlay_status()

            status["experience"] = experience_status

        # Add Phase 8 analytics and automation status
        phase8_status = {}

        if self.user_behavior_analytics:
            # Note: This would need to be called from an async context in practice
            phase8_status["user_behavior"] = {
                "analytics_active": True,
                "component_status": "running"
            }

        if self.cross_app_manager:
            phase8_status["cross_application"] = {
                "manager_active": True,
                "component_status": "running"
            }

        if self.intelligent_scheduler:
            phase8_status["intelligent_scheduler"] = {
                "scheduler_active": True,
                "component_status": "running"
            }

        if self.data_flow_automation:
            phase8_status["data_flow"] = {
                "automation_active": True,
                "component_status": "running"
            }

        if phase8_status:
            status["phase8_analytics"] = phase8_status

        return status
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get detailed performance metrics."""
        
        return {
            "response_times": self.response_times[-10:],  # Last 10 response times
            "recognition_accuracy": self.stats.voice_recognition_accuracy,
            "average_response_time": self.stats.average_response_time,
            "commands_per_minute": self.stats.commands_processed / max(self.stats.uptime_seconds / 60, 1),
            "error_rate": self.stats.commands_failed / max(self.stats.commands_processed + self.stats.commands_failed, 1),
            "system_resources": {
                "memory_mb": self.stats.memory_usage_mb,
                "cpu_percent": self.stats.cpu_usage_percent
            }
        }
