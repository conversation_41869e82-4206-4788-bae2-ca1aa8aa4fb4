"""
Ultimate Voice-Controlled AI Computer System
Voice Recognition Engine - Core Component

This module handles all voice input processing, including:
- Continuous wake word detection
- Speech-to-text conversion using multiple engines
- Audio preprocessing and noise reduction
- Real-time audio streaming
"""

import asyncio
import threading
import time
from typing import Optional, Callable, Dict, Any
import speech_recognition as sr
import whisper
import pyaudio
import numpy as np
from loguru import logger

from ..utils.config_manager import ConfigManager
from ..utils.audio_processor import AudioProcessor


class VoiceEngine:
    """
    Core voice recognition engine with multiple backend support.
    
    Features:
    - Wake word detection
    - Multi-engine speech recognition (Whisper, Google, Azure)
    - Continuous listening with low power consumption
    - Audio preprocessing and noise reduction
    - Configurable sensitivity and timeout settings
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        self.is_listening = False
        self.is_wake_word_active = False
        self.audio_processor = AudioProcessor(config_manager)
        
        # Initialize speech recognition
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()
        
        # Load Whisper model
        self.whisper_model = None
        self._load_whisper_model()
        
        # Callbacks
        self.on_wake_word_detected: Optional[Callable] = None
        self.on_speech_recognized: Optional[Callable[[str], None]] = None
        self.on_error: Optional[Callable[[Exception], None]] = None
        
        # Threading
        self.listen_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        
        logger.info("Voice Engine initialized")
    
    def _load_whisper_model(self):
        """Load the Whisper model for speech recognition."""
        try:
            model_size = self.config.get("voice.recognition.whisper_model", "base")
            self.whisper_model = whisper.load_model(model_size)
            logger.info(f"Whisper model '{model_size}' loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load Whisper model: {e}")
            self.whisper_model = None
    
    def start_listening(self):
        """Start continuous voice recognition."""
        if self.is_listening:
            logger.warning("Voice engine is already listening")
            return
        
        self.is_listening = True
        self.stop_event.clear()
        
        # Start listening thread
        self.listen_thread = threading.Thread(target=self._listen_loop, daemon=True)
        self.listen_thread.start()
        
        logger.info("Voice recognition started")
    
    def stop_listening(self):
        """Stop voice recognition."""
        if not self.is_listening:
            return
        
        self.is_listening = False
        self.stop_event.set()
        
        if self.listen_thread and self.listen_thread.is_alive():
            self.listen_thread.join(timeout=2.0)
        
        logger.info("Voice recognition stopped")
    
    def _listen_loop(self):
        """Main listening loop for continuous voice recognition."""
        with self.microphone as source:
            # Adjust for ambient noise
            self.recognizer.adjust_for_ambient_noise(source, duration=1)

            # Apply optimized recognition settings
            self.recognizer.energy_threshold = self.config.get("voice.recognition.energy_threshold", 200)
            self.recognizer.dynamic_energy_threshold = self.config.get("voice.recognition.dynamic_energy_threshold", True)
            self.recognizer.pause_threshold = self.config.get("voice.recognition.pause_threshold", 0.5)

            logger.info(f"Microphone calibrated - Energy: {self.recognizer.energy_threshold}, Pause: {self.recognizer.pause_threshold}")
        
        while not self.stop_event.is_set():
            try:
                if self.config.get("voice.wake_word.enabled", True):
                    self._listen_for_wake_word()
                else:
                    self._listen_for_speech()
                    
            except Exception as e:
                logger.error(f"Error in listen loop: {e}")
                if self.on_error:
                    self.on_error(e)
                time.sleep(1)  # Brief pause before retrying
    
    def _listen_for_wake_word(self):
        """Listen for wake word activation."""
        try:
            with self.microphone as source:
                # Listen for wake word with shorter timeout
                audio = self.recognizer.listen(
                    source,
                    timeout=1,
                    phrase_time_limit=3
                )

            # Quick recognition for wake word
            text = self._recognize_speech(audio, quick=True)

            if text and self._is_wake_word(text):
                logger.info(f"Wake word detected: {text}")
                self.is_wake_word_active = True

                if self.on_wake_word_detected:
                    try:
                        self.on_wake_word_detected()
                    except Exception as callback_error:
                        logger.error(f"Wake word callback error: {callback_error}")

                # Check if command is included in the same phrase
                command = self._extract_command_from_wake_phrase(text)
                if command:
                    logger.info(f"Command extracted from wake phrase: {command}")
                    if self.on_speech_recognized:
                        self.on_speech_recognized(command)
                    self.is_wake_word_active = False
                else:
                    # Listen for separate command
                    self._listen_for_command()

        except sr.WaitTimeoutError:
            # Normal timeout, continue listening
            pass
        except OSError as e:
            logger.error(f"Microphone access error: {e}")
            time.sleep(2)  # Wait before retrying
        except Exception as e:
            logger.debug(f"Wake word detection error: {e}")
            if "device" in str(e).lower() or "audio" in str(e).lower():
                logger.warning("Audio device issue detected, attempting recovery...")
                time.sleep(1)
    
    def _listen_for_command(self):
        """Listen for voice command after wake word detection."""
        try:
            with self.microphone as source:
                logger.info("Listening for command...")
                audio = self.recognizer.listen(
                    source,
                    timeout=self.config.get("voice.recognition.timeout", 8),
                    phrase_time_limit=self.config.get("voice.recognition.phrase_timeout", 8)
                )
            
            # Recognize the command
            text = self._recognize_speech(audio)

            # If Whisper fails, try Google as fallback
            if not text:
                try:
                    text = self._recognize_with_google(audio)
                    if text:
                        logger.info(f"Command recognized with Google fallback: {text}")
                except Exception as e:
                    logger.debug(f"Google fallback also failed: {e}")

            if text:
                logger.info(f"Command recognized: {text}")
                if self.on_speech_recognized:
                    self.on_speech_recognized(text)
            else:
                logger.warning("No speech recognized")
                
        except sr.WaitTimeoutError:
            logger.info("Command timeout - no speech detected")
        except Exception as e:
            logger.error(f"Command recognition error: {e}")
        finally:
            self.is_wake_word_active = False
    
    def _listen_for_speech(self):
        """Direct speech recognition without wake word."""
        try:
            with self.microphone as source:
                audio = self.recognizer.listen(
                    source,
                    timeout=1,
                    phrase_time_limit=self.config.get("voice.recognition.phrase_timeout", 10)
                )
            
            text = self._recognize_speech(audio)
            
            if text:
                logger.info(f"Speech recognized: {text}")
                if self.on_speech_recognized:
                    self.on_speech_recognized(text)
                    
        except sr.WaitTimeoutError:
            pass
        except Exception as e:
            logger.debug(f"Speech recognition error: {e}")
    
    def _recognize_speech(self, audio, quick: bool = False) -> Optional[str]:
        """
        Recognize speech using configured recognition engine.
        
        Args:
            audio: Audio data to recognize
            quick: Use faster but less accurate recognition for wake words
            
        Returns:
            Recognized text or None
        """
        engine = self.config.get("voice.recognition.engine", "whisper")
        
        try:
            if engine == "whisper" and self.whisper_model and not quick:
                return self._recognize_with_whisper(audio)
            elif engine == "google" or quick:
                return self._recognize_with_google(audio)
            elif engine == "azure":
                return self._recognize_with_azure(audio)
            else:
                # Fallback to Google
                return self._recognize_with_google(audio)
                
        except Exception as e:
            logger.error(f"Speech recognition failed with {engine}: {str(e)}")
            logger.debug(f"Full error details: {repr(e)}")

            # Try fallback engine
            if engine != "google":
                try:
                    return self._recognize_with_google(audio)
                except Exception as fallback_error:
                    logger.error(f"Fallback recognition failed: {str(fallback_error)}")
                    logger.debug(f"Full fallback error details: {repr(fallback_error)}")

            return None
    
    def _recognize_with_whisper(self, audio) -> Optional[str]:
        """Recognize speech using Whisper."""
        if not self.whisper_model:
            raise Exception("Whisper model not loaded")

        try:
            # Convert audio to numpy array
            wav_data = audio.get_wav_data()
            if len(wav_data) == 0:
                logger.debug("Empty audio data received")
                return None

            audio_data = np.frombuffer(wav_data, dtype=np.int16)
            if len(audio_data) == 0:
                logger.debug("No audio data after conversion")
                return None

            audio_data = audio_data.astype(np.float32) / 32768.0

            # Recognize with Whisper
            result = self.whisper_model.transcribe(audio_data)
            text = result.get("text", "").strip()

            logger.debug(f"Whisper transcription result: '{text}'")
            return text if text else None

        except Exception as e:
            logger.error(f"Whisper transcription error: {str(e)}")
            raise
    
    def _recognize_with_google(self, audio) -> Optional[str]:
        """Recognize speech using Google Speech Recognition."""
        try:
            language = self.config.get("voice.recognition.language", "en-US")
            text = self.recognizer.recognize_google(audio, language=language)
            logger.debug(f"Google recognition result: '{text}'")
            return text.strip() if text else None
        except sr.UnknownValueError:
            logger.debug("Google Speech Recognition could not understand audio")
            return None
        except sr.RequestError as e:
            logger.error(f"Google Speech Recognition service error: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Google recognition error: {str(e)}")
            raise
    
    def _recognize_with_azure(self, audio) -> Optional[str]:
        """Recognize speech using Azure Speech Services."""
        # TODO: Implement Azure Speech Services integration
        raise NotImplementedError("Azure Speech Services not yet implemented")
    
    def _is_wake_word(self, text: str) -> bool:
        """Check if the recognized text contains the wake word."""
        wake_word = self.config.get("voice.wake_word.word", "computer").lower()
        text_lower = text.lower()

        # Simple wake word detection - can be enhanced with fuzzy matching
        return wake_word in text_lower

    def _extract_command_from_wake_phrase(self, text: str) -> Optional[str]:
        """Extract command from a phrase that includes the wake word."""
        wake_word = self.config.get("voice.wake_word.word", "computer").lower()
        text_lower = text.lower()

        # Find wake word position and extract everything after it
        wake_word_pos = text_lower.find(wake_word)
        if wake_word_pos != -1:
            # Extract text after wake word
            after_wake_word = text[wake_word_pos + len(wake_word):].strip()

            # Remove common connecting words
            connecting_words = [",", "please", "can you", "could you", "would you"]
            for word in connecting_words:
                if after_wake_word.lower().startswith(word):
                    after_wake_word = after_wake_word[len(word):].strip()

            # Return command if it's substantial enough
            if len(after_wake_word) > 2:  # At least 3 characters
                return after_wake_word

        return None
    
    def set_microphone_index(self, index: int):
        """Set the microphone device index."""
        try:
            self.microphone = sr.Microphone(device_index=index)
            logger.info(f"Microphone set to device index: {index}")
        except Exception as e:
            logger.error(f"Failed to set microphone index {index}: {e}")
    
    def get_available_microphones(self) -> Dict[int, str]:
        """Get list of available microphone devices."""
        microphones = {}
        for index, name in enumerate(sr.Microphone.list_microphone_names()):
            microphones[index] = name
        return microphones
    
    def test_microphone(self) -> bool:
        """Test if microphone is working."""
        try:
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
                audio = self.recognizer.listen(source, timeout=2, phrase_time_limit=3)
            
            # Try to recognize something
            text = self._recognize_with_google(audio)
            logger.info(f"Microphone test result: {text}")
            return True
            
        except Exception as e:
            logger.error(f"Microphone test failed: {e}")
            return False
