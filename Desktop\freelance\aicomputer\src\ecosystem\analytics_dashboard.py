"""
Advanced Analytics Dashboard

This module provides comprehensive analytics and reporting capabilities
including usage analytics, performance monitoring, user behavior insights,
system health metrics, and advanced reporting features.
"""

import os
import json
import sqlite3
import logging
import threading
import time
import statistics
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from pathlib import Path
import hashlib
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

@dataclass
class AnalyticsEvent:
    """Analytics event structure"""
    event_id: str
    event_type: str
    user_id: str
    session_id: str
    timestamp: str
    data: Dict[str, Any]
    source: str
    category: str

@dataclass
class PerformanceMetric:
    """Performance metric structure"""
    metric_id: str
    metric_name: str
    value: float
    unit: str
    timestamp: str
    source: str
    tags: Dict[str, str]

@dataclass
class UsageReport:
    """Usage report structure"""
    report_id: str
    report_type: str
    period_start: str
    period_end: str
    data: Dict[str, Any]
    generated_date: str

class AnalyticsCollector:
    """Analytics data collection system"""
    
    def __init__(self, db_path: str = "analytics.db"):
        self.db_path = db_path
        self.init_database()
        
        # In-memory buffers for real-time metrics
        self.event_buffer = deque(maxlen=1000)
        self.performance_buffer = deque(maxlen=1000)
        
        # Real-time metrics
        self.real_time_metrics = {
            "active_sessions": 0,
            "commands_per_minute": 0,
            "average_response_time": 0.0,
            "error_rate": 0.0
        }
        
        # Start background collection
        self.collection_thread = threading.Thread(target=self._background_collection)
        self.collection_thread.daemon = True
        self.collection_running = True
        self.collection_thread.start()
    
    def init_database(self):
        """Initialize analytics database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Events table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS analytics_events (
                    event_id TEXT PRIMARY KEY,
                    event_type TEXT NOT NULL,
                    user_id TEXT,
                    session_id TEXT,
                    timestamp TEXT NOT NULL,
                    data TEXT,
                    source TEXT,
                    category TEXT
                )
            ''')
            
            # Performance metrics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    metric_id TEXT PRIMARY KEY,
                    metric_name TEXT NOT NULL,
                    value REAL NOT NULL,
                    unit TEXT,
                    timestamp TEXT NOT NULL,
                    source TEXT,
                    tags TEXT
                )
            ''')
            
            # User sessions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_sessions (
                    session_id TEXT PRIMARY KEY,
                    user_id TEXT,
                    start_time TEXT,
                    end_time TEXT,
                    duration INTEGER,
                    commands_count INTEGER,
                    errors_count INTEGER,
                    ip_address TEXT,
                    user_agent TEXT
                )
            ''')
            
            # System health table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_health (
                    health_id TEXT PRIMARY KEY,
                    timestamp TEXT NOT NULL,
                    cpu_usage REAL,
                    memory_usage REAL,
                    disk_usage REAL,
                    network_usage REAL,
                    active_connections INTEGER,
                    response_time REAL
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("Analytics database initialized")
            
        except Exception as e:
            logger.error(f"Error initializing analytics database: {e}")
    
    def record_event(self, event: AnalyticsEvent):
        """Record an analytics event"""
        try:
            # Add to buffer for real-time processing
            self.event_buffer.append(event)
            
            # Store in database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO analytics_events VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                event.event_id, event.event_type, event.user_id, event.session_id,
                event.timestamp, json.dumps(event.data), event.source, event.category
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error recording analytics event: {e}")
    
    def record_performance_metric(self, metric: PerformanceMetric):
        """Record a performance metric"""
        try:
            # Add to buffer for real-time processing
            self.performance_buffer.append(metric)
            
            # Store in database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO performance_metrics VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                metric.metric_id, metric.metric_name, metric.value, metric.unit,
                metric.timestamp, metric.source, json.dumps(metric.tags)
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error recording performance metric: {e}")
    
    def record_system_health(self, health_data: Dict[str, Any]):
        """Record system health metrics"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            health_id = hashlib.md5(f"{time.time()}".encode()).hexdigest()
            
            cursor.execute('''
                INSERT INTO system_health VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                health_id, datetime.now().isoformat(),
                health_data.get('cpu_usage', 0.0),
                health_data.get('memory_usage', 0.0),
                health_data.get('disk_usage', 0.0),
                health_data.get('network_usage', 0.0),
                health_data.get('active_connections', 0),
                health_data.get('response_time', 0.0)
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error recording system health: {e}")
    
    def _background_collection(self):
        """Background thread for real-time metrics collection"""
        while self.collection_running:
            try:
                # Update real-time metrics every 10 seconds
                self._update_real_time_metrics()
                time.sleep(10)
                
            except Exception as e:
                logger.error(f"Error in background collection: {e}")
                time.sleep(10)
    
    def _update_real_time_metrics(self):
        """Update real-time metrics from buffers"""
        try:
            current_time = datetime.now()
            one_minute_ago = current_time - timedelta(minutes=1)
            
            # Count recent events
            recent_events = [
                event for event in self.event_buffer
                if datetime.fromisoformat(event.timestamp) > one_minute_ago
            ]
            
            # Count recent performance metrics
            recent_metrics = [
                metric for metric in self.performance_buffer
                if datetime.fromisoformat(metric.timestamp) > one_minute_ago
            ]
            
            # Update metrics
            self.real_time_metrics["commands_per_minute"] = len([
                e for e in recent_events if e.event_type == "voice_command"
            ])
            
            # Calculate average response time
            response_times = [
                m.value for m in recent_metrics 
                if m.metric_name == "response_time"
            ]
            
            if response_times:
                self.real_time_metrics["average_response_time"] = statistics.mean(response_times)
            
            # Calculate error rate
            total_commands = len([e for e in recent_events if e.event_type == "voice_command"])
            error_commands = len([e for e in recent_events if e.event_type == "command_error"])
            
            if total_commands > 0:
                self.real_time_metrics["error_rate"] = (error_commands / total_commands) * 100
            
        except Exception as e:
            logger.error(f"Error updating real-time metrics: {e}")
    
    def get_real_time_metrics(self) -> Dict[str, Any]:
        """Get current real-time metrics"""
        return self.real_time_metrics.copy()

class ReportingEngine:
    """Advanced reporting and analytics engine"""
    
    def __init__(self, analytics_collector: AnalyticsCollector):
        self.analytics_collector = analytics_collector
        self.db_path = analytics_collector.db_path
    
    def generate_usage_report(self, start_date: str, end_date: str, 
                            report_type: str = "summary") -> UsageReport:
        """Generate usage report for specified period"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get events in date range
            cursor.execute('''
                SELECT * FROM analytics_events 
                WHERE timestamp BETWEEN ? AND ?
                ORDER BY timestamp
            ''', (start_date, end_date))
            
            events = cursor.fetchall()
            
            # Get performance metrics in date range
            cursor.execute('''
                SELECT * FROM performance_metrics 
                WHERE timestamp BETWEEN ? AND ?
                ORDER BY timestamp
            ''', (start_date, end_date))
            
            metrics = cursor.fetchall()
            
            conn.close()
            
            # Analyze data
            report_data = self._analyze_usage_data(events, metrics, report_type)
            
            report = UsageReport(
                report_id=hashlib.md5(f"{start_date}_{end_date}_{report_type}".encode()).hexdigest(),
                report_type=report_type,
                period_start=start_date,
                period_end=end_date,
                data=report_data,
                generated_date=datetime.now().isoformat()
            )
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating usage report: {e}")
            return UsageReport("", "error", start_date, end_date, {"error": str(e)}, "")
    
    def _analyze_usage_data(self, events: List[Tuple], metrics: List[Tuple], 
                          report_type: str) -> Dict[str, Any]:
        """Analyze usage data for reporting"""
        try:
            analysis = {
                "summary": {},
                "events": {},
                "performance": {},
                "users": {},
                "trends": {}
            }
            
            # Event analysis
            event_types = defaultdict(int)
            user_activity = defaultdict(int)
            hourly_activity = defaultdict(int)
            
            for event in events:
                event_type = event[1]
                user_id = event[2]
                timestamp = datetime.fromisoformat(event[4])
                
                event_types[event_type] += 1
                if user_id:
                    user_activity[user_id] += 1
                hourly_activity[timestamp.hour] += 1
            
            analysis["events"] = {
                "total_events": len(events),
                "event_types": dict(event_types),
                "hourly_distribution": dict(hourly_activity)
            }
            
            analysis["users"] = {
                "active_users": len(user_activity),
                "user_activity": dict(user_activity)
            }
            
            # Performance analysis
            performance_data = defaultdict(list)
            
            for metric in metrics:
                metric_name = metric[1]
                value = metric[2]
                performance_data[metric_name].append(value)
            
            performance_summary = {}
            for metric_name, values in performance_data.items():
                if values:
                    performance_summary[metric_name] = {
                        "count": len(values),
                        "average": statistics.mean(values),
                        "min": min(values),
                        "max": max(values),
                        "median": statistics.median(values)
                    }
            
            analysis["performance"] = performance_summary
            
            # Summary
            analysis["summary"] = {
                "period_start": events[0][4] if events else "",
                "period_end": events[-1][4] if events else "",
                "total_events": len(events),
                "total_metrics": len(metrics),
                "active_users": len(user_activity),
                "most_active_user": max(user_activity.items(), key=lambda x: x[1])[0] if user_activity else None,
                "peak_hour": max(hourly_activity.items(), key=lambda x: x[1])[0] if hourly_activity else None
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing usage data: {e}")
            return {"error": str(e)}
    
    def generate_performance_report(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """Generate performance report"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get system health data
            cursor.execute('''
                SELECT * FROM system_health 
                WHERE timestamp BETWEEN ? AND ?
                ORDER BY timestamp
            ''', (start_date, end_date))
            
            health_data = cursor.fetchall()
            
            # Get performance metrics
            cursor.execute('''
                SELECT * FROM performance_metrics 
                WHERE timestamp BETWEEN ? AND ?
                ORDER BY timestamp
            ''', (start_date, end_date))
            
            metrics_data = cursor.fetchall()
            
            conn.close()
            
            # Analyze performance
            performance_analysis = {
                "system_health": self._analyze_system_health(health_data),
                "performance_metrics": self._analyze_performance_metrics(metrics_data),
                "recommendations": self._generate_performance_recommendations(health_data, metrics_data)
            }
            
            return performance_analysis
            
        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return {"error": str(e)}
    
    def _analyze_system_health(self, health_data: List[Tuple]) -> Dict[str, Any]:
        """Analyze system health data"""
        if not health_data:
            return {}
        
        cpu_usage = [row[2] for row in health_data if row[2] is not None]
        memory_usage = [row[3] for row in health_data if row[3] is not None]
        disk_usage = [row[4] for row in health_data if row[4] is not None]
        response_times = [row[7] for row in health_data if row[7] is not None]
        
        return {
            "cpu": {
                "average": statistics.mean(cpu_usage) if cpu_usage else 0,
                "max": max(cpu_usage) if cpu_usage else 0,
                "min": min(cpu_usage) if cpu_usage else 0
            },
            "memory": {
                "average": statistics.mean(memory_usage) if memory_usage else 0,
                "max": max(memory_usage) if memory_usage else 0,
                "min": min(memory_usage) if memory_usage else 0
            },
            "disk": {
                "average": statistics.mean(disk_usage) if disk_usage else 0,
                "max": max(disk_usage) if disk_usage else 0,
                "min": min(disk_usage) if disk_usage else 0
            },
            "response_time": {
                "average": statistics.mean(response_times) if response_times else 0,
                "max": max(response_times) if response_times else 0,
                "min": min(response_times) if response_times else 0
            }
        }
    
    def _analyze_performance_metrics(self, metrics_data: List[Tuple]) -> Dict[str, Any]:
        """Analyze performance metrics"""
        metrics_by_name = defaultdict(list)
        
        for metric in metrics_data:
            metric_name = metric[1]
            value = metric[2]
            metrics_by_name[metric_name].append(value)
        
        analysis = {}
        for metric_name, values in metrics_by_name.items():
            if values:
                analysis[metric_name] = {
                    "count": len(values),
                    "average": statistics.mean(values),
                    "max": max(values),
                    "min": min(values),
                    "median": statistics.median(values)
                }
        
        return analysis
    
    def _generate_performance_recommendations(self, health_data: List[Tuple], 
                                           metrics_data: List[Tuple]) -> List[str]:
        """Generate performance recommendations"""
        recommendations = []
        
        if health_data:
            # Analyze CPU usage
            cpu_usage = [row[2] for row in health_data if row[2] is not None]
            if cpu_usage and statistics.mean(cpu_usage) > 80:
                recommendations.append("High CPU usage detected. Consider optimizing voice processing algorithms.")
            
            # Analyze memory usage
            memory_usage = [row[3] for row in health_data if row[3] is not None]
            if memory_usage and statistics.mean(memory_usage) > 85:
                recommendations.append("High memory usage detected. Consider implementing memory optimization.")
            
            # Analyze response times
            response_times = [row[7] for row in health_data if row[7] is not None]
            if response_times and statistics.mean(response_times) > 1000:  # 1 second
                recommendations.append("Slow response times detected. Consider performance tuning.")
        
        if not recommendations:
            recommendations.append("System performance is within normal parameters.")
        
        return recommendations

class AnalyticsDashboard:
    """Main analytics dashboard interface"""
    
    def __init__(self, system_controller=None):
        self.system_controller = system_controller
        self.analytics_collector = AnalyticsCollector()
        self.reporting_engine = ReportingEngine(self.analytics_collector)
        
        # Dashboard configuration
        self.dashboard_config = {
            "refresh_interval": 30,  # seconds
            "data_retention_days": 365,
            "real_time_enabled": True,
            "auto_reports": True
        }
        
        logger.info("Analytics Dashboard initialized")
    
    def track_voice_command(self, command: str, user_id: str, session_id: str, 
                          response_time: float, success: bool):
        """Track a voice command event"""
        try:
            # Record event
            event = AnalyticsEvent(
                event_id=hashlib.md5(f"{time.time()}_{command}".encode()).hexdigest(),
                event_type="voice_command",
                user_id=user_id,
                session_id=session_id,
                timestamp=datetime.now().isoformat(),
                data={
                    "command": command,
                    "response_time": response_time,
                    "success": success
                },
                source="voice_engine",
                category="user_interaction"
            )
            
            self.analytics_collector.record_event(event)
            
            # Record performance metric
            metric = PerformanceMetric(
                metric_id=hashlib.md5(f"{time.time()}_response_time".encode()).hexdigest(),
                metric_name="response_time",
                value=response_time,
                unit="milliseconds",
                timestamp=datetime.now().isoformat(),
                source="voice_engine",
                tags={"command_type": "voice", "success": str(success)}
            )
            
            self.analytics_collector.record_performance_metric(metric)
            
        except Exception as e:
            logger.error(f"Error tracking voice command: {e}")
    
    def track_system_event(self, event_type: str, data: Dict[str, Any], source: str):
        """Track a system event"""
        try:
            event = AnalyticsEvent(
                event_id=hashlib.md5(f"{time.time()}_{event_type}".encode()).hexdigest(),
                event_type=event_type,
                user_id="system",
                session_id="system",
                timestamp=datetime.now().isoformat(),
                data=data,
                source=source,
                category="system"
            )
            
            self.analytics_collector.record_event(event)
            
        except Exception as e:
            logger.error(f"Error tracking system event: {e}")
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """Get comprehensive dashboard data"""
        try:
            # Get real-time metrics
            real_time_metrics = self.analytics_collector.get_real_time_metrics()
            
            # Get recent usage report
            end_date = datetime.now().isoformat()
            start_date = (datetime.now() - timedelta(days=7)).isoformat()
            
            usage_report = self.reporting_engine.generate_usage_report(
                start_date, end_date, "summary"
            )
            
            # Get performance report
            performance_report = self.reporting_engine.generate_performance_report(
                start_date, end_date
            )
            
            return {
                "real_time_metrics": real_time_metrics,
                "usage_summary": usage_report.data,
                "performance_summary": performance_report,
                "dashboard_config": self.dashboard_config,
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting dashboard data: {e}")
            return {"error": str(e)}
    
    def generate_custom_report(self, report_config: Dict[str, Any]) -> Dict[str, Any]:
        """Generate custom analytics report"""
        try:
            start_date = report_config.get("start_date")
            end_date = report_config.get("end_date")
            report_type = report_config.get("type", "summary")
            
            if report_type == "usage":
                report = self.reporting_engine.generate_usage_report(
                    start_date, end_date, "detailed"
                )
                return asdict(report)
            elif report_type == "performance":
                return self.reporting_engine.generate_performance_report(
                    start_date, end_date
                )
            else:
                return {"error": "Unknown report type"}
                
        except Exception as e:
            logger.error(f"Error generating custom report: {e}")
            return {"error": str(e)}
