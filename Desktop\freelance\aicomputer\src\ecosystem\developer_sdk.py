"""
Developer SDK and API Manager

This module provides comprehensive APIs for third-party developers to integrate
with the voice AI system. It includes voice APIs, command APIs, context APIs,
and event systems for plugin development.
"""

import logging
from typing import Dict, List, Any, Optional, Callable, Union
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
import threading
import queue
import json
import asyncio

logger = logging.getLogger(__name__)

@dataclass
class APIPermission:
    """API permission structure"""
    name: str
    description: str
    level: str  # 'read', 'write', 'admin'
    required: bool = False

@dataclass
class APIResponse:
    """Standard API response structure"""
    success: bool
    data: Any = None
    error: Optional[str] = None
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()

class VoiceAPI:
    """Voice recognition and synthesis API for plugins"""
    
    def __init__(self, system_controller):
        self.system_controller = system_controller
        self.voice_engine = system_controller.voice_engine if system_controller else None
    
    def listen_for_speech(self, timeout: float = 5.0, wake_word_required: bool = False) -> APIResponse:
        """Listen for speech input"""
        try:
            if not self.voice_engine:
                return APIResponse(False, error="Voice engine not available")
            
            # Use voice engine to listen
            result = self.voice_engine.listen_for_command(timeout=timeout)
            
            if result:
                return APIResponse(True, data={"text": result, "confidence": 0.95})
            else:
                return APIResponse(False, error="No speech detected")
                
        except Exception as e:
            logger.error(f"Error in listen_for_speech: {e}")
            return APIResponse(False, error=str(e))
    
    def speak_text(self, text: str, voice: str = "default") -> APIResponse:
        """Convert text to speech"""
        try:
            if not self.voice_engine:
                return APIResponse(False, error="Voice engine not available")
            
            # Use voice engine to speak
            success = self.voice_engine.speak(text)
            
            if success:
                return APIResponse(True, data={"text": text, "voice": voice})
            else:
                return APIResponse(False, error="Failed to speak text")
                
        except Exception as e:
            logger.error(f"Error in speak_text: {e}")
            return APIResponse(False, error=str(e))
    
    def get_voice_settings(self) -> APIResponse:
        """Get current voice recognition settings"""
        try:
            settings = {
                "recognition_engine": "whisper",
                "language": "en-US",
                "sensitivity": 0.7,
                "wake_word": "computer"
            }
            return APIResponse(True, data=settings)
            
        except Exception as e:
            logger.error(f"Error getting voice settings: {e}")
            return APIResponse(False, error=str(e))

class CommandAPI:
    """Command registration and execution API for plugins"""
    
    def __init__(self, system_controller):
        self.system_controller = system_controller
        self.registered_commands: Dict[str, Dict[str, Any]] = {}
        self.command_executor = system_controller.command_executor if system_controller else None
    
    def register_command(self, plugin_name: str, command_pattern: str, 
                        handler: Callable, description: str = "", 
                        permissions: List[str] = None) -> APIResponse:
        """Register a new voice command"""
        try:
            command_id = f"{plugin_name}_{command_pattern}"
            
            self.registered_commands[command_id] = {
                "plugin_name": plugin_name,
                "pattern": command_pattern,
                "handler": handler,
                "description": description,
                "permissions": permissions or [],
                "registered_at": datetime.now().isoformat()
            }
            
            logger.info(f"Registered command: {command_id}")
            return APIResponse(True, data={"command_id": command_id})
            
        except Exception as e:
            logger.error(f"Error registering command: {e}")
            return APIResponse(False, error=str(e))
    
    def unregister_command(self, plugin_name: str, command_pattern: str) -> APIResponse:
        """Unregister a voice command"""
        try:
            command_id = f"{plugin_name}_{command_pattern}"
            
            if command_id in self.registered_commands:
                del self.registered_commands[command_id]
                logger.info(f"Unregistered command: {command_id}")
                return APIResponse(True, data={"command_id": command_id})
            else:
                return APIResponse(False, error="Command not found")
                
        except Exception as e:
            logger.error(f"Error unregistering command: {e}")
            return APIResponse(False, error=str(e))
    
    def execute_system_command(self, command: str, parameters: Dict[str, Any] = None) -> APIResponse:
        """Execute a system command"""
        try:
            if not self.command_executor:
                return APIResponse(False, error="Command executor not available")
            
            # Execute through system command executor
            result = self.command_executor.execute_command(command, parameters or {})
            
            return APIResponse(True, data=result)
            
        except Exception as e:
            logger.error(f"Error executing system command: {e}")
            return APIResponse(False, error=str(e))
    
    def get_registered_commands(self, plugin_name: str = None) -> APIResponse:
        """Get list of registered commands"""
        try:
            if plugin_name:
                commands = {k: v for k, v in self.registered_commands.items() 
                           if v["plugin_name"] == plugin_name}
            else:
                commands = self.registered_commands
            
            return APIResponse(True, data=commands)
            
        except Exception as e:
            logger.error(f"Error getting registered commands: {e}")
            return APIResponse(False, error=str(e))

class ContextAPI:
    """Context and user data API for plugins"""
    
    def __init__(self, system_controller):
        self.system_controller = system_controller
        self.context_manager = system_controller.context_manager if system_controller else None
    
    def get_current_context(self) -> APIResponse:
        """Get current system context"""
        try:
            if not self.context_manager:
                return APIResponse(False, error="Context manager not available")
            
            context = self.context_manager.get_current_context()
            return APIResponse(True, data=context)
            
        except Exception as e:
            logger.error(f"Error getting current context: {e}")
            return APIResponse(False, error=str(e))
    
    def set_context_data(self, key: str, value: Any, plugin_name: str) -> APIResponse:
        """Set context data for plugin"""
        try:
            if not self.context_manager:
                return APIResponse(False, error="Context manager not available")
            
            # Namespace the key with plugin name
            namespaced_key = f"plugin_{plugin_name}_{key}"
            self.context_manager.set_context(namespaced_key, value)
            
            return APIResponse(True, data={"key": namespaced_key, "value": value})
            
        except Exception as e:
            logger.error(f"Error setting context data: {e}")
            return APIResponse(False, error=str(e))
    
    def get_context_data(self, key: str, plugin_name: str) -> APIResponse:
        """Get context data for plugin"""
        try:
            if not self.context_manager:
                return APIResponse(False, error="Context manager not available")
            
            # Namespace the key with plugin name
            namespaced_key = f"plugin_{plugin_name}_{key}"
            value = self.context_manager.get_context(namespaced_key)
            
            return APIResponse(True, data={"key": key, "value": value})
            
        except Exception as e:
            logger.error(f"Error getting context data: {e}")
            return APIResponse(False, error=str(e))
    
    def get_user_preferences(self) -> APIResponse:
        """Get user preferences (read-only)"""
        try:
            # Return safe subset of user preferences
            preferences = {
                "language": "en-US",
                "voice_speed": "normal",
                "confirmation_level": "medium",
                "theme": "dark"
            }
            return APIResponse(True, data=preferences)
            
        except Exception as e:
            logger.error(f"Error getting user preferences: {e}")
            return APIResponse(False, error=str(e))

class EventAPI:
    """Event system API for plugins"""
    
    def __init__(self, system_controller):
        self.system_controller = system_controller
        self.event_listeners: Dict[str, List[Callable]] = {}
        self.event_queue = queue.Queue()
        self.event_thread = None
        self.running = False
    
    def start_event_system(self):
        """Start the event processing system"""
        if not self.running:
            self.running = True
            self.event_thread = threading.Thread(target=self._process_events)
            self.event_thread.daemon = True
            self.event_thread.start()
    
    def stop_event_system(self):
        """Stop the event processing system"""
        self.running = False
        if self.event_thread:
            self.event_thread.join()
    
    def _process_events(self):
        """Process events in background thread"""
        while self.running:
            try:
                event = self.event_queue.get(timeout=1.0)
                self._dispatch_event(event)
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Error processing event: {e}")
    
    def _dispatch_event(self, event: Dict[str, Any]):
        """Dispatch event to registered listeners"""
        event_type = event.get("type")
        if event_type in self.event_listeners:
            for listener in self.event_listeners[event_type]:
                try:
                    listener(event)
                except Exception as e:
                    logger.error(f"Error in event listener: {e}")
    
    def register_event_listener(self, event_type: str, listener: Callable, plugin_name: str) -> APIResponse:
        """Register an event listener"""
        try:
            if event_type not in self.event_listeners:
                self.event_listeners[event_type] = []
            
            # Wrap listener with plugin context
            def wrapped_listener(event):
                event["plugin_context"] = plugin_name
                return listener(event)
            
            self.event_listeners[event_type].append(wrapped_listener)
            
            logger.info(f"Registered event listener for {event_type} from plugin {plugin_name}")
            return APIResponse(True, data={"event_type": event_type, "plugin": plugin_name})
            
        except Exception as e:
            logger.error(f"Error registering event listener: {e}")
            return APIResponse(False, error=str(e))
    
    def emit_event(self, event_type: str, data: Dict[str, Any], plugin_name: str) -> APIResponse:
        """Emit an event"""
        try:
            event = {
                "type": event_type,
                "data": data,
                "source_plugin": plugin_name,
                "timestamp": datetime.now().isoformat()
            }
            
            self.event_queue.put(event)
            return APIResponse(True, data=event)
            
        except Exception as e:
            logger.error(f"Error emitting event: {e}")
            return APIResponse(False, error=str(e))

class APIManager:
    """Central API manager for plugin access"""
    
    def __init__(self, system_controller):
        self.system_controller = system_controller
        
        # Initialize API components
        self.voice_api = VoiceAPI(system_controller)
        self.command_api = CommandAPI(system_controller)
        self.context_api = ContextAPI(system_controller)
        self.event_api = EventAPI(system_controller)
        
        # Plugin permissions
        self.plugin_permissions: Dict[str, List[APIPermission]] = {}
        
        # Start event system
        self.event_api.start_event_system()
    
    def register_plugin_permissions(self, plugin_name: str, permissions: List[APIPermission]) -> bool:
        """Register permissions for a plugin"""
        try:
            self.plugin_permissions[plugin_name] = permissions
            logger.info(f"Registered permissions for plugin {plugin_name}")
            return True
        except Exception as e:
            logger.error(f"Error registering plugin permissions: {e}")
            return False
    
    def check_permission(self, plugin_name: str, permission_name: str) -> bool:
        """Check if plugin has specific permission"""
        if plugin_name not in self.plugin_permissions:
            return False
        
        for permission in self.plugin_permissions[plugin_name]:
            if permission.name == permission_name:
                return True
        
        return False
    
    def get_api_for_plugin(self, plugin_name: str) -> 'PluginAPIWrapper':
        """Get API wrapper for specific plugin"""
        return PluginAPIWrapper(self, plugin_name)

class PluginAPIWrapper:
    """API wrapper that enforces permissions for specific plugin"""
    
    def __init__(self, api_manager: APIManager, plugin_name: str):
        self.api_manager = api_manager
        self.plugin_name = plugin_name
    
    @property
    def voice(self) -> VoiceAPI:
        """Get voice API with permission check"""
        if self.api_manager.check_permission(self.plugin_name, "voice_access"):
            return self.api_manager.voice_api
        else:
            raise PermissionError(f"Plugin {self.plugin_name} does not have voice access permission")
    
    @property
    def commands(self) -> CommandAPI:
        """Get command API with permission check"""
        if self.api_manager.check_permission(self.plugin_name, "command_access"):
            return self.api_manager.command_api
        else:
            raise PermissionError(f"Plugin {self.plugin_name} does not have command access permission")
    
    @property
    def context(self) -> ContextAPI:
        """Get context API with permission check"""
        if self.api_manager.check_permission(self.plugin_name, "context_access"):
            return self.api_manager.context_api
        else:
            raise PermissionError(f"Plugin {self.plugin_name} does not have context access permission")
    
    @property
    def events(self) -> EventAPI:
        """Get event API with permission check"""
        if self.api_manager.check_permission(self.plugin_name, "event_access"):
            return self.api_manager.event_api
        else:
            raise PermissionError(f"Plugin {self.plugin_name} does not have event access permission")

class DeveloperSDK:
    """Main Developer SDK interface"""
    
    def __init__(self, system_controller):
        self.system_controller = system_controller
        self.api_manager = APIManager(system_controller)
        
        # SDK version and compatibility
        self.version = "5.0.0"
        self.api_version = "1.0"
        self.min_plugin_version = "1.0"
    
    def get_api_documentation(self) -> Dict[str, Any]:
        """Get comprehensive API documentation"""
        return {
            "version": self.version,
            "api_version": self.api_version,
            "apis": {
                "voice": {
                    "description": "Voice recognition and synthesis",
                    "methods": ["listen_for_speech", "speak_text", "get_voice_settings"],
                    "permissions": ["voice_access"]
                },
                "commands": {
                    "description": "Command registration and execution",
                    "methods": ["register_command", "unregister_command", "execute_system_command"],
                    "permissions": ["command_access"]
                },
                "context": {
                    "description": "Context and user data access",
                    "methods": ["get_current_context", "set_context_data", "get_context_data"],
                    "permissions": ["context_access"]
                },
                "events": {
                    "description": "Event system for notifications",
                    "methods": ["register_event_listener", "emit_event"],
                    "permissions": ["event_access"]
                }
            }
        }
    
    def create_plugin_template(self, plugin_name: str, author: str) -> str:
        """Generate a plugin template"""
        template = f'''"""
{plugin_name} Plugin

Generated by Voice AI Developer SDK v{self.version}
Author: {author}
"""

from src.ecosystem.plugin_framework import PluginInterface, PluginMetadata
from src.ecosystem.developer_sdk import APIResponse
from typing import Dict, List, Any, Callable

class Plugin(PluginInterface):
    """Main plugin class"""
    
    def __init__(self):
        self.api = None
        self.metadata = PluginMetadata(
            name="{plugin_name}",
            version="1.0.0",
            description="Description of {plugin_name}",
            author="{author}",
            email="<EMAIL>",
            website="https://example.com",
            license="MIT",
            dependencies=[],
            permissions=["voice_access", "command_access"],
            api_version="1.0",
            min_system_version="5.0.0",
            category="utility",
            tags=["voice", "automation"]
        )
    
    def initialize(self, api_manager) -> bool:
        """Initialize the plugin"""
        try:
            self.api = api_manager.get_api_for_plugin(self.metadata.name)
            
            # Register commands
            self.api.commands.register_command(
                self.metadata.name,
                "hello world",
                self.handle_hello_command,
                "Say hello to the world"
            )
            
            return True
        except Exception as e:
            print(f"Error initializing plugin: {{e}}")
            return False
    
    def get_commands(self) -> Dict[str, Callable]:
        """Return available commands"""
        return {{
            "hello_world": self.handle_hello_command
        }}
    
    def handle_hello_command(self, command: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle hello world command"""
        try:
            response_text = "Hello, World! This is {plugin_name} plugin."
            self.api.voice.speak_text(response_text)
            
            return {{
                "status": "success",
                "message": response_text
            }}
        except Exception as e:
            return {{
                "status": "error",
                "message": str(e)
            }}
    
    def cleanup(self) -> None:
        """Cleanup when plugin is disabled"""
        if self.api:
            self.api.commands.unregister_command(self.metadata.name, "hello world")
    
    def get_metadata(self) -> PluginMetadata:
        """Return plugin metadata"""
        return self.metadata
'''
        return template
