"""
Community Marketplace Platform

This module provides a comprehensive marketplace for plugin distribution,
including plugin repository, rating system, search and discovery,
version management, and revenue sharing capabilities.
"""

import os
import json
import sqlite3
import hashlib
import requests
import zipfile
import shutil
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
import logging
import threading
import time

logger = logging.getLogger(__name__)

@dataclass
class PluginListing:
    """Plugin marketplace listing"""
    plugin_id: str
    name: str
    version: str
    description: str
    author: str
    author_id: str
    category: str
    tags: List[str]
    price: float
    currency: str
    license: str
    download_url: str
    icon_url: str
    screenshots: List[str]
    rating: float
    review_count: int
    download_count: int
    last_updated: str
    created_date: str
    verified: bool
    featured: bool
    compatibility: List[str]
    file_size: int
    checksum: str

@dataclass
class PluginReview:
    """Plugin review structure"""
    review_id: str
    plugin_id: str
    user_id: str
    username: str
    rating: int
    title: str
    content: str
    created_date: str
    helpful_count: int
    verified_purchase: bool

@dataclass
class MarketplaceUser:
    """Marketplace user profile"""
    user_id: str
    username: str
    email: str
    display_name: str
    avatar_url: str
    developer: bool
    verified: bool
    created_date: str
    total_downloads: int
    total_revenue: float

class PluginRepository:
    """Plugin repository management"""
    
    def __init__(self, repo_path: str = "marketplace_data"):
        self.repo_path = Path(repo_path)
        self.repo_path.mkdir(exist_ok=True)
        
        # Database for metadata
        self.db_path = self.repo_path / "marketplace.db"
        self.init_database()
        
        # Storage directories
        self.plugins_dir = self.repo_path / "plugins"
        self.icons_dir = self.repo_path / "icons"
        self.screenshots_dir = self.repo_path / "screenshots"
        
        for dir_path in [self.plugins_dir, self.icons_dir, self.screenshots_dir]:
            dir_path.mkdir(exist_ok=True)
    
    def init_database(self):
        """Initialize marketplace database"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            
            # Plugin listings table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS plugin_listings (
                    plugin_id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    version TEXT NOT NULL,
                    description TEXT,
                    author TEXT NOT NULL,
                    author_id TEXT NOT NULL,
                    category TEXT,
                    tags TEXT,
                    price REAL DEFAULT 0.0,
                    currency TEXT DEFAULT 'USD',
                    license TEXT,
                    download_url TEXT,
                    icon_url TEXT,
                    screenshots TEXT,
                    rating REAL DEFAULT 0.0,
                    review_count INTEGER DEFAULT 0,
                    download_count INTEGER DEFAULT 0,
                    last_updated TEXT,
                    created_date TEXT,
                    verified BOOLEAN DEFAULT FALSE,
                    featured BOOLEAN DEFAULT FALSE,
                    compatibility TEXT,
                    file_size INTEGER,
                    checksum TEXT
                )
            ''')
            
            # Plugin reviews table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS plugin_reviews (
                    review_id TEXT PRIMARY KEY,
                    plugin_id TEXT NOT NULL,
                    user_id TEXT NOT NULL,
                    username TEXT NOT NULL,
                    rating INTEGER NOT NULL,
                    title TEXT,
                    content TEXT,
                    created_date TEXT,
                    helpful_count INTEGER DEFAULT 0,
                    verified_purchase BOOLEAN DEFAULT FALSE,
                    FOREIGN KEY (plugin_id) REFERENCES plugin_listings (plugin_id)
                )
            ''')
            
            # Users table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS marketplace_users (
                    user_id TEXT PRIMARY KEY,
                    username TEXT UNIQUE NOT NULL,
                    email TEXT UNIQUE NOT NULL,
                    display_name TEXT,
                    avatar_url TEXT,
                    developer BOOLEAN DEFAULT FALSE,
                    verified BOOLEAN DEFAULT FALSE,
                    created_date TEXT,
                    total_downloads INTEGER DEFAULT 0,
                    total_revenue REAL DEFAULT 0.0
                )
            ''')
            
            # Downloads table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS plugin_downloads (
                    download_id TEXT PRIMARY KEY,
                    plugin_id TEXT NOT NULL,
                    user_id TEXT,
                    download_date TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    FOREIGN KEY (plugin_id) REFERENCES plugin_listings (plugin_id)
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("Marketplace database initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing marketplace database: {e}")
    
    def add_plugin_listing(self, listing: PluginListing) -> bool:
        """Add a new plugin listing to the repository"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO plugin_listings VALUES (
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                )
            ''', (
                listing.plugin_id, listing.name, listing.version, listing.description,
                listing.author, listing.author_id, listing.category, json.dumps(listing.tags),
                listing.price, listing.currency, listing.license, listing.download_url,
                listing.icon_url, json.dumps(listing.screenshots), listing.rating,
                listing.review_count, listing.download_count, listing.last_updated,
                listing.created_date, listing.verified, listing.featured,
                json.dumps(listing.compatibility), listing.file_size, listing.checksum
            ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"Added plugin listing: {listing.name}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding plugin listing: {e}")
            return False
    
    def get_plugin_listing(self, plugin_id: str) -> Optional[PluginListing]:
        """Get a specific plugin listing"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            
            cursor.execute('SELECT * FROM plugin_listings WHERE plugin_id = ?', (plugin_id,))
            row = cursor.fetchone()
            
            conn.close()
            
            if row:
                return self._row_to_plugin_listing(row)
            return None
            
        except Exception as e:
            logger.error(f"Error getting plugin listing: {e}")
            return None
    
    def search_plugins(self, query: str = "", category: str = "", 
                      tags: List[str] = None, sort_by: str = "rating",
                      limit: int = 50, offset: int = 0) -> List[PluginListing]:
        """Search for plugins in the repository"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            
            # Build search query
            where_conditions = []
            params = []
            
            if query:
                where_conditions.append("(name LIKE ? OR description LIKE ?)")
                params.extend([f"%{query}%", f"%{query}%"])
            
            if category:
                where_conditions.append("category = ?")
                params.append(category)
            
            if tags:
                for tag in tags:
                    where_conditions.append("tags LIKE ?")
                    params.append(f"%{tag}%")
            
            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
            
            # Sort options
            sort_options = {
                "rating": "rating DESC",
                "downloads": "download_count DESC",
                "newest": "created_date DESC",
                "updated": "last_updated DESC",
                "name": "name ASC"
            }
            order_clause = sort_options.get(sort_by, "rating DESC")
            
            query_sql = f'''
                SELECT * FROM plugin_listings 
                WHERE {where_clause}
                ORDER BY {order_clause}
                LIMIT ? OFFSET ?
            '''
            
            params.extend([limit, offset])
            cursor.execute(query_sql, params)
            rows = cursor.fetchall()
            
            conn.close()
            
            return [self._row_to_plugin_listing(row) for row in rows]
            
        except Exception as e:
            logger.error(f"Error searching plugins: {e}")
            return []
    
    def get_featured_plugins(self, limit: int = 10) -> List[PluginListing]:
        """Get featured plugins"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM plugin_listings 
                WHERE featured = TRUE 
                ORDER BY rating DESC, download_count DESC
                LIMIT ?
            ''', (limit,))
            
            rows = cursor.fetchall()
            conn.close()
            
            return [self._row_to_plugin_listing(row) for row in rows]
            
        except Exception as e:
            logger.error(f"Error getting featured plugins: {e}")
            return []
    
    def get_categories(self) -> List[Dict[str, Any]]:
        """Get all plugin categories with counts"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT category, COUNT(*) as count 
                FROM plugin_listings 
                GROUP BY category 
                ORDER BY count DESC
            ''')
            
            rows = cursor.fetchall()
            conn.close()
            
            return [{"category": row[0], "count": row[1]} for row in rows]
            
        except Exception as e:
            logger.error(f"Error getting categories: {e}")
            return []
    
    def _row_to_plugin_listing(self, row) -> PluginListing:
        """Convert database row to PluginListing object"""
        return PluginListing(
            plugin_id=row[0], name=row[1], version=row[2], description=row[3],
            author=row[4], author_id=row[5], category=row[6], 
            tags=json.loads(row[7]) if row[7] else [],
            price=row[8], currency=row[9], license=row[10], download_url=row[11],
            icon_url=row[12], screenshots=json.loads(row[13]) if row[13] else [],
            rating=row[14], review_count=row[15], download_count=row[16],
            last_updated=row[17], created_date=row[18], verified=bool(row[19]),
            featured=bool(row[20]), compatibility=json.loads(row[21]) if row[21] else [],
            file_size=row[22], checksum=row[23]
        )

class MarketplaceAPI:
    """API for marketplace operations"""
    
    def __init__(self, repository: PluginRepository):
        self.repository = repository
        self.base_url = "https://api.voiceai-marketplace.com"  # Placeholder
    
    def upload_plugin(self, plugin_file: str, metadata: Dict[str, Any], 
                     author_id: str) -> Dict[str, Any]:
        """Upload a plugin to the marketplace"""
        try:
            # Validate plugin file
            if not os.path.exists(plugin_file):
                return {"success": False, "error": "Plugin file not found"}
            
            # Calculate checksum
            with open(plugin_file, 'rb') as f:
                file_content = f.read()
                checksum = hashlib.sha256(file_content).hexdigest()
            
            # Generate plugin ID
            plugin_id = f"{metadata['name']}_{metadata['version']}_{author_id}"
            plugin_id = hashlib.md5(plugin_id.encode()).hexdigest()
            
            # Create plugin listing
            listing = PluginListing(
                plugin_id=plugin_id,
                name=metadata['name'],
                version=metadata['version'],
                description=metadata.get('description', ''),
                author=metadata['author'],
                author_id=author_id,
                category=metadata.get('category', 'utility'),
                tags=metadata.get('tags', []),
                price=metadata.get('price', 0.0),
                currency=metadata.get('currency', 'USD'),
                license=metadata.get('license', 'MIT'),
                download_url=f"/plugins/{plugin_id}.zip",
                icon_url=metadata.get('icon_url', ''),
                screenshots=metadata.get('screenshots', []),
                rating=0.0,
                review_count=0,
                download_count=0,
                last_updated=datetime.now().isoformat(),
                created_date=datetime.now().isoformat(),
                verified=False,
                featured=False,
                compatibility=metadata.get('compatibility', ['5.0.0']),
                file_size=len(file_content),
                checksum=checksum
            )
            
            # Store plugin file
            plugin_storage_path = self.repository.plugins_dir / f"{plugin_id}.zip"
            shutil.copy2(plugin_file, plugin_storage_path)
            
            # Add to repository
            success = self.repository.add_plugin_listing(listing)
            
            if success:
                return {
                    "success": True,
                    "plugin_id": plugin_id,
                    "message": "Plugin uploaded successfully"
                }
            else:
                return {"success": False, "error": "Failed to add plugin to repository"}
                
        except Exception as e:
            logger.error(f"Error uploading plugin: {e}")
            return {"success": False, "error": str(e)}
    
    def download_plugin(self, plugin_id: str, user_id: str = None) -> Dict[str, Any]:
        """Download a plugin from the marketplace"""
        try:
            # Get plugin listing
            listing = self.repository.get_plugin_listing(plugin_id)
            if not listing:
                return {"success": False, "error": "Plugin not found"}
            
            # Check if plugin file exists
            plugin_file = self.repository.plugins_dir / f"{plugin_id}.zip"
            if not plugin_file.exists():
                return {"success": False, "error": "Plugin file not available"}
            
            # Record download
            self._record_download(plugin_id, user_id)
            
            return {
                "success": True,
                "plugin_file": str(plugin_file),
                "checksum": listing.checksum,
                "file_size": listing.file_size
            }
            
        except Exception as e:
            logger.error(f"Error downloading plugin: {e}")
            return {"success": False, "error": str(e)}
    
    def _record_download(self, plugin_id: str, user_id: str = None):
        """Record a plugin download"""
        try:
            conn = sqlite3.connect(str(self.repository.db_path))
            cursor = conn.cursor()
            
            # Record download
            download_id = hashlib.md5(f"{plugin_id}_{user_id}_{time.time()}".encode()).hexdigest()
            cursor.execute('''
                INSERT INTO plugin_downloads VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                download_id, plugin_id, user_id, datetime.now().isoformat(),
                "127.0.0.1", "VoiceAI-Client/5.0"
            ))
            
            # Update download count
            cursor.execute('''
                UPDATE plugin_listings 
                SET download_count = download_count + 1 
                WHERE plugin_id = ?
            ''', (plugin_id,))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error recording download: {e}")

class Marketplace:
    """Main marketplace interface"""
    
    def __init__(self, system_controller=None):
        self.system_controller = system_controller
        self.repository = PluginRepository()
        self.api = MarketplaceAPI(self.repository)
        
        # Initialize with sample plugins
        self._initialize_sample_plugins()
    
    def _initialize_sample_plugins(self):
        """Initialize marketplace with sample plugins"""
        sample_plugins = [
            {
                "name": "Weather Assistant",
                "version": "1.0.0",
                "description": "Get weather information using voice commands",
                "author": "VoiceAI Team",
                "author_id": "voiceai_official",
                "category": "productivity",
                "tags": ["weather", "information", "voice"],
                "license": "MIT",
                "compatibility": ["5.0.0"]
            },
            {
                "name": "Smart Home Controller",
                "version": "2.1.0",
                "description": "Control smart home devices with voice commands",
                "author": "SmartHome Inc",
                "author_id": "smarthome_inc",
                "category": "automation",
                "tags": ["smart-home", "iot", "automation"],
                "license": "Apache-2.0",
                "compatibility": ["5.0.0"]
            },
            {
                "name": "Code Assistant",
                "version": "1.5.0",
                "description": "AI-powered coding assistance and code generation",
                "author": "DevTools Pro",
                "author_id": "devtools_pro",
                "category": "development",
                "tags": ["coding", "ai", "development"],
                "license": "GPL-3.0",
                "compatibility": ["5.0.0"]
            }
        ]
        
        for plugin_data in sample_plugins:
            plugin_id = hashlib.md5(f"{plugin_data['name']}_{plugin_data['version']}".encode()).hexdigest()
            
            listing = PluginListing(
                plugin_id=plugin_id,
                name=plugin_data['name'],
                version=plugin_data['version'],
                description=plugin_data['description'],
                author=plugin_data['author'],
                author_id=plugin_data['author_id'],
                category=plugin_data['category'],
                tags=plugin_data['tags'],
                price=0.0,
                currency='USD',
                license=plugin_data['license'],
                download_url=f"/plugins/{plugin_id}.zip",
                icon_url="",
                screenshots=[],
                rating=4.5,
                review_count=25,
                download_count=1000,
                last_updated=datetime.now().isoformat(),
                created_date=(datetime.now() - timedelta(days=30)).isoformat(),
                verified=True,
                featured=True,
                compatibility=plugin_data['compatibility'],
                file_size=1024000,
                checksum="sample_checksum"
            )
            
            self.repository.add_plugin_listing(listing)
    
    def search_plugins(self, query: str = "", **kwargs) -> List[PluginListing]:
        """Search for plugins in the marketplace"""
        return self.repository.search_plugins(query, **kwargs)
    
    def get_featured_plugins(self) -> List[PluginListing]:
        """Get featured plugins"""
        return self.repository.get_featured_plugins()
    
    def get_plugin_details(self, plugin_id: str) -> Optional[PluginListing]:
        """Get detailed information about a plugin"""
        return self.repository.get_plugin_listing(plugin_id)
    
    def install_plugin_from_marketplace(self, plugin_id: str) -> Dict[str, Any]:
        """Install a plugin from the marketplace"""
        try:
            # Download plugin
            download_result = self.api.download_plugin(plugin_id)
            
            if not download_result["success"]:
                return download_result
            
            # Install plugin (integrate with plugin framework)
            if self.system_controller and hasattr(self.system_controller, 'plugin_framework'):
                success = self.system_controller.plugin_framework.install_plugin(
                    download_result["plugin_file"]
                )
                
                if success:
                    return {"success": True, "message": "Plugin installed successfully"}
                else:
                    return {"success": False, "error": "Failed to install plugin"}
            
            return {"success": True, "message": "Plugin downloaded successfully"}
            
        except Exception as e:
            logger.error(f"Error installing plugin from marketplace: {e}")
            return {"success": False, "error": str(e)}
