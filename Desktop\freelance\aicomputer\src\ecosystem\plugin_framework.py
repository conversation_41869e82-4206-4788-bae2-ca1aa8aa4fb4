"""
Plugin Framework - Core Plugin Architecture

This module provides the foundation for third-party plugin development and management.
It includes plugin discovery, loading, lifecycle management, and security sandboxing.
"""

import os
import sys
import json
import importlib
import importlib.util
from typing import Dict, List, Any, Optional, Callable
from abc import ABC, abstractmethod
from dataclasses import dataclass, asdict
from pathlib import Path
import logging
import hashlib
import subprocess
import threading
import time
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class PluginMetadata:
    """Plugin metadata structure"""
    name: str
    version: str
    description: str
    author: str
    email: str
    website: str
    license: str
    dependencies: List[str]
    permissions: List[str]
    api_version: str
    min_system_version: str
    category: str
    tags: List[str]
    install_date: Optional[str] = None
    last_updated: Optional[str] = None
    enabled: bool = True
    verified: bool = False
    rating: float = 0.0
    downloads: int = 0

class PluginInterface(ABC):
    """Abstract base class for all plugins"""
    
    @abstractmethod
    def initialize(self, api_manager) -> bool:
        """Initialize the plugin with API access"""
        pass
    
    @abstractmethod
    def get_commands(self) -> Dict[str, Callable]:
        """Return dictionary of voice commands this plugin handles"""
        pass
    
    @abstractmethod
    def cleanup(self) -> None:
        """Cleanup resources when plugin is disabled/uninstalled"""
        pass
    
    @abstractmethod
    def get_metadata(self) -> PluginMetadata:
        """Return plugin metadata"""
        pass
    
    def on_voice_command(self, command: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle voice command (optional override)"""
        return {"status": "not_handled"}
    
    def on_system_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """Handle system events (optional override)"""
        pass

class PluginSandbox:
    """Security sandbox for plugin execution"""
    
    def __init__(self, plugin_name: str):
        self.plugin_name = plugin_name
        self.allowed_modules = {
            'os', 'sys', 'json', 'time', 'datetime', 'math', 'random',
            'requests', 'urllib', 'pathlib', 'typing', 'dataclasses',
            'logging', 'threading', 'queue', 'collections'
        }
        self.restricted_functions = {
            'eval', 'exec', 'compile', '__import__', 'open', 'file',
            'input', 'raw_input', 'reload', 'vars', 'globals', 'locals'
        }
    
    def validate_plugin_code(self, plugin_path: str) -> bool:
        """Validate plugin code for security issues"""
        try:
            with open(plugin_path, 'r', encoding='utf-8') as f:
                code = f.read()
            
            # Check for restricted functions
            for func in self.restricted_functions:
                if func in code:
                    logger.warning(f"Plugin {self.plugin_name} contains restricted function: {func}")
                    return False
            
            # Basic syntax validation
            try:
                compile(code, plugin_path, 'exec')
            except SyntaxError as e:
                logger.error(f"Plugin {self.plugin_name} has syntax error: {e}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating plugin {self.plugin_name}: {e}")
            return False
    
    def execute_in_sandbox(self, func: Callable, *args, **kwargs) -> Any:
        """Execute plugin function in sandboxed environment"""
        try:
            # Set execution timeout
            result = func(*args, **kwargs)
            return result
        except Exception as e:
            logger.error(f"Error executing plugin {self.plugin_name}: {e}")
            raise

class PluginManager:
    """Core plugin management system"""
    
    def __init__(self, plugins_dir: str = "plugins"):
        self.plugins_dir = Path(plugins_dir)
        self.plugins_dir.mkdir(exist_ok=True)
        
        self.loaded_plugins: Dict[str, PluginInterface] = {}
        self.plugin_metadata: Dict[str, PluginMetadata] = {}
        self.plugin_sandboxes: Dict[str, PluginSandbox] = {}
        
        # Plugin registry file
        self.registry_file = self.plugins_dir / "registry.json"
        self.load_registry()
    
    def load_registry(self) -> None:
        """Load plugin registry from file"""
        try:
            if self.registry_file.exists():
                with open(self.registry_file, 'r', encoding='utf-8') as f:
                    registry_data = json.load(f)
                    
                for plugin_name, metadata_dict in registry_data.items():
                    self.plugin_metadata[plugin_name] = PluginMetadata(**metadata_dict)
                    
        except Exception as e:
            logger.error(f"Error loading plugin registry: {e}")
    
    def save_registry(self) -> None:
        """Save plugin registry to file"""
        try:
            registry_data = {}
            for plugin_name, metadata in self.plugin_metadata.items():
                registry_data[plugin_name] = asdict(metadata)
            
            with open(self.registry_file, 'w', encoding='utf-8') as f:
                json.dump(registry_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving plugin registry: {e}")
    
    def discover_plugins(self) -> List[str]:
        """Discover available plugins in plugins directory"""
        discovered = []
        
        for plugin_dir in self.plugins_dir.iterdir():
            if plugin_dir.is_dir():
                plugin_file = plugin_dir / "plugin.py"
                manifest_file = plugin_dir / "manifest.json"
                
                if plugin_file.exists() and manifest_file.exists():
                    discovered.append(plugin_dir.name)
        
        logger.info(f"Discovered {len(discovered)} plugins: {discovered}")
        return discovered
    
    def load_plugin(self, plugin_name: str) -> bool:
        """Load a specific plugin"""
        try:
            plugin_dir = self.plugins_dir / plugin_name
            plugin_file = plugin_dir / "plugin.py"
            manifest_file = plugin_dir / "manifest.json"
            
            if not plugin_file.exists() or not manifest_file.exists():
                logger.error(f"Plugin {plugin_name} files not found")
                return False
            
            # Load manifest
            with open(manifest_file, 'r', encoding='utf-8') as f:
                manifest = json.load(f)
            
            metadata = PluginMetadata(**manifest)
            
            # Create sandbox
            sandbox = PluginSandbox(plugin_name)
            
            # Validate plugin code
            if not sandbox.validate_plugin_code(str(plugin_file)):
                logger.error(f"Plugin {plugin_name} failed security validation")
                return False
            
            # Load plugin module
            spec = importlib.util.spec_from_file_location(
                f"plugin_{plugin_name}", plugin_file
            )
            module = importlib.util.module_from_spec(spec)
            
            # Execute in sandbox context
            spec.loader.exec_module(module)
            
            # Get plugin class
            plugin_class = getattr(module, 'Plugin', None)
            if not plugin_class:
                logger.error(f"Plugin {plugin_name} does not have Plugin class")
                return False
            
            # Instantiate plugin
            plugin_instance = plugin_class()
            
            if not isinstance(plugin_instance, PluginInterface):
                logger.error(f"Plugin {plugin_name} does not implement PluginInterface")
                return False
            
            # Store plugin data
            self.loaded_plugins[plugin_name] = plugin_instance
            self.plugin_metadata[plugin_name] = metadata
            self.plugin_sandboxes[plugin_name] = sandbox
            
            # Update metadata
            metadata.install_date = datetime.now().isoformat()
            metadata.enabled = True
            
            self.save_registry()
            
            logger.info(f"Successfully loaded plugin: {plugin_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading plugin {plugin_name}: {e}")
            return False
    
    def unload_plugin(self, plugin_name: str) -> bool:
        """Unload a specific plugin"""
        try:
            if plugin_name in self.loaded_plugins:
                plugin = self.loaded_plugins[plugin_name]
                plugin.cleanup()
                
                del self.loaded_plugins[plugin_name]
                del self.plugin_sandboxes[plugin_name]
                
                if plugin_name in self.plugin_metadata:
                    self.plugin_metadata[plugin_name].enabled = False
                
                self.save_registry()
                logger.info(f"Successfully unloaded plugin: {plugin_name}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error unloading plugin {plugin_name}: {e}")
            return False
    
    def get_plugin_commands(self) -> Dict[str, Callable]:
        """Get all commands from loaded plugins"""
        commands = {}
        
        for plugin_name, plugin in self.loaded_plugins.items():
            try:
                plugin_commands = plugin.get_commands()
                for cmd_name, cmd_func in plugin_commands.items():
                    # Prefix with plugin name to avoid conflicts
                    full_cmd_name = f"{plugin_name}_{cmd_name}"
                    commands[full_cmd_name] = cmd_func
                    
            except Exception as e:
                logger.error(f"Error getting commands from plugin {plugin_name}: {e}")
        
        return commands
    
    def execute_plugin_command(self, plugin_name: str, command: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a command from a specific plugin"""
        try:
            if plugin_name not in self.loaded_plugins:
                return {"status": "error", "message": f"Plugin {plugin_name} not loaded"}
            
            plugin = self.loaded_plugins[plugin_name]
            sandbox = self.plugin_sandboxes[plugin_name]
            
            # Execute in sandbox
            result = sandbox.execute_in_sandbox(
                plugin.on_voice_command, command, context
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error executing command in plugin {plugin_name}: {e}")
            return {"status": "error", "message": str(e)}

class PluginFramework:
    """Main plugin framework interface"""
    
    def __init__(self, system_controller=None):
        self.system_controller = system_controller
        self.plugin_manager = PluginManager()
        self.api_manager = None  # Will be set by DeveloperSDK
        
        # Initialize framework
        self.initialize()
    
    def initialize(self) -> None:
        """Initialize the plugin framework"""
        logger.info("Initializing Plugin Framework...")
        
        # Discover and load plugins
        discovered_plugins = self.plugin_manager.discover_plugins()
        
        for plugin_name in discovered_plugins:
            if plugin_name in self.plugin_manager.plugin_metadata:
                metadata = self.plugin_manager.plugin_metadata[plugin_name]
                if metadata.enabled:
                    self.plugin_manager.load_plugin(plugin_name)
        
        logger.info("Plugin Framework initialized successfully")
    
    def install_plugin(self, plugin_path: str) -> bool:
        """Install a new plugin from file/directory"""
        # Implementation for plugin installation
        pass
    
    def get_available_plugins(self) -> List[PluginMetadata]:
        """Get list of all available plugins"""
        return list(self.plugin_manager.plugin_metadata.values())
    
    def get_loaded_plugins(self) -> List[str]:
        """Get list of currently loaded plugins"""
        return list(self.plugin_manager.loaded_plugins.keys())
