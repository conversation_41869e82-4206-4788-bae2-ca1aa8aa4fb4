"""
Augmented Reality Overlay - Phase 4 Component

Advanced AR overlay system for immersive voice AI interaction.
"""

import asyncio
import time
import json
import cv2
import numpy as np
from typing import Dict, List, Optional, Any, Callable, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import tkinter as tk
from tkinter import ttk
import threading

from loguru import logger

from ..utils.config_manager import ConfigManager


class OverlayType(Enum):
    """AR overlay types."""
    COMMAND_FEEDBACK = "command_feedback"
    SYSTEM_STATUS = "system_status"
    DEVICE_CONTROL = "device_control"
    NAVIGATION = "navigation"
    INFORMATION = "information"
    NOTIFICATION = "notification"
    GESTURE_GUIDE = "gesture_guide"
    VOICE_VISUALIZATION = "voice_visualization"


class OverlayPosition(Enum):
    """Overlay position on screen."""
    TOP_LEFT = "top_left"
    TOP_RIGHT = "top_right"
    TOP_CENTER = "top_center"
    BOTTOM_LEFT = "bottom_left"
    BOTTOM_RIGHT = "bottom_right"
    BOTTOM_CENTER = "bottom_center"
    CENTER = "center"
    FLOATING = "floating"


@dataclass
class ARElement:
    """AR overlay element."""
    element_id: str
    overlay_type: OverlayType
    position: OverlayPosition
    content: Dict[str, Any]
    visibility: bool
    opacity: float
    duration: Optional[float]
    created_at: float
    coordinates: Optional[Tuple[int, int]] = None
    size: Optional[Tuple[int, int]] = None
    animation: Optional[str] = None


@dataclass
class VoiceVisualization:
    """Voice activity visualization."""
    amplitude: float
    frequency_bands: List[float]
    speaking: bool
    confidence: float
    timestamp: float


@dataclass
class GestureGuide:
    """Gesture guidance overlay."""
    gesture_name: str
    description: str
    demonstration_points: List[Tuple[int, int]]
    current_step: int
    total_steps: int


class AugmentedRealityOverlay:
    """
    Advanced augmented reality overlay system.
    
    Features:
    - Real-time voice activity visualization
    - Interactive command feedback overlays
    - System status and device control displays
    - Gesture guidance and tutorials
    - Contextual information overlays
    - Adaptive UI based on user attention
    - Multi-modal interaction indicators
    - Immersive notification system
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        
        # Overlay management
        self.active_elements: Dict[str, ARElement] = {}
        self.overlay_history: List[ARElement] = []
        
        # Visualization data
        self.voice_visualization: Optional[VoiceVisualization] = None
        self.gesture_guides: Dict[str, GestureGuide] = {}
        
        # Display components
        self.overlay_window: Optional[tk.Tk] = None
        self.canvas: Optional[tk.Canvas] = None
        self.overlay_active = False
        
        # Configuration
        self.overlay_enabled = self.config.get("experience.ar.enabled", True)
        self.default_opacity = self.config.get("experience.ar.default_opacity", 0.8)
        self.animation_speed = self.config.get("experience.ar.animation_speed", 1.0)
        self.max_elements = self.config.get("experience.ar.max_elements", 10)
        
        # Callbacks
        self.on_element_created: Optional[Callable[[ARElement], None]] = None
        self.on_element_removed: Optional[Callable[[str], None]] = None
        self.on_user_interaction: Optional[Callable[[str, Dict[str, Any]], None]] = None
        
        # Initialize AR overlay
        if self.overlay_enabled:
            self._initialize_overlay()
        
        logger.info("Augmented Reality Overlay initialized")
    
    def _initialize_overlay(self):
        """Initialize AR overlay system."""
        
        try:
            # Initialize gesture guides
            self._initialize_gesture_guides()
            
            # Start overlay in separate thread
            self.overlay_thread = threading.Thread(target=self._run_overlay_window, daemon=True)
            self.overlay_thread.start()
            
            # Start background tasks
            asyncio.create_task(self._overlay_management_loop())
            
        except Exception as e:
            logger.error(f"Error initializing AR overlay: {e}")
    
    def _initialize_gesture_guides(self):
        """Initialize gesture guidance overlays."""
        
        self.gesture_guides = {
            "point": GestureGuide(
                gesture_name="Point",
                description="Extend your index finger to point at objects",
                demonstration_points=[(100, 200), (150, 180), (200, 160)],
                current_step=0,
                total_steps=3
            ),
            "wave": GestureGuide(
                gesture_name="Wave",
                description="Open your hand and move it side to side",
                demonstration_points=[(100, 200), (200, 200), (100, 200), (200, 200)],
                current_step=0,
                total_steps=4
            ),
            "thumbs_up": GestureGuide(
                gesture_name="Thumbs Up",
                description="Extend your thumb upward while keeping other fingers folded",
                demonstration_points=[(150, 250), (150, 200), (150, 150)],
                current_step=0,
                total_steps=3
            )
        }
    
    def _run_overlay_window(self):
        """Run the AR overlay window in a separate thread."""
        
        try:
            self.overlay_window = tk.Tk()
            self.overlay_window.title("Voice AI AR Overlay")
            self.overlay_window.attributes("-topmost", True)
            self.overlay_window.attributes("-alpha", self.default_opacity)
            self.overlay_window.configure(bg='black')
            
            # Make window transparent and click-through
            self.overlay_window.wm_attributes("-transparentcolor", "black")
            
            # Set window size and position
            screen_width = self.overlay_window.winfo_screenwidth()
            screen_height = self.overlay_window.winfo_screenheight()
            
            window_width = int(screen_width * 0.8)
            window_height = int(screen_height * 0.8)
            
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2
            
            self.overlay_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
            
            # Create canvas for drawing
            self.canvas = tk.Canvas(
                self.overlay_window,
                width=window_width,
                height=window_height,
                bg='black',
                highlightthickness=0
            )
            self.canvas.pack(fill=tk.BOTH, expand=True)
            
            self.overlay_active = True
            
            # Start periodic updates
            self._schedule_overlay_update()
            
            # Run the window
            self.overlay_window.mainloop()
            
        except Exception as e:
            logger.error(f"Error running overlay window: {e}")
    
    def _schedule_overlay_update(self):
        """Schedule periodic overlay updates."""
        
        try:
            if self.overlay_active and self.overlay_window:
                self._update_overlay_display()
                self.overlay_window.after(50, self._schedule_overlay_update)  # 20 FPS
                
        except Exception as e:
            logger.error(f"Error scheduling overlay update: {e}")
    
    def _update_overlay_display(self):
        """Update the AR overlay display."""
        
        try:
            if not self.canvas:
                return
            
            # Clear canvas
            self.canvas.delete("all")
            
            # Draw active elements
            for element in self.active_elements.values():
                if element.visibility:
                    self._draw_element(element)
            
            # Draw voice visualization
            if self.voice_visualization:
                self._draw_voice_visualization()
            
        except Exception as e:
            logger.error(f"Error updating overlay display: {e}")
    
    def _draw_element(self, element: ARElement):
        """Draw an AR element on the overlay."""
        
        try:
            if not self.canvas:
                return
            
            # Get position coordinates
            x, y = self._get_position_coordinates(element.position, element.coordinates)
            
            if element.overlay_type == OverlayType.COMMAND_FEEDBACK:
                self._draw_command_feedback(element, x, y)
            elif element.overlay_type == OverlayType.SYSTEM_STATUS:
                self._draw_system_status(element, x, y)
            elif element.overlay_type == OverlayType.NOTIFICATION:
                self._draw_notification(element, x, y)
            elif element.overlay_type == OverlayType.GESTURE_GUIDE:
                self._draw_gesture_guide(element, x, y)
            elif element.overlay_type == OverlayType.DEVICE_CONTROL:
                self._draw_device_control(element, x, y)
            
        except Exception as e:
            logger.error(f"Error drawing element: {e}")
    
    def _get_position_coordinates(self, position: OverlayPosition, 
                                 custom_coords: Optional[Tuple[int, int]]) -> Tuple[int, int]:
        """Get screen coordinates for overlay position."""
        
        if custom_coords:
            return custom_coords
        
        if not self.canvas:
            return (0, 0)
        
        width = self.canvas.winfo_width()
        height = self.canvas.winfo_height()
        
        position_map = {
            OverlayPosition.TOP_LEFT: (50, 50),
            OverlayPosition.TOP_RIGHT: (width - 200, 50),
            OverlayPosition.TOP_CENTER: (width // 2, 50),
            OverlayPosition.BOTTOM_LEFT: (50, height - 100),
            OverlayPosition.BOTTOM_RIGHT: (width - 200, height - 100),
            OverlayPosition.BOTTOM_CENTER: (width // 2, height - 100),
            OverlayPosition.CENTER: (width // 2, height // 2),
            OverlayPosition.FLOATING: (width // 2, height // 2)
        }
        
        return position_map.get(position, (width // 2, height // 2))
    
    def _draw_command_feedback(self, element: ARElement, x: int, y: int):
        """Draw command feedback overlay."""
        
        try:
            content = element.content
            command = content.get("command", "Unknown")
            status = content.get("status", "processing")
            confidence = content.get("confidence", 0.0)
            
            # Background
            self.canvas.create_rectangle(
                x - 100, y - 30, x + 100, y + 30,
                fill="darkblue", outline="lightblue", width=2
            )
            
            # Command text
            self.canvas.create_text(
                x, y - 10, text=f"Command: {command}",
                fill="white", font=("Arial", 10, "bold")
            )
            
            # Status
            status_color = "green" if status == "completed" else "yellow" if status == "processing" else "red"
            self.canvas.create_text(
                x, y + 10, text=f"Status: {status}",
                fill=status_color, font=("Arial", 9)
            )
            
            # Confidence bar
            bar_width = int(confidence * 80)
            self.canvas.create_rectangle(
                x - 40, y + 20, x - 40 + bar_width, y + 25,
                fill="lightgreen", outline="green"
            )
            
        except Exception as e:
            logger.error(f"Error drawing command feedback: {e}")
    
    def _draw_system_status(self, element: ARElement, x: int, y: int):
        """Draw system status overlay."""
        
        try:
            content = element.content
            cpu_usage = content.get("cpu_usage", 0)
            memory_usage = content.get("memory_usage", 0)
            active_devices = content.get("active_devices", 0)
            
            # Background
            self.canvas.create_rectangle(
                x - 80, y - 40, x + 80, y + 40,
                fill="darkgreen", outline="lightgreen", width=2
            )
            
            # Title
            self.canvas.create_text(
                x, y - 25, text="System Status",
                fill="white", font=("Arial", 10, "bold")
            )
            
            # Metrics
            self.canvas.create_text(
                x, y - 5, text=f"CPU: {cpu_usage}%",
                fill="white", font=("Arial", 8)
            )
            
            self.canvas.create_text(
                x, y + 10, text=f"Memory: {memory_usage}%",
                fill="white", font=("Arial", 8)
            )
            
            self.canvas.create_text(
                x, y + 25, text=f"Devices: {active_devices}",
                fill="white", font=("Arial", 8)
            )
            
        except Exception as e:
            logger.error(f"Error drawing system status: {e}")
    
    def _draw_notification(self, element: ARElement, x: int, y: int):
        """Draw notification overlay."""
        
        try:
            content = element.content
            title = content.get("title", "Notification")
            message = content.get("message", "")
            priority = content.get("priority", "normal")
            
            # Color based on priority
            colors = {
                "low": ("darkgray", "lightgray"),
                "normal": ("darkblue", "lightblue"),
                "high": ("darkorange", "orange"),
                "critical": ("darkred", "red")
            }
            
            bg_color, border_color = colors.get(priority, colors["normal"])
            
            # Background
            self.canvas.create_rectangle(
                x - 120, y - 35, x + 120, y + 35,
                fill=bg_color, outline=border_color, width=3
            )
            
            # Title
            self.canvas.create_text(
                x, y - 15, text=title,
                fill="white", font=("Arial", 11, "bold")
            )
            
            # Message
            self.canvas.create_text(
                x, y + 5, text=message,
                fill="white", font=("Arial", 9), width=200
            )
            
            # Close button
            self.canvas.create_rectangle(
                x + 100, y - 30, x + 115, y - 15,
                fill="red", outline="darkred"
            )
            self.canvas.create_text(
                x + 107, y - 22, text="×",
                fill="white", font=("Arial", 12, "bold")
            )
            
        except Exception as e:
            logger.error(f"Error drawing notification: {e}")
    
    def _draw_gesture_guide(self, element: ARElement, x: int, y: int):
        """Draw gesture guide overlay."""
        
        try:
            content = element.content
            gesture_name = content.get("gesture_name", "")
            description = content.get("description", "")
            step = content.get("current_step", 0)
            total_steps = content.get("total_steps", 1)
            
            # Background
            self.canvas.create_rectangle(
                x - 150, y - 60, x + 150, y + 60,
                fill="purple", outline="magenta", width=2
            )
            
            # Title
            self.canvas.create_text(
                x, y - 40, text=f"Gesture Guide: {gesture_name}",
                fill="white", font=("Arial", 12, "bold")
            )
            
            # Description
            self.canvas.create_text(
                x, y - 15, text=description,
                fill="white", font=("Arial", 9), width=280
            )
            
            # Progress
            self.canvas.create_text(
                x, y + 10, text=f"Step {step + 1} of {total_steps}",
                fill="yellow", font=("Arial", 10)
            )
            
            # Progress bar
            progress = (step + 1) / total_steps
            bar_width = int(progress * 200)
            self.canvas.create_rectangle(
                x - 100, y + 30, x - 100 + bar_width, y + 40,
                fill="lightgreen", outline="green"
            )
            
        except Exception as e:
            logger.error(f"Error drawing gesture guide: {e}")
    
    def _draw_device_control(self, element: ARElement, x: int, y: int):
        """Draw device control overlay."""
        
        try:
            content = element.content
            device_name = content.get("device_name", "Device")
            device_type = content.get("device_type", "unknown")
            status = content.get("status", "unknown")
            controls = content.get("controls", [])
            
            # Background
            self.canvas.create_rectangle(
                x - 100, y - 50, x + 100, y + 50,
                fill="teal", outline="cyan", width=2
            )
            
            # Device name
            self.canvas.create_text(
                x, y - 35, text=device_name,
                fill="white", font=("Arial", 11, "bold")
            )
            
            # Device type and status
            self.canvas.create_text(
                x, y - 15, text=f"{device_type} - {status}",
                fill="lightgray", font=("Arial", 8)
            )
            
            # Controls
            for i, control in enumerate(controls[:3]):  # Max 3 controls
                control_y = y + 5 + (i * 15)
                self.canvas.create_text(
                    x, control_y, text=control,
                    fill="yellow", font=("Arial", 8)
                )
            
        except Exception as e:
            logger.error(f"Error drawing device control: {e}")
    
    def _draw_voice_visualization(self):
        """Draw voice activity visualization."""
        
        try:
            if not self.voice_visualization or not self.canvas:
                return
            
            # Get canvas dimensions
            width = self.canvas.winfo_width()
            height = self.canvas.winfo_height()
            
            # Voice activity indicator
            if self.voice_visualization.speaking:
                # Pulsing circle for speaking
                radius = int(20 + self.voice_visualization.amplitude * 30)
                color_intensity = int(255 * self.voice_visualization.confidence)
                color = f"#{color_intensity:02x}{255-color_intensity:02x}00"
                
                self.canvas.create_oval(
                    50 - radius, height - 100 - radius,
                    50 + radius, height - 100 + radius,
                    fill=color, outline="white", width=2
                )
                
                # Confidence text
                self.canvas.create_text(
                    50, height - 60, text=f"Confidence: {self.voice_visualization.confidence:.2f}",
                    fill="white", font=("Arial", 8)
                )
            
            # Frequency visualization
            if self.voice_visualization.frequency_bands:
                bar_width = 5
                bar_spacing = 7
                start_x = width - 200
                
                for i, amplitude in enumerate(self.voice_visualization.frequency_bands[:20]):
                    bar_height = int(amplitude * 100)
                    x = start_x + (i * bar_spacing)
                    
                    self.canvas.create_rectangle(
                        x, height - 50,
                        x + bar_width, height - 50 - bar_height,
                        fill="cyan", outline="blue"
                    )
            
        except Exception as e:
            logger.error(f"Error drawing voice visualization: {e}")
    
    async def _overlay_management_loop(self):
        """Overlay management loop."""
        
        try:
            while self.overlay_active:
                await asyncio.sleep(0.1)  # 10 FPS management
                
                current_time = time.time()
                expired_elements = []
                
                # Check for expired elements
                for element_id, element in self.active_elements.items():
                    if (element.duration and 
                        current_time - element.created_at > element.duration):
                        expired_elements.append(element_id)
                
                # Remove expired elements
                for element_id in expired_elements:
                    await self.remove_element(element_id)
                
                # Limit number of active elements
                if len(self.active_elements) > self.max_elements:
                    oldest_element = min(
                        self.active_elements.values(),
                        key=lambda e: e.created_at
                    )
                    await self.remove_element(oldest_element.element_id)
                
        except Exception as e:
            logger.error(f"Error in overlay management loop: {e}")
    
    async def create_element(self, overlay_type: OverlayType, content: Dict[str, Any],
                           position: OverlayPosition = OverlayPosition.CENTER,
                           duration: Optional[float] = None,
                           coordinates: Optional[Tuple[int, int]] = None) -> str:
        """Create a new AR overlay element."""
        
        try:
            element_id = f"{overlay_type.value}_{int(time.time() * 1000)}"
            
            element = ARElement(
                element_id=element_id,
                overlay_type=overlay_type,
                position=position,
                content=content,
                visibility=True,
                opacity=self.default_opacity,
                duration=duration,
                created_at=time.time(),
                coordinates=coordinates
            )
            
            self.active_elements[element_id] = element
            
            # Trigger callback
            if self.on_element_created:
                self.on_element_created(element)
            
            logger.debug(f"Created AR element: {element_id}")
            return element_id
            
        except Exception as e:
            logger.error(f"Error creating AR element: {e}")
            return ""
    
    async def remove_element(self, element_id: str):
        """Remove an AR overlay element."""
        
        try:
            if element_id in self.active_elements:
                element = self.active_elements[element_id]
                self.overlay_history.append(element)
                del self.active_elements[element_id]
                
                # Trigger callback
                if self.on_element_removed:
                    self.on_element_removed(element_id)
                
                logger.debug(f"Removed AR element: {element_id}")
                
        except Exception as e:
            logger.error(f"Error removing AR element: {e}")
    
    async def update_voice_visualization(self, amplitude: float, frequency_bands: List[float],
                                       speaking: bool, confidence: float):
        """Update voice activity visualization."""
        
        try:
            self.voice_visualization = VoiceVisualization(
                amplitude=amplitude,
                frequency_bands=frequency_bands,
                speaking=speaking,
                confidence=confidence,
                timestamp=time.time()
            )
            
        except Exception as e:
            logger.error(f"Error updating voice visualization: {e}")
    
    async def show_command_feedback(self, command: str, status: str, confidence: float,
                                  duration: float = 3.0):
        """Show command feedback overlay."""
        
        content = {
            "command": command,
            "status": status,
            "confidence": confidence
        }
        
        return await self.create_element(
            OverlayType.COMMAND_FEEDBACK,
            content,
            OverlayPosition.TOP_CENTER,
            duration
        )
    
    async def show_notification(self, title: str, message: str, priority: str = "normal",
                              duration: float = 5.0):
        """Show notification overlay."""
        
        content = {
            "title": title,
            "message": message,
            "priority": priority
        }
        
        return await self.create_element(
            OverlayType.NOTIFICATION,
            content,
            OverlayPosition.TOP_RIGHT,
            duration
        )
    
    async def show_gesture_guide(self, gesture_name: str, duration: float = 10.0):
        """Show gesture guide overlay."""
        
        if gesture_name in self.gesture_guides:
            guide = self.gesture_guides[gesture_name]
            content = asdict(guide)
            
            return await self.create_element(
                OverlayType.GESTURE_GUIDE,
                content,
                OverlayPosition.CENTER,
                duration
            )
        
        return ""
    
    async def show_system_status(self, cpu_usage: int, memory_usage: int, 
                               active_devices: int, duration: float = 8.0):
        """Show system status overlay."""
        
        content = {
            "cpu_usage": cpu_usage,
            "memory_usage": memory_usage,
            "active_devices": active_devices
        }
        
        return await self.create_element(
            OverlayType.SYSTEM_STATUS,
            content,
            OverlayPosition.BOTTOM_LEFT,
            duration
        )
    
    def get_overlay_status(self) -> Dict[str, Any]:
        """Get AR overlay status."""
        
        try:
            return {
                "overlay_enabled": self.overlay_enabled,
                "overlay_active": self.overlay_active,
                "active_elements": len(self.active_elements),
                "total_elements_created": len(self.overlay_history) + len(self.active_elements),
                "voice_visualization_active": self.voice_visualization is not None,
                "gesture_guides_available": list(self.gesture_guides.keys())
            }
            
        except Exception as e:
            logger.error(f"Error getting overlay status: {e}")
            return {"error": str(e)}
    
    def cleanup(self):
        """Clean up AR overlay resources."""
        
        try:
            self.overlay_active = False
            
            if self.overlay_window:
                self.overlay_window.quit()
                self.overlay_window.destroy()
            
            logger.info("AR overlay cleaned up")

        except Exception as e:
            logger.error(f"Error during AR overlay cleanup: {e}")


# Alias for compatibility
AROverlaySystem = AugmentedRealityOverlay
