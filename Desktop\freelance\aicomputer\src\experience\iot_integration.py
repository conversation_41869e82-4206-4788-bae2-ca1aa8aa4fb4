"""
IoT Integration Hub - Phase 4 Component

Smart home and IoT device integration for comprehensive automation control.
"""

import asyncio
import time
import json
import socket
import requests
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import paho.mqtt.client as mqtt

from loguru import logger

from ..utils.config_manager import ConfigManager


class DeviceType(Enum):
    """IoT device types."""
    SMART_LIGHT = "smart_light"
    SMART_SWITCH = "smart_switch"
    SMART_THERMOSTAT = "smart_thermostat"
    SMART_LOCK = "smart_lock"
    SMART_CAMERA = "smart_camera"
    SMART_SPEAKER = "smart_speaker"
    SMART_TV = "smart_tv"
    SMART_SENSOR = "smart_sensor"
    SMART_APPLIANCE = "smart_appliance"


class DeviceStatus(Enum):
    """Device status."""
    ONLINE = "online"
    OFFLINE = "offline"
    UNKNOWN = "unknown"
    ERROR = "error"


class Protocol(Enum):
    """Communication protocols."""
    MQTT = "mqtt"
    HTTP = "http"
    WEBSOCKET = "websocket"
    ZIGBEE = "zigbee"
    ZWAVE = "zwave"
    BLUETOOTH = "bluetooth"
    WIFI = "wifi"


@dataclass
class IoTDevice:
    """IoT device representation."""
    device_id: str
    name: str
    device_type: DeviceType
    protocol: Protocol
    ip_address: Optional[str]
    mac_address: Optional[str]
    status: DeviceStatus
    capabilities: List[str]
    current_state: Dict[str, Any]
    last_seen: float
    metadata: Dict[str, Any] = None


@dataclass
class DeviceCommand:
    """Device command structure."""
    command_id: str
    device_id: str
    action: str
    parameters: Dict[str, Any]
    timestamp: float
    executed: bool = False
    result: Optional[Dict[str, Any]] = None


@dataclass
class AutomationRule:
    """IoT automation rule."""
    rule_id: str
    name: str
    description: str
    triggers: List[Dict[str, Any]]
    conditions: List[Dict[str, Any]]
    actions: List[Dict[str, Any]]
    enabled: bool
    last_executed: Optional[float] = None


class IoTIntegrationHub:
    """
    Comprehensive IoT device integration and automation hub.
    
    Features:
    - Multi-protocol device discovery and management
    - Smart home automation and control
    - Voice-controlled IoT device operations
    - Intelligent automation rules and scenes
    - Real-time device monitoring and status
    - Energy management and optimization
    - Security and access control for IoT devices
    - Integration with popular IoT platforms
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        
        # Device management
        self.devices: Dict[str, IoTDevice] = {}
        self.device_commands: List[DeviceCommand] = []
        self.automation_rules: Dict[str, AutomationRule] = {}
        
        # Communication
        self.mqtt_client: Optional[mqtt.Client] = None
        self.websocket_connections: Dict[str, Any] = {}
        
        # Discovery
        self.discovery_active = False
        self.supported_protocols = [Protocol.MQTT, Protocol.HTTP, Protocol.WEBSOCKET]
        
        # Configuration
        self.mqtt_broker = self.config.get("experience.iot.mqtt_broker", "localhost")
        self.mqtt_port = self.config.get("experience.iot.mqtt_port", 1883)
        self.discovery_interval = self.config.get("experience.iot.discovery_interval", 300)
        self.device_timeout = self.config.get("experience.iot.device_timeout", 600)
        
        # Callbacks
        self.on_device_discovered: Optional[Callable[[IoTDevice], None]] = None
        self.on_device_status_changed: Optional[Callable[[str, DeviceStatus], None]] = None
        self.on_automation_triggered: Optional[Callable[[AutomationRule], None]] = None
        
        # Initialize IoT hub
        self._initialize_iot_hub()
        
        logger.info("IoT Integration Hub initialized")
    
    def _initialize_iot_hub(self):
        """Initialize IoT integration components."""
        
        try:
            # Initialize MQTT client
            self._initialize_mqtt()
            
            # Load existing devices
            self._load_devices()
            
            # Load automation rules
            self._load_automation_rules()
            
            # Start background tasks
            asyncio.create_task(self._device_discovery_loop())
            asyncio.create_task(self._device_monitoring_loop())
            asyncio.create_task(self._automation_engine_loop())
            
        except Exception as e:
            logger.error(f"Error initializing IoT hub: {e}")
    
    def _initialize_mqtt(self):
        """Initialize MQTT client for IoT communication."""
        
        try:
            self.mqtt_client = mqtt.Client()
            self.mqtt_client.on_connect = self._on_mqtt_connect
            self.mqtt_client.on_message = self._on_mqtt_message
            self.mqtt_client.on_disconnect = self._on_mqtt_disconnect
            
            # Connect to MQTT broker
            self.mqtt_client.connect_async(self.mqtt_broker, self.mqtt_port, 60)
            self.mqtt_client.loop_start()
            
            logger.info(f"MQTT client initialized for broker {self.mqtt_broker}:{self.mqtt_port}")
            
        except Exception as e:
            logger.error(f"Error initializing MQTT: {e}")
    
    def _on_mqtt_connect(self, client, userdata, flags, rc):
        """MQTT connection callback."""
        
        if rc == 0:
            logger.info("Connected to MQTT broker")
            # Subscribe to device topics
            client.subscribe("homeassistant/+/+/config")
            client.subscribe("zigbee2mqtt/+")
            client.subscribe("tasmota/+/+")
        else:
            logger.error(f"Failed to connect to MQTT broker: {rc}")
    
    def _on_mqtt_message(self, client, userdata, msg):
        """MQTT message callback."""
        
        try:
            topic = msg.topic
            payload = json.loads(msg.payload.decode())
            
            # Process device messages
            asyncio.create_task(self._process_mqtt_message(topic, payload))
            
        except Exception as e:
            logger.error(f"Error processing MQTT message: {e}")
    
    def _on_mqtt_disconnect(self, client, userdata, rc):
        """MQTT disconnect callback."""
        logger.warning("Disconnected from MQTT broker")
    
    async def _process_mqtt_message(self, topic: str, payload: Dict[str, Any]):
        """Process incoming MQTT message."""
        
        try:
            # Home Assistant discovery
            if "homeassistant" in topic:
                await self._process_homeassistant_discovery(topic, payload)
            
            # Zigbee2MQTT devices
            elif "zigbee2mqtt" in topic:
                await self._process_zigbee2mqtt_message(topic, payload)
            
            # Tasmota devices
            elif "tasmota" in topic:
                await self._process_tasmota_message(topic, payload)
            
        except Exception as e:
            logger.error(f"Error processing MQTT message: {e}")
    
    async def _process_homeassistant_discovery(self, topic: str, payload: Dict[str, Any]):
        """Process Home Assistant discovery message."""
        
        try:
            topic_parts = topic.split("/")
            if len(topic_parts) >= 4:
                component = topic_parts[1]
                object_id = topic_parts[2]
                
                device_id = f"ha_{component}_{object_id}"
                
                # Map component to device type
                device_type_map = {
                    "light": DeviceType.SMART_LIGHT,
                    "switch": DeviceType.SMART_SWITCH,
                    "climate": DeviceType.SMART_THERMOSTAT,
                    "lock": DeviceType.SMART_LOCK,
                    "camera": DeviceType.SMART_CAMERA,
                    "sensor": DeviceType.SMART_SENSOR
                }
                
                device_type = device_type_map.get(component, DeviceType.SMART_APPLIANCE)
                
                # Create device
                device = IoTDevice(
                    device_id=device_id,
                    name=payload.get("name", object_id),
                    device_type=device_type,
                    protocol=Protocol.MQTT,
                    ip_address=None,
                    mac_address=None,
                    status=DeviceStatus.ONLINE,
                    capabilities=self._extract_capabilities(payload),
                    current_state={},
                    last_seen=time.time(),
                    metadata=payload
                )
                
                await self._register_device(device)
                
        except Exception as e:
            logger.error(f"Error processing Home Assistant discovery: {e}")
    
    async def _process_zigbee2mqtt_message(self, topic: str, payload: Dict[str, Any]):
        """Process Zigbee2MQTT message."""
        
        try:
            topic_parts = topic.split("/")
            if len(topic_parts) >= 2:
                device_name = topic_parts[1]
                device_id = f"zigbee_{device_name}"
                
                # Update device state
                if device_id in self.devices:
                    device = self.devices[device_id]
                    device.current_state.update(payload)
                    device.last_seen = time.time()
                    device.status = DeviceStatus.ONLINE
                
        except Exception as e:
            logger.error(f"Error processing Zigbee2MQTT message: {e}")
    
    async def _process_tasmota_message(self, topic: str, payload: Dict[str, Any]):
        """Process Tasmota device message."""
        
        try:
            topic_parts = topic.split("/")
            if len(topic_parts) >= 2:
                device_name = topic_parts[1]
                device_id = f"tasmota_{device_name}"
                
                # Update device state
                if device_id in self.devices:
                    device = self.devices[device_id]
                    device.current_state.update(payload)
                    device.last_seen = time.time()
                    device.status = DeviceStatus.ONLINE
                
        except Exception as e:
            logger.error(f"Error processing Tasmota message: {e}")
    
    def _extract_capabilities(self, config: Dict[str, Any]) -> List[str]:
        """Extract device capabilities from configuration."""
        
        capabilities = []
        
        if "brightness" in config:
            capabilities.append("brightness_control")
        if "color_mode" in config:
            capabilities.append("color_control")
        if "temperature" in config:
            capabilities.append("temperature_control")
        if "humidity" in config:
            capabilities.append("humidity_sensing")
        if "motion" in config:
            capabilities.append("motion_detection")
        
        return capabilities
    
    async def _register_device(self, device: IoTDevice):
        """Register a new IoT device."""
        
        try:
            self.devices[device.device_id] = device
            
            # Trigger callback
            if self.on_device_discovered:
                self.on_device_discovered(device)
            
            logger.info(f"Registered IoT device: {device.name} ({device.device_type.value})")
            
        except Exception as e:
            logger.error(f"Error registering device: {e}")
    
    async def _device_discovery_loop(self):
        """Device discovery loop."""
        
        try:
            while True:
                if self.discovery_active:
                    await self._discover_devices()
                
                await asyncio.sleep(self.discovery_interval)
                
        except Exception as e:
            logger.error(f"Error in device discovery loop: {e}")
    
    async def _discover_devices(self):
        """Discover IoT devices on the network."""
        
        try:
            # HTTP device discovery
            await self._discover_http_devices()
            
            # mDNS discovery
            await self._discover_mdns_devices()
            
            # UPnP discovery
            await self._discover_upnp_devices()
            
        except Exception as e:
            logger.error(f"Error in device discovery: {e}")
    
    async def _discover_http_devices(self):
        """Discover devices via HTTP."""
        
        try:
            # Common IoT device ports and endpoints
            common_ports = [80, 8080, 8081, 8266, 8888]
            common_endpoints = ["/", "/status", "/info", "/api", "/device"]
            
            # Scan local network (simplified)
            base_ip = "192.168.1."
            
            for i in range(1, 255):
                ip = f"{base_ip}{i}"
                
                for port in common_ports:
                    try:
                        response = requests.get(f"http://{ip}:{port}/", timeout=1)
                        if response.status_code == 200:
                            await self._analyze_http_device(ip, port, response.text)
                    except:
                        continue
                        
        except Exception as e:
            logger.error(f"Error in HTTP device discovery: {e}")
    
    async def _analyze_http_device(self, ip: str, port: int, content: str):
        """Analyze discovered HTTP device."""
        
        try:
            device_id = f"http_{ip}_{port}"
            
            # Simple device type detection based on content
            device_type = DeviceType.SMART_APPLIANCE
            name = f"Device at {ip}:{port}"
            
            if "tasmota" in content.lower():
                device_type = DeviceType.SMART_SWITCH
                name = f"Tasmota Device {ip}"
            elif "esp" in content.lower():
                device_type = DeviceType.SMART_SENSOR
                name = f"ESP Device {ip}"
            
            device = IoTDevice(
                device_id=device_id,
                name=name,
                device_type=device_type,
                protocol=Protocol.HTTP,
                ip_address=ip,
                mac_address=None,
                status=DeviceStatus.ONLINE,
                capabilities=["basic_control"],
                current_state={"port": port},
                last_seen=time.time()
            )
            
            await self._register_device(device)
            
        except Exception as e:
            logger.error(f"Error analyzing HTTP device: {e}")
    
    async def _discover_mdns_devices(self):
        """Discover devices via mDNS."""
        # Simplified mDNS discovery
        # In production, would use proper mDNS library
        pass
    
    async def _discover_upnp_devices(self):
        """Discover devices via UPnP."""
        # Simplified UPnP discovery
        # In production, would use proper UPnP library
        pass
    
    async def _device_monitoring_loop(self):
        """Device monitoring and health check loop."""
        
        try:
            while True:
                await asyncio.sleep(60)  # Check every minute
                
                current_time = time.time()
                
                for device in self.devices.values():
                    # Check if device is offline
                    if current_time - device.last_seen > self.device_timeout:
                        if device.status != DeviceStatus.OFFLINE:
                            device.status = DeviceStatus.OFFLINE
                            
                            # Trigger callback
                            if self.on_device_status_changed:
                                self.on_device_status_changed(device.device_id, DeviceStatus.OFFLINE)
                            
                            logger.warning(f"Device {device.name} is now offline")
                
        except Exception as e:
            logger.error(f"Error in device monitoring loop: {e}")
    
    async def _automation_engine_loop(self):
        """Automation rule engine loop."""
        
        try:
            while True:
                await asyncio.sleep(10)  # Check every 10 seconds
                
                for rule in self.automation_rules.values():
                    if rule.enabled:
                        await self._evaluate_automation_rule(rule)
                
        except Exception as e:
            logger.error(f"Error in automation engine loop: {e}")
    
    async def _evaluate_automation_rule(self, rule: AutomationRule):
        """Evaluate an automation rule."""
        
        try:
            # Check triggers
            triggered = False
            
            for trigger in rule.triggers:
                if await self._check_trigger(trigger):
                    triggered = True
                    break
            
            if not triggered:
                return
            
            # Check conditions
            conditions_met = True
            
            for condition in rule.conditions:
                if not await self._check_condition(condition):
                    conditions_met = False
                    break
            
            if not conditions_met:
                return
            
            # Execute actions
            await self._execute_automation_actions(rule)
            
        except Exception as e:
            logger.error(f"Error evaluating automation rule: {e}")
    
    async def _check_trigger(self, trigger: Dict[str, Any]) -> bool:
        """Check if a trigger condition is met."""
        
        try:
            trigger_type = trigger.get("type")
            
            if trigger_type == "time":
                # Time-based trigger
                current_hour = time.localtime().tm_hour
                trigger_hour = trigger.get("hour", 0)
                return current_hour == trigger_hour
            
            elif trigger_type == "device_state":
                # Device state trigger
                device_id = trigger.get("device_id")
                state_key = trigger.get("state_key")
                expected_value = trigger.get("value")
                
                if device_id in self.devices:
                    device = self.devices[device_id]
                    current_value = device.current_state.get(state_key)
                    return current_value == expected_value
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking trigger: {e}")
            return False
    
    async def _check_condition(self, condition: Dict[str, Any]) -> bool:
        """Check if a condition is met."""
        
        try:
            condition_type = condition.get("type")
            
            if condition_type == "device_online":
                device_id = condition.get("device_id")
                if device_id in self.devices:
                    return self.devices[device_id].status == DeviceStatus.ONLINE
            
            elif condition_type == "time_range":
                current_hour = time.localtime().tm_hour
                start_hour = condition.get("start_hour", 0)
                end_hour = condition.get("end_hour", 23)
                return start_hour <= current_hour <= end_hour
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking condition: {e}")
            return False
    
    async def _execute_automation_actions(self, rule: AutomationRule):
        """Execute automation rule actions."""
        
        try:
            for action in rule.actions:
                await self._execute_automation_action(action)
            
            rule.last_executed = time.time()
            
            # Trigger callback
            if self.on_automation_triggered:
                self.on_automation_triggered(rule)
            
            logger.info(f"Executed automation rule: {rule.name}")
            
        except Exception as e:
            logger.error(f"Error executing automation actions: {e}")
    
    async def _execute_automation_action(self, action: Dict[str, Any]):
        """Execute a single automation action."""
        
        try:
            action_type = action.get("type")
            
            if action_type == "device_control":
                device_id = action.get("device_id")
                command = action.get("command")
                parameters = action.get("parameters", {})
                
                await self.control_device(device_id, command, parameters)
            
            elif action_type == "scene":
                scene_name = action.get("scene")
                await self.activate_scene(scene_name)
            
        except Exception as e:
            logger.error(f"Error executing automation action: {e}")
    
    async def control_device(self, device_id: str, action: str, parameters: Dict[str, Any] = None) -> bool:
        """Control an IoT device."""
        
        try:
            if device_id not in self.devices:
                logger.warning(f"Device not found: {device_id}")
                return False
            
            device = self.devices[device_id]
            
            # Create command
            command = DeviceCommand(
                command_id=f"cmd_{int(time.time() * 1000)}",
                device_id=device_id,
                action=action,
                parameters=parameters or {},
                timestamp=time.time()
            )
            
            self.device_commands.append(command)
            
            # Execute command based on protocol
            if device.protocol == Protocol.MQTT:
                success = await self._execute_mqtt_command(device, command)
            elif device.protocol == Protocol.HTTP:
                success = await self._execute_http_command(device, command)
            else:
                logger.warning(f"Unsupported protocol: {device.protocol}")
                success = False
            
            command.executed = True
            command.result = {"success": success}
            
            logger.info(f"Device control command executed: {device.name} - {action}")
            return success
            
        except Exception as e:
            logger.error(f"Error controlling device: {e}")
            return False
    
    async def _execute_mqtt_command(self, device: IoTDevice, command: DeviceCommand) -> bool:
        """Execute MQTT command."""
        
        try:
            if not self.mqtt_client:
                return False
            
            # Build MQTT topic and payload
            topic = f"zigbee2mqtt/{device.name}/set"
            payload = {command.action: command.parameters}
            
            self.mqtt_client.publish(topic, json.dumps(payload))
            return True
            
        except Exception as e:
            logger.error(f"Error executing MQTT command: {e}")
            return False
    
    async def _execute_http_command(self, device: IoTDevice, command: DeviceCommand) -> bool:
        """Execute HTTP command."""
        
        try:
            if not device.ip_address:
                return False
            
            url = f"http://{device.ip_address}/api/{command.action}"
            response = requests.post(url, json=command.parameters, timeout=5)
            
            return response.status_code == 200
            
        except Exception as e:
            logger.error(f"Error executing HTTP command: {e}")
            return False
    
    async def activate_scene(self, scene_name: str) -> bool:
        """Activate a predefined scene."""
        
        try:
            # Predefined scenes
            scenes = {
                "morning": [
                    {"device_type": "smart_light", "action": "turn_on", "brightness": 80},
                    {"device_type": "smart_thermostat", "action": "set_temperature", "temperature": 22}
                ],
                "evening": [
                    {"device_type": "smart_light", "action": "turn_on", "brightness": 40},
                    {"device_type": "smart_thermostat", "action": "set_temperature", "temperature": 20}
                ],
                "away": [
                    {"device_type": "smart_light", "action": "turn_off"},
                    {"device_type": "smart_lock", "action": "lock"}
                ]
            }
            
            if scene_name not in scenes:
                logger.warning(f"Scene not found: {scene_name}")
                return False
            
            scene_actions = scenes[scene_name]
            
            for action in scene_actions:
                device_type = DeviceType(action["device_type"])
                
                # Find devices of this type
                matching_devices = [
                    device for device in self.devices.values()
                    if device.device_type == device_type and device.status == DeviceStatus.ONLINE
                ]
                
                for device in matching_devices:
                    await self.control_device(
                        device.device_id,
                        action["action"],
                        {k: v for k, v in action.items() if k not in ["device_type", "action"]}
                    )
            
            logger.info(f"Scene activated: {scene_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error activating scene: {e}")
            return False
    
    def start_discovery(self):
        """Start device discovery."""
        self.discovery_active = True
        logger.info("IoT device discovery started")
    
    def stop_discovery(self):
        """Stop device discovery."""
        self.discovery_active = False
        logger.info("IoT device discovery stopped")
    
    def get_iot_status(self) -> Dict[str, Any]:
        """Get comprehensive IoT status."""
        
        try:
            device_counts = {}
            online_devices = 0
            
            for device in self.devices.values():
                device_type = device.device_type.value
                device_counts[device_type] = device_counts.get(device_type, 0) + 1
                
                if device.status == DeviceStatus.ONLINE:
                    online_devices += 1
            
            return {
                "total_devices": len(self.devices),
                "online_devices": online_devices,
                "device_type_distribution": device_counts,
                "automation_rules": len(self.automation_rules),
                "discovery_active": self.discovery_active,
                "mqtt_connected": self.mqtt_client.is_connected() if self.mqtt_client else False,
                "supported_protocols": [p.value for p in self.supported_protocols]
            }
            
        except Exception as e:
            logger.error(f"Error getting IoT status: {e}")
            return {"error": str(e)}
    
    def get_devices(self) -> List[IoTDevice]:
        """Get all registered devices."""
        return list(self.devices.values())
    
    def get_device(self, device_id: str) -> Optional[IoTDevice]:
        """Get a specific device."""
        return self.devices.get(device_id)
    
    def _load_devices(self):
        """Load devices from storage."""
        try:
            devices_file = Path("data/iot/devices.json")
            if devices_file.exists():
                with open(devices_file, "r") as f:
                    devices_data = json.load(f)
                    for device_data in devices_data:
                        device = IoTDevice(**device_data)
                        self.devices[device.device_id] = device
                logger.info("IoT devices loaded successfully")
        except Exception as e:
            logger.error(f"Error loading devices: {e}")
    
    def _load_automation_rules(self):
        """Load automation rules from storage."""
        try:
            rules_file = Path("data/iot/automation_rules.json")
            if rules_file.exists():
                with open(rules_file, "r") as f:
                    rules_data = json.load(f)
                    for rule_data in rules_data:
                        rule = AutomationRule(**rule_data)
                        self.automation_rules[rule.rule_id] = rule
                logger.info("Automation rules loaded successfully")
        except Exception as e:
            logger.error(f"Error loading automation rules: {e}")
    
    def save_iot_data(self):
        """Save IoT data to storage."""
        try:
            data_dir = Path("data/iot")
            data_dir.mkdir(parents=True, exist_ok=True)
            
            # Save devices
            devices_data = [asdict(device) for device in self.devices.values()]
            with open(data_dir / "devices.json", "w") as f:
                json.dump(devices_data, f, indent=2)
            
            # Save automation rules
            rules_data = [asdict(rule) for rule in self.automation_rules.values()]
            with open(data_dir / "automation_rules.json", "w") as f:
                json.dump(rules_data, f, indent=2)
            
            logger.info("IoT data saved successfully")
        except Exception as e:
            logger.error(f"Error saving IoT data: {e}")
    
    def cleanup(self):
        """Clean up IoT hub resources."""
        try:
            if self.mqtt_client:
                self.mqtt_client.loop_stop()
                self.mqtt_client.disconnect()
            
            self.discovery_active = False
            logger.info("IoT Integration Hub cleaned up")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
