"""
Multi-Modal Interface Manager - Phase 4 Component

Advanced interface management supporting voice, gesture, eye tracking, and touch inputs.
"""

import asyncio
import time
import json
import cv2
import numpy as np
from typing import Dict, List, Optional, Any, Callable, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import threading

from loguru import logger

from ..utils.config_manager import ConfigManager


class InputModality(Enum):
    """Input modality types."""
    VOICE = "voice"
    GESTURE = "gesture"
    EYE_TRACKING = "eye_tracking"
    TOUCH = "touch"
    KEYBOARD = "keyboard"
    MOUSE = "mouse"


class GestureType(Enum):
    """Gesture types."""
    POINT = "point"
    SWIPE_LEFT = "swipe_left"
    SWIPE_RIGHT = "swipe_right"
    SWIPE_UP = "swipe_up"
    SWIPE_DOWN = "swipe_down"
    PINCH = "pinch"
    SPREAD = "spread"
    CIRCLE = "circle"
    WAVE = "wave"
    THUMBS_UP = "thumbs_up"
    THUMBS_DOWN = "thumbs_down"


@dataclass
class InputEvent:
    """Multi-modal input event."""
    event_id: str
    modality: InputModality
    event_type: str
    data: Dict[str, Any]
    confidence: float
    timestamp: float
    coordinates: Optional[Tuple[int, int]] = None
    metadata: Dict[str, Any] = None


@dataclass
class GestureEvent:
    """Gesture recognition event."""
    gesture_type: GestureType
    confidence: float
    coordinates: Tuple[int, int]
    duration: float
    hand_landmarks: List[Tuple[float, float]]
    timestamp: float


@dataclass
class EyeTrackingEvent:
    """Eye tracking event."""
    gaze_point: Tuple[int, int]
    fixation_duration: float
    blink_detected: bool
    attention_level: float
    timestamp: float


class MultiModalInterfaceManager:
    """
    Advanced multi-modal interface management system.
    
    Features:
    - Voice input integration with existing voice engine
    - Hand gesture recognition using computer vision
    - Eye tracking for attention and focus detection
    - Touch and mouse input coordination
    - Multi-modal command fusion and interpretation
    - Adaptive interface based on user preferences
    - Accessibility enhancements for different abilities
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        
        # Input processing
        self.active_modalities: Dict[InputModality, bool] = {}
        self.input_events: List[InputEvent] = []
        self.gesture_events: List[GestureEvent] = []
        self.eye_tracking_events: List[EyeTrackingEvent] = []
        
        # Computer vision components
        self.camera_active = False
        self.gesture_detector = None
        self.eye_tracker = None
        
        # Event callbacks
        self.on_gesture_detected: Optional[Callable[[GestureEvent], None]] = None
        self.on_eye_tracking_event: Optional[Callable[[EyeTrackingEvent], None]] = None
        self.on_multimodal_command: Optional[Callable[[List[InputEvent]], None]] = None
        
        # Configuration
        self.gesture_confidence_threshold = self.config.get("experience.gesture.confidence_threshold", 0.7)
        self.eye_tracking_enabled = self.config.get("experience.eye_tracking.enabled", False)
        self.gesture_recognition_enabled = self.config.get("experience.gesture.enabled", True)
        self.multimodal_fusion_window = self.config.get("experience.multimodal.fusion_window_ms", 2000)
        
        # Initialize modalities
        self._initialize_modalities()
        
        logger.info("Multi-Modal Interface Manager initialized")
    
    def _initialize_modalities(self):
        """Initialize available input modalities."""
        
        try:
            # Voice is always available (handled by existing voice engine)
            self.active_modalities[InputModality.VOICE] = True
            
            # Initialize gesture recognition
            if self.gesture_recognition_enabled:
                self._initialize_gesture_recognition()
            
            # Initialize eye tracking
            if self.eye_tracking_enabled:
                self._initialize_eye_tracking()
            
            # Standard input modalities
            self.active_modalities[InputModality.KEYBOARD] = True
            self.active_modalities[InputModality.MOUSE] = True
            self.active_modalities[InputModality.TOUCH] = True
            
        except Exception as e:
            logger.error(f"Error initializing modalities: {e}")
    
    def _initialize_gesture_recognition(self):
        """Initialize hand gesture recognition."""
        
        try:
            # Try to import MediaPipe for gesture recognition
            try:
                import mediapipe as mp
                
                self.mp_hands = mp.solutions.hands
                self.mp_drawing = mp.solutions.drawing_utils
                self.hands = self.mp_hands.Hands(
                    static_image_mode=False,
                    max_num_hands=2,
                    min_detection_confidence=0.7,
                    min_tracking_confidence=0.5
                )
                
                self.active_modalities[InputModality.GESTURE] = True
                logger.info("Gesture recognition initialized with MediaPipe")
                
            except ImportError:
                logger.warning("MediaPipe not available, gesture recognition disabled")
                self.active_modalities[InputModality.GESTURE] = False
                
        except Exception as e:
            logger.error(f"Error initializing gesture recognition: {e}")
            self.active_modalities[InputModality.GESTURE] = False
    
    def _initialize_eye_tracking(self):
        """Initialize eye tracking."""
        
        try:
            # Simplified eye tracking using OpenCV
            # In a production system, this would use specialized eye tracking hardware
            self.active_modalities[InputModality.EYE_TRACKING] = True
            logger.info("Eye tracking initialized")
            
        except Exception as e:
            logger.error(f"Error initializing eye tracking: {e}")
            self.active_modalities[InputModality.EYE_TRACKING] = False
    
    async def start_multimodal_processing(self):
        """Start multi-modal input processing."""
        
        try:
            if self.active_modalities.get(InputModality.GESTURE, False):
                asyncio.create_task(self._gesture_processing_loop())
            
            if self.active_modalities.get(InputModality.EYE_TRACKING, False):
                asyncio.create_task(self._eye_tracking_loop())
            
            # Start multi-modal fusion
            asyncio.create_task(self._multimodal_fusion_loop())
            
            logger.info("Multi-modal processing started")
            
        except Exception as e:
            logger.error(f"Error starting multi-modal processing: {e}")
    
    async def _gesture_processing_loop(self):
        """Main gesture processing loop."""
        
        try:
            cap = cv2.VideoCapture(0)
            if not cap.isOpened():
                logger.warning("Camera not available for gesture recognition")
                return
            
            self.camera_active = True
            logger.info("Gesture recognition camera started")
            
            while self.camera_active:
                ret, frame = cap.read()
                if not ret:
                    continue
                
                # Process frame for gestures
                gesture_event = await self._process_gesture_frame(frame)
                if gesture_event:
                    self.gesture_events.append(gesture_event)
                    
                    # Create input event
                    input_event = InputEvent(
                        event_id=f"gesture_{int(time.time() * 1000)}",
                        modality=InputModality.GESTURE,
                        event_type=gesture_event.gesture_type.value,
                        data=asdict(gesture_event),
                        confidence=gesture_event.confidence,
                        timestamp=time.time(),
                        coordinates=gesture_event.coordinates
                    )
                    
                    self.input_events.append(input_event)
                    
                    # Trigger callback
                    if self.on_gesture_detected:
                        self.on_gesture_detected(gesture_event)
                
                # Small delay to prevent overwhelming the system
                await asyncio.sleep(0.1)
            
            cap.release()
            
        except Exception as e:
            logger.error(f"Error in gesture processing loop: {e}")
    
    async def _process_gesture_frame(self, frame) -> Optional[GestureEvent]:
        """Process a single frame for gesture recognition."""
        
        try:
            if not hasattr(self, 'hands'):
                return None
            
            # Convert BGR to RGB
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.hands.process(rgb_frame)
            
            if results.multi_hand_landmarks:
                for hand_landmarks in results.multi_hand_landmarks:
                    # Extract landmark coordinates
                    landmarks = []
                    for landmark in hand_landmarks.landmark:
                        landmarks.append((landmark.x, landmark.y))
                    
                    # Analyze gesture
                    gesture_type, confidence = self._analyze_hand_gesture(landmarks)
                    
                    if gesture_type and confidence >= self.gesture_confidence_threshold:
                        # Calculate center point
                        center_x = int(np.mean([lm[0] for lm in landmarks]) * frame.shape[1])
                        center_y = int(np.mean([lm[1] for lm in landmarks]) * frame.shape[0])
                        
                        return GestureEvent(
                            gesture_type=gesture_type,
                            confidence=confidence,
                            coordinates=(center_x, center_y),
                            duration=0.1,  # Frame duration
                            hand_landmarks=landmarks,
                            timestamp=time.time()
                        )
            
            return None
            
        except Exception as e:
            logger.error(f"Error processing gesture frame: {e}")
            return None
    
    def _analyze_hand_gesture(self, landmarks: List[Tuple[float, float]]) -> Tuple[Optional[GestureType], float]:
        """Analyze hand landmarks to detect gestures."""
        
        try:
            if len(landmarks) < 21:  # MediaPipe hand has 21 landmarks
                return None, 0.0
            
            # Simple gesture recognition based on landmark positions
            # In a production system, this would use more sophisticated ML models
            
            # Thumb tip and thumb IP
            thumb_tip = landmarks[4]
            thumb_ip = landmarks[3]
            
            # Index finger tip and PIP
            index_tip = landmarks[8]
            index_pip = landmarks[6]
            
            # Middle finger tip and PIP
            middle_tip = landmarks[12]
            middle_pip = landmarks[10]
            
            # Pointing gesture (index finger extended, others folded)
            if (index_tip[1] < index_pip[1] and  # Index finger extended
                middle_tip[1] > middle_pip[1] and  # Middle finger folded
                thumb_tip[0] < thumb_ip[0]):  # Thumb folded
                return GestureType.POINT, 0.8
            
            # Thumbs up (thumb extended upward)
            if (thumb_tip[1] < thumb_ip[1] and  # Thumb extended up
                index_tip[1] > index_pip[1]):  # Other fingers folded
                return GestureType.THUMBS_UP, 0.9
            
            # Thumbs down (thumb extended downward)
            if (thumb_tip[1] > thumb_ip[1] and  # Thumb extended down
                index_tip[1] > index_pip[1]):  # Other fingers folded
                return GestureType.THUMBS_DOWN, 0.9
            
            # Open hand (all fingers extended)
            fingers_extended = (
                index_tip[1] < index_pip[1] and
                middle_tip[1] < middle_pip[1] and
                landmarks[16][1] < landmarks[14][1] and  # Ring finger
                landmarks[20][1] < landmarks[18][1]     # Pinky
            )
            
            if fingers_extended:
                return GestureType.WAVE, 0.7
            
            return None, 0.0
            
        except Exception as e:
            logger.error(f"Error analyzing hand gesture: {e}")
            return None, 0.0
    
    async def _eye_tracking_loop(self):
        """Main eye tracking loop."""
        
        try:
            # Simplified eye tracking simulation
            # In a production system, this would use specialized eye tracking hardware
            
            while self.active_modalities.get(InputModality.EYE_TRACKING, False):
                # Simulate eye tracking data
                gaze_point = (np.random.randint(0, 1920), np.random.randint(0, 1080))
                fixation_duration = np.random.uniform(0.1, 2.0)
                blink_detected = np.random.random() < 0.1  # 10% chance of blink
                attention_level = np.random.uniform(0.5, 1.0)
                
                eye_event = EyeTrackingEvent(
                    gaze_point=gaze_point,
                    fixation_duration=fixation_duration,
                    blink_detected=blink_detected,
                    attention_level=attention_level,
                    timestamp=time.time()
                )
                
                self.eye_tracking_events.append(eye_event)
                
                # Create input event
                input_event = InputEvent(
                    event_id=f"eye_{int(time.time() * 1000)}",
                    modality=InputModality.EYE_TRACKING,
                    event_type="gaze",
                    data=asdict(eye_event),
                    confidence=attention_level,
                    timestamp=time.time(),
                    coordinates=gaze_point
                )
                
                self.input_events.append(input_event)
                
                # Trigger callback
                if self.on_eye_tracking_event:
                    self.on_eye_tracking_event(eye_event)
                
                await asyncio.sleep(0.5)  # Eye tracking at 2 Hz
                
        except Exception as e:
            logger.error(f"Error in eye tracking loop: {e}")
    
    async def _multimodal_fusion_loop(self):
        """Multi-modal input fusion loop."""
        
        try:
            while True:
                await asyncio.sleep(0.5)  # Check every 500ms
                
                # Get recent events within fusion window
                current_time = time.time()
                fusion_window_start = current_time - (self.multimodal_fusion_window / 1000.0)
                
                recent_events = [
                    event for event in self.input_events
                    if event.timestamp >= fusion_window_start
                ]
                
                if len(recent_events) >= 2:  # Multiple modalities active
                    await self._process_multimodal_command(recent_events)
                
                # Clean up old events
                self.input_events = [
                    event for event in self.input_events
                    if event.timestamp >= fusion_window_start
                ]
                
        except Exception as e:
            logger.error(f"Error in multimodal fusion loop: {e}")
    
    async def _process_multimodal_command(self, events: List[InputEvent]):
        """Process multi-modal command from multiple input events."""
        
        try:
            # Group events by modality
            modality_events = {}
            for event in events:
                if event.modality not in modality_events:
                    modality_events[event.modality] = []
                modality_events[event.modality].append(event)
            
            # Analyze multi-modal patterns
            command_detected = False
            
            # Voice + Gesture combination
            if (InputModality.VOICE in modality_events and 
                InputModality.GESTURE in modality_events):
                
                voice_events = modality_events[InputModality.VOICE]
                gesture_events = modality_events[InputModality.GESTURE]
                
                # Check for pointing gesture with voice command
                for gesture_event in gesture_events:
                    if gesture_event.event_type == GestureType.POINT.value:
                        logger.info(f"Multi-modal command: Voice + Pointing gesture at {gesture_event.coordinates}")
                        command_detected = True
            
            # Voice + Eye tracking combination
            if (InputModality.VOICE in modality_events and 
                InputModality.EYE_TRACKING in modality_events):
                
                eye_events = modality_events[InputModality.EYE_TRACKING]
                for eye_event in eye_events:
                    if eye_event.data.get('attention_level', 0) > 0.8:
                        logger.info(f"Multi-modal command: Voice + High attention at {eye_event.coordinates}")
                        command_detected = True
            
            # Gesture + Eye tracking combination
            if (InputModality.GESTURE in modality_events and 
                InputModality.EYE_TRACKING in modality_events):
                
                logger.info("Multi-modal command: Gesture + Eye tracking coordination")
                command_detected = True
            
            if command_detected and self.on_multimodal_command:
                self.on_multimodal_command(events)
                
        except Exception as e:
            logger.error(f"Error processing multimodal command: {e}")
    
    async def register_voice_event(self, voice_data: Dict[str, Any]):
        """Register a voice event from the voice engine."""
        
        try:
            input_event = InputEvent(
                event_id=f"voice_{int(time.time() * 1000)}",
                modality=InputModality.VOICE,
                event_type="speech",
                data=voice_data,
                confidence=voice_data.get('confidence', 1.0),
                timestamp=time.time()
            )
            
            self.input_events.append(input_event)
            
        except Exception as e:
            logger.error(f"Error registering voice event: {e}")
    
    async def register_keyboard_event(self, key_data: Dict[str, Any]):
        """Register a keyboard event."""
        
        try:
            input_event = InputEvent(
                event_id=f"keyboard_{int(time.time() * 1000)}",
                modality=InputModality.KEYBOARD,
                event_type=key_data.get('type', 'keypress'),
                data=key_data,
                confidence=1.0,
                timestamp=time.time()
            )
            
            self.input_events.append(input_event)
            
        except Exception as e:
            logger.error(f"Error registering keyboard event: {e}")
    
    async def register_mouse_event(self, mouse_data: Dict[str, Any]):
        """Register a mouse event."""
        
        try:
            input_event = InputEvent(
                event_id=f"mouse_{int(time.time() * 1000)}",
                modality=InputModality.MOUSE,
                event_type=mouse_data.get('type', 'click'),
                data=mouse_data,
                confidence=1.0,
                timestamp=time.time(),
                coordinates=mouse_data.get('coordinates')
            )
            
            self.input_events.append(input_event)
            
        except Exception as e:
            logger.error(f"Error registering mouse event: {e}")
    
    def stop_camera(self):
        """Stop camera for gesture recognition."""
        self.camera_active = False
    
    def get_interface_statistics(self) -> Dict[str, Any]:
        """Get comprehensive interface statistics."""
        
        try:
            # Count events by modality
            modality_counts = {}
            for event in self.input_events[-100:]:  # Recent events
                modality = event.modality.value
                modality_counts[modality] = modality_counts.get(modality, 0) + 1
            
            # Gesture statistics
            gesture_counts = {}
            for gesture_event in self.gesture_events[-50:]:  # Recent gestures
                gesture_type = gesture_event.gesture_type.value
                gesture_counts[gesture_type] = gesture_counts.get(gesture_type, 0) + 1
            
            # Calculate average confidence
            recent_events = self.input_events[-50:] if self.input_events else []
            avg_confidence = np.mean([e.confidence for e in recent_events]) if recent_events else 0
            
            return {
                "active_modalities": {k.value: v for k, v in self.active_modalities.items()},
                "total_input_events": len(self.input_events),
                "modality_distribution": modality_counts,
                "gesture_distribution": gesture_counts,
                "average_confidence": avg_confidence,
                "camera_active": self.camera_active,
                "multimodal_fusion_window_ms": self.multimodal_fusion_window
            }
            
        except Exception as e:
            logger.error(f"Error getting interface statistics: {e}")
            return {"error": str(e)}
    
    def get_recent_events(self, count: int = 10) -> List[InputEvent]:
        """Get recent input events."""
        return self.input_events[-count:] if self.input_events else []
    
    def get_recent_gestures(self, count: int = 5) -> List[GestureEvent]:
        """Get recent gesture events."""
        return self.gesture_events[-count:] if self.gesture_events else []
    
    def configure_modality(self, modality: InputModality, enabled: bool):
        """Enable or disable a specific input modality."""
        
        try:
            if modality == InputModality.GESTURE and enabled and not self.active_modalities.get(modality, False):
                self._initialize_gesture_recognition()
            elif modality == InputModality.EYE_TRACKING and enabled and not self.active_modalities.get(modality, False):
                self._initialize_eye_tracking()
            
            self.active_modalities[modality] = enabled
            logger.info(f"Modality {modality.value} {'enabled' if enabled else 'disabled'}")
            
        except Exception as e:
            logger.error(f"Error configuring modality: {e}")
    
    def set_gesture_confidence_threshold(self, threshold: float):
        """Set gesture recognition confidence threshold."""
        self.gesture_confidence_threshold = max(0.0, min(1.0, threshold))
        logger.info(f"Gesture confidence threshold set to {self.gesture_confidence_threshold}")
    
    def cleanup(self):
        """Clean up resources."""
        try:
            self.camera_active = False
            self.active_modalities[InputModality.EYE_TRACKING] = False
            logger.info("Multi-modal interface cleaned up")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


# Alias for compatibility
MultiModalInterface = MultiModalInterfaceManager
