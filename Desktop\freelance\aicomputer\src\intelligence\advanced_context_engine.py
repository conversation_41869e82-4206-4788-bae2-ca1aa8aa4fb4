"""
Advanced Context Engine - Phase 2 Component

Enhanced context management with deep understanding, semantic analysis,
and intelligent context switching capabilities.
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
import numpy as np
from collections import defaultdict

from loguru import logger

from ..utils.config_manager import ConfigManager
from ..core.ai_processor import ProcessedCommand, CommandIntent


@dataclass
class SemanticContext:
    """Semantic context information."""
    topic: str
    entities: List[str]
    relationships: Dict[str, Any]
    confidence: float
    timestamp: float


@dataclass
class WorkflowContext:
    """Workflow context for multi-step operations."""
    workflow_id: str
    steps: List[Dict[str, Any]]
    current_step: int
    variables: Dict[str, Any]
    start_time: float
    status: str  # 'active', 'paused', 'completed', 'failed'


@dataclass
class UserIntent:
    """Deep user intent analysis."""
    primary_intent: str
    secondary_intents: List[str]
    emotional_state: str
    urgency_level: int  # 1-5
    context_dependencies: List[str]
    confidence: float


class AdvancedContextEngine:
    """
    Advanced context engine with semantic understanding and intelligent context management.
    
    Features:
    - Semantic context analysis
    - Multi-step workflow tracking
    - Deep intent understanding
    - Context-aware suggestions
    - Intelligent context switching
    - Long-term memory integration
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        
        # Context storage
        self.semantic_contexts: List[SemanticContext] = []
        self.active_workflows: Dict[str, WorkflowContext] = {}
        self.user_intents: List[UserIntent] = []
        
        # Context analysis
        self.topic_history: List[str] = []
        self.entity_relationships: Dict[str, Dict[str, float]] = defaultdict(dict)
        self.context_switches: List[Dict[str, Any]] = []
        
        # Learning data
        self.user_patterns: Dict[str, Any] = {}
        self.command_sequences: List[List[str]] = []
        self.context_preferences: Dict[str, float] = {}
        
        # Configuration
        self.max_context_history = self.config.get("intelligence.context.max_history", 1000)
        self.semantic_threshold = self.config.get("intelligence.context.semantic_threshold", 0.7)
        self.workflow_timeout = self.config.get("intelligence.workflow.timeout", 3600)
        
        logger.info("Advanced Context Engine initialized")
    
    async def analyze_command_context(self, command: ProcessedCommand, 
                                    conversation_history: List[str]) -> Dict[str, Any]:
        """
        Perform deep context analysis of a command.
        
        Args:
            command: Processed command to analyze
            conversation_history: Recent conversation history
            
        Returns:
            Comprehensive context analysis
        """
        try:
            # Extract semantic context
            semantic_context = await self._extract_semantic_context(command, conversation_history)
            
            # Analyze user intent
            user_intent = await self._analyze_user_intent(command, conversation_history)
            
            # Check for workflow continuation
            workflow_context = self._check_workflow_continuation(command)
            
            # Generate context-aware suggestions
            suggestions = await self._generate_context_suggestions(command, semantic_context)
            
            # Detect context switches
            context_switch = self._detect_context_switch(semantic_context)
            
            analysis = {
                "semantic_context": asdict(semantic_context),
                "user_intent": asdict(user_intent),
                "workflow_context": asdict(workflow_context) if workflow_context else None,
                "suggestions": suggestions,
                "context_switch": context_switch,
                "confidence": min(semantic_context.confidence, user_intent.confidence),
                "timestamp": time.time()
            }
            
            # Store for learning
            await self._update_learning_data(command, analysis)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error in context analysis: {e}")
            return {"error": str(e), "confidence": 0.0}
    
    async def _extract_semantic_context(self, command: ProcessedCommand, 
                                      history: List[str]) -> SemanticContext:
        """Extract semantic context from command and history."""
        
        try:
            # Simple topic extraction (can be enhanced with NLP models)
            topics = self._extract_topics(command.raw_text, history)
            primary_topic = topics[0] if topics else "general"
            
            # Entity extraction
            entities = self._extract_entities(command)
            
            # Relationship analysis
            relationships = self._analyze_relationships(entities, history)
            
            # Calculate confidence based on context clarity
            confidence = self._calculate_semantic_confidence(command, topics, entities)
            
            semantic_context = SemanticContext(
                topic=primary_topic,
                entities=entities,
                relationships=relationships,
                confidence=confidence,
                timestamp=time.time()
            )
            
            # Store in context history
            self.semantic_contexts.append(semantic_context)
            self._cleanup_old_contexts()
            
            return semantic_context
            
        except Exception as e:
            logger.error(f"Error extracting semantic context: {e}")
            return SemanticContext("unknown", [], {}, 0.0, time.time())
    
    async def _analyze_user_intent(self, command: ProcessedCommand, 
                                 history: List[str]) -> UserIntent:
        """Analyze deep user intent beyond basic command classification."""
        
        try:
            # Primary intent from command
            primary_intent = command.intent.value
            
            # Secondary intents (implied or related)
            secondary_intents = self._infer_secondary_intents(command, history)
            
            # Emotional state analysis (basic implementation)
            emotional_state = self._analyze_emotional_state(command.raw_text)
            
            # Urgency level based on language patterns
            urgency_level = self._calculate_urgency_level(command.raw_text)
            
            # Context dependencies
            dependencies = self._identify_context_dependencies(command, history)
            
            # Intent confidence
            confidence = command.confidence * 0.8  # Slightly lower for deep analysis
            
            user_intent = UserIntent(
                primary_intent=primary_intent,
                secondary_intents=secondary_intents,
                emotional_state=emotional_state,
                urgency_level=urgency_level,
                context_dependencies=dependencies,
                confidence=confidence
            )
            
            self.user_intents.append(user_intent)
            
            return user_intent
            
        except Exception as e:
            logger.error(f"Error analyzing user intent: {e}")
            return UserIntent("unknown", [], "neutral", 1, [], 0.0)
    
    def _extract_topics(self, text: str, history: List[str]) -> List[str]:
        """Extract topics from text and conversation history."""
        
        # Topic keywords mapping
        topic_keywords = {
            "file_management": ["file", "folder", "document", "save", "open", "delete", "copy"],
            "application": ["app", "program", "software", "launch", "start", "open"],
            "system": ["system", "computer", "performance", "cpu", "memory", "disk"],
            "productivity": ["work", "task", "project", "schedule", "meeting", "email"],
            "entertainment": ["music", "video", "game", "movie", "photo", "image"],
            "communication": ["message", "call", "chat", "email", "contact"],
            "development": ["code", "programming", "debug", "compile", "git", "repository"],
            "research": ["search", "find", "lookup", "information", "data", "analysis"]
        }
        
        text_lower = text.lower()
        topics = []
        
        for topic, keywords in topic_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                topics.append(topic)
        
        # Check recent history for topic continuity
        if history:
            recent_text = " ".join(history[-3:]).lower()
            for topic, keywords in topic_keywords.items():
                if any(keyword in recent_text for keyword in keywords) and topic not in topics:
                    topics.append(topic)
        
        return topics[:3]  # Return top 3 topics
    
    def _extract_entities(self, command: ProcessedCommand) -> List[str]:
        """Extract entities from command."""
        
        entities = []
        
        # Extract from command entities
        for key, value in command.entities.items():
            if isinstance(value, str) and value:
                entities.append(value)
            elif isinstance(value, list):
                entities.extend([str(v) for v in value if v])
        
        # Extract from raw text (simple pattern matching)
        text = command.raw_text.lower()
        
        # File extensions
        import re
        file_patterns = re.findall(r'\w+\.\w+', text)
        entities.extend(file_patterns)
        
        # Application names
        app_names = ["notepad", "calculator", "chrome", "firefox", "word", "excel", "powerpoint"]
        for app in app_names:
            if app in text:
                entities.append(app)
        
        return list(set(entities))  # Remove duplicates
    
    def _analyze_relationships(self, entities: List[str], history: List[str]) -> Dict[str, Any]:
        """Analyze relationships between entities."""
        
        relationships = {}
        
        # Co-occurrence relationships
        for i, entity1 in enumerate(entities):
            for entity2 in entities[i+1:]:
                key = f"{entity1}-{entity2}"
                relationships[key] = {"type": "co_occurrence", "strength": 1.0}
        
        # Historical relationships
        history_text = " ".join(history).lower()
        for entity in entities:
            if entity.lower() in history_text:
                relationships[f"history-{entity}"] = {"type": "historical", "strength": 0.8}
        
        return relationships
    
    def _calculate_semantic_confidence(self, command: ProcessedCommand, 
                                     topics: List[str], entities: List[str]) -> float:
        """Calculate confidence in semantic understanding."""
        
        confidence = command.confidence
        
        # Boost confidence if we have clear topics
        if topics:
            confidence += 0.1 * len(topics)
        
        # Boost confidence if we have entities
        if entities:
            confidence += 0.05 * len(entities)
        
        # Reduce confidence for ambiguous commands
        ambiguous_words = ["it", "this", "that", "thing", "stuff"]
        if any(word in command.raw_text.lower() for word in ambiguous_words):
            confidence -= 0.2
        
        return min(confidence, 1.0)
    
    def _infer_secondary_intents(self, command: ProcessedCommand, history: List[str]) -> List[str]:
        """Infer secondary intents from context."""
        
        secondary_intents = []
        
        # If file operation, might also want to view or edit
        if command.intent == CommandIntent.FILE_OPERATION:
            if command.action in ["create", "copy"]:
                secondary_intents.append("open_file")
            elif command.action == "list":
                secondary_intents.append("select_file")
        
        # If app control, might want to perform tasks
        elif command.intent == CommandIntent.APP_CONTROL:
            if command.action == "open":
                secondary_intents.append("use_application")
        
        # If system info, might want to optimize
        elif command.intent == CommandIntent.SYSTEM_INFO:
            secondary_intents.append("system_optimization")
        
        return secondary_intents
    
    def _analyze_emotional_state(self, text: str) -> str:
        """Analyze emotional state from text (basic implementation)."""
        
        text_lower = text.lower()
        
        # Positive indicators
        positive_words = ["please", "thanks", "great", "awesome", "perfect"]
        if any(word in text_lower for word in positive_words):
            return "positive"
        
        # Negative indicators
        negative_words = ["urgent", "quickly", "asap", "problem", "issue", "broken", "error"]
        if any(word in text_lower for word in negative_words):
            return "frustrated"
        
        # Question indicators
        if "?" in text or text_lower.startswith(("what", "how", "where", "when", "why")):
            return "curious"
        
        return "neutral"
    
    def _calculate_urgency_level(self, text: str) -> int:
        """Calculate urgency level from 1-5."""
        
        text_lower = text.lower()
        urgency = 1
        
        # High urgency indicators
        high_urgency = ["urgent", "asap", "immediately", "quickly", "emergency", "critical"]
        if any(word in text_lower for word in high_urgency):
            urgency = 5
        
        # Medium urgency indicators
        medium_urgency = ["soon", "fast", "hurry", "important"]
        if any(word in text_lower for word in medium_urgency):
            urgency = 3
        
        # Time-based urgency
        time_indicators = ["now", "today", "tonight"]
        if any(word in text_lower for word in time_indicators):
            urgency = max(urgency, 4)
        
        return urgency
    
    def _identify_context_dependencies(self, command: ProcessedCommand, 
                                     history: List[str]) -> List[str]:
        """Identify what context this command depends on."""
        
        dependencies = []
        
        # Pronoun dependencies
        pronouns = ["it", "this", "that", "them", "these", "those"]
        if any(pronoun in command.raw_text.lower() for pronoun in pronouns):
            dependencies.append("previous_reference")
        
        # Location dependencies
        if "here" in command.raw_text.lower() or "current" in command.entities.get("location", ""):
            dependencies.append("current_location")
        
        # Application dependencies
        if command.intent == CommandIntent.FILE_OPERATION and not command.entities.get("target"):
            dependencies.append("active_application")
        
        return dependencies
    
    def _check_workflow_continuation(self, command: ProcessedCommand) -> Optional[WorkflowContext]:
        """Check if command continues an existing workflow."""
        
        # Simple workflow detection based on command patterns
        for workflow_id, workflow in self.active_workflows.items():
            if workflow.status == "active":
                # Check if command fits the workflow pattern
                if self._command_fits_workflow(command, workflow):
                    return workflow
        
        return None
    
    def _command_fits_workflow(self, command: ProcessedCommand, workflow: WorkflowContext) -> bool:
        """Check if command fits into an existing workflow."""
        
        if workflow.current_step < len(workflow.steps):
            expected_step = workflow.steps[workflow.current_step]
            return command.intent.value == expected_step.get("intent")
        
        return False
    
    async def _generate_context_suggestions(self, command: ProcessedCommand, 
                                          semantic_context: SemanticContext) -> List[str]:
        """Generate context-aware suggestions."""
        
        suggestions = []
        
        # Topic-based suggestions
        if semantic_context.topic == "file_management":
            suggestions.extend([
                "Would you like to open the file after creating it?",
                "Should I organize files by type?",
                "Do you want to backup important files?"
            ])
        
        elif semantic_context.topic == "application":
            suggestions.extend([
                "Would you like me to remember this application preference?",
                "Should I create a shortcut for quick access?",
                "Do you want to set up automatic startup?"
            ])
        
        # Entity-based suggestions
        for entity in semantic_context.entities:
            if entity.endswith(('.txt', '.doc', '.pdf')):
                suggestions.append(f"Would you like to edit {entity}?")
        
        return suggestions[:3]  # Return top 3 suggestions
    
    def _detect_context_switch(self, semantic_context: SemanticContext) -> Dict[str, Any]:
        """Detect if there's been a context switch."""
        
        if not self.topic_history:
            self.topic_history.append(semantic_context.topic)
            return {"switched": False}
        
        last_topic = self.topic_history[-1]
        current_topic = semantic_context.topic
        
        if last_topic != current_topic:
            switch_info = {
                "switched": True,
                "from_topic": last_topic,
                "to_topic": current_topic,
                "timestamp": time.time()
            }
            
            self.context_switches.append(switch_info)
            self.topic_history.append(current_topic)
            
            return switch_info
        
        return {"switched": False}
    
    async def _update_learning_data(self, command: ProcessedCommand, analysis: Dict[str, Any]):
        """Update learning data for future improvements."""
        
        try:
            # Update command sequences
            command_text = command.raw_text
            if self.command_sequences and len(self.command_sequences[-1]) < 10:
                self.command_sequences[-1].append(command_text)
            else:
                self.command_sequences.append([command_text])
            
            # Update user patterns
            intent = command.intent.value
            if intent not in self.user_patterns:
                self.user_patterns[intent] = {"count": 0, "success_rate": 0.0, "avg_confidence": 0.0}
            
            self.user_patterns[intent]["count"] += 1
            self.user_patterns[intent]["avg_confidence"] = (
                (self.user_patterns[intent]["avg_confidence"] * (self.user_patterns[intent]["count"] - 1) + 
                 command.confidence) / self.user_patterns[intent]["count"]
            )
            
            # Update context preferences
            topic = analysis.get("semantic_context", {}).get("topic", "unknown")
            if topic in self.context_preferences:
                self.context_preferences[topic] += 1
            else:
                self.context_preferences[topic] = 1
            
        except Exception as e:
            logger.error(f"Error updating learning data: {e}")
    
    def _cleanup_old_contexts(self):
        """Clean up old context data to manage memory."""
        
        # Keep only recent semantic contexts
        if len(self.semantic_contexts) > self.max_context_history:
            self.semantic_contexts = self.semantic_contexts[-self.max_context_history//2:]
        
        # Clean up old workflows
        current_time = time.time()
        expired_workflows = [
            wf_id for wf_id, wf in self.active_workflows.items()
            if current_time - wf.start_time > self.workflow_timeout
        ]
        
        for wf_id in expired_workflows:
            del self.active_workflows[wf_id]
        
        # Keep only recent topic history
        if len(self.topic_history) > 50:
            self.topic_history = self.topic_history[-25:]
    
    def get_context_summary(self) -> Dict[str, Any]:
        """Get summary of current context state."""
        
        return {
            "active_topics": list(set(self.topic_history[-5:])),
            "active_workflows": len(self.active_workflows),
            "recent_entities": [ctx.entities for ctx in self.semantic_contexts[-3:]],
            "context_switches_today": len([
                cs for cs in self.context_switches 
                if time.time() - cs["timestamp"] < 86400
            ]),
            "user_patterns": dict(list(self.user_patterns.items())[:5]),
            "context_preferences": dict(sorted(
                self.context_preferences.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:5])
        }
