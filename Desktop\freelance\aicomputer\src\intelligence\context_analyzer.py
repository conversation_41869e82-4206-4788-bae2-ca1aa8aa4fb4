"""
Context Analyzer - Phase 2 Component

Advanced context analysis for understanding user intent and environmental factors.
"""

import asyncio
import time
import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
from datetime import datetime, timedelta
import re

from loguru import logger

from ..utils.config_manager import ConfigManager
from ..core.ai_processor import ProcessedCommand, CommandIntent


@dataclass
class ContextElement:
    """Context element data structure."""
    element_id: str
    element_type: str
    value: Any
    confidence: float
    source: str
    timestamp: float
    metadata: Dict[str, Any]


@dataclass
class ContextPattern:
    """Context pattern data structure."""
    pattern_id: str
    pattern_type: str
    elements: List[str]
    frequency: int
    confidence: float
    last_seen: float
    conditions: Dict[str, Any]


class ContextAnalyzer:
    """
    Advanced context analysis system.
    
    Features:
    - Multi-dimensional context extraction
    - Temporal context analysis
    - Environmental context detection
    - User state inference
    - Context pattern recognition
    - Contextual relevance scoring
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        
        # Context storage
        self.current_context: Dict[str, ContextElement] = {}
        self.context_history: List[Dict[str, Any]] = []
        self.context_patterns: Dict[str, ContextPattern] = {}
        
        # Analysis configuration
        self.context_window = self.config.get("intelligence.context.window_minutes", 30)
        self.pattern_threshold = self.config.get("intelligence.context.pattern_threshold", 3)
        self.relevance_threshold = self.config.get("intelligence.context.relevance_threshold", 0.6)
        
        # Context extractors
        self.extractors = {
            'temporal': self._extract_temporal_context,
            'system': self._extract_system_context,
            'user': self._extract_user_context,
            'environmental': self._extract_environmental_context,
            'semantic': self._extract_semantic_context
        }
        
        logger.info("Context Analyzer initialized")
    
    async def analyze_context(self, command: ProcessedCommand, 
                            system_state: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze and extract context from command and system state."""
        try:
            context_elements = {}
            
            # Extract different types of context
            for extractor_name, extractor_func in self.extractors.items():
                elements = await extractor_func(command, system_state)
                context_elements.update(elements)
            
            # Update current context
            self.current_context.update(context_elements)
            
            # Analyze context patterns
            await self._analyze_context_patterns()
            
            # Calculate context relevance
            relevance_scores = await self._calculate_relevance_scores(command)
            
            # Build comprehensive context
            comprehensive_context = {
                'elements': {k: asdict(v) for k, v in context_elements.items()},
                'patterns': [asdict(p) for p in self.context_patterns.values()],
                'relevance_scores': relevance_scores,
                'timestamp': time.time(),
                'command_context': {
                    'intent': command.intent.value,
                    'entities': command.entities,
                    'confidence': command.confidence
                }
            }
            
            # Store in history
            self.context_history.append(comprehensive_context)
            self._cleanup_old_context()
            
            return comprehensive_context
            
        except Exception as e:
            logger.error(f"Error analyzing context: {e}")
            return {}
    
    async def _extract_temporal_context(self, command: ProcessedCommand, 
                                      system_state: Dict[str, Any]) -> Dict[str, ContextElement]:
        """Extract temporal context elements."""
        elements = {}
        current_time = time.time()
        dt = datetime.now()
        
        try:
            # Time of day
            elements['time_of_day'] = ContextElement(
                element_id='time_of_day',
                element_type='temporal',
                value=dt.hour,
                confidence=1.0,
                source='system_clock',
                timestamp=current_time,
                metadata={'hour': dt.hour, 'period': self._get_time_period(dt.hour)}
            )
            
            # Day of week
            elements['day_of_week'] = ContextElement(
                element_id='day_of_week',
                element_type='temporal',
                value=dt.weekday(),
                confidence=1.0,
                source='system_clock',
                timestamp=current_time,
                metadata={'weekday': dt.weekday(), 'is_weekend': dt.weekday() >= 5}
            )
            
            # Session duration
            session_start = system_state.get('session_start_time', current_time)
            session_duration = current_time - session_start
            elements['session_duration'] = ContextElement(
                element_id='session_duration',
                element_type='temporal',
                value=session_duration,
                confidence=0.9,
                source='session_tracker',
                timestamp=current_time,
                metadata={'duration_minutes': session_duration / 60}
            )
            
            # Time since last command
            last_command_time = system_state.get('last_command_time', current_time)
            time_since_last = current_time - last_command_time
            elements['time_since_last_command'] = ContextElement(
                element_id='time_since_last_command',
                element_type='temporal',
                value=time_since_last,
                confidence=0.8,
                source='command_tracker',
                timestamp=current_time,
                metadata={'seconds': time_since_last}
            )
            
        except Exception as e:
            logger.error(f"Error extracting temporal context: {e}")
        
        return elements
    
    async def _extract_system_context(self, command: ProcessedCommand, 
                                    system_state: Dict[str, Any]) -> Dict[str, ContextElement]:
        """Extract system context elements."""
        elements = {}
        current_time = time.time()
        
        try:
            # System performance
            cpu_usage = system_state.get('cpu_usage', 0)
            elements['cpu_usage'] = ContextElement(
                element_id='cpu_usage',
                element_type='system',
                value=cpu_usage,
                confidence=0.9,
                source='system_monitor',
                timestamp=current_time,
                metadata={'level': self._get_usage_level(cpu_usage)}
            )
            
            # Memory usage
            memory_usage = system_state.get('memory_usage', 0)
            elements['memory_usage'] = ContextElement(
                element_id='memory_usage',
                element_type='system',
                value=memory_usage,
                confidence=0.9,
                source='system_monitor',
                timestamp=current_time,
                metadata={'level': self._get_usage_level(memory_usage)}
            )
            
            # Active applications
            active_apps = system_state.get('active_applications', [])
            elements['active_applications'] = ContextElement(
                element_id='active_applications',
                element_type='system',
                value=len(active_apps),
                confidence=0.8,
                source='application_monitor',
                timestamp=current_time,
                metadata={'apps': active_apps, 'count': len(active_apps)}
            )
            
            # Network connectivity
            network_status = system_state.get('network_connected', True)
            elements['network_status'] = ContextElement(
                element_id='network_status',
                element_type='system',
                value=network_status,
                confidence=0.95,
                source='network_monitor',
                timestamp=current_time,
                metadata={'connected': network_status}
            )
            
        except Exception as e:
            logger.error(f"Error extracting system context: {e}")
        
        return elements
    
    async def _extract_user_context(self, command: ProcessedCommand, 
                                  system_state: Dict[str, Any]) -> Dict[str, ContextElement]:
        """Extract user context elements."""
        elements = {}
        current_time = time.time()
        
        try:
            # User activity level
            command_frequency = system_state.get('command_frequency', 0)
            activity_level = self._calculate_activity_level(command_frequency)
            elements['user_activity'] = ContextElement(
                element_id='user_activity',
                element_type='user',
                value=activity_level,
                confidence=0.7,
                source='activity_tracker',
                timestamp=current_time,
                metadata={'frequency': command_frequency, 'level': activity_level}
            )
            
            # User expertise level (inferred from command complexity)
            expertise_level = self._infer_expertise_level(command, system_state)
            elements['user_expertise'] = ContextElement(
                element_id='user_expertise',
                element_type='user',
                value=expertise_level,
                confidence=0.6,
                source='expertise_analyzer',
                timestamp=current_time,
                metadata={'level': expertise_level}
            )
            
            # User mood/state (inferred from command patterns)
            user_state = self._infer_user_state(command, system_state)
            elements['user_state'] = ContextElement(
                element_id='user_state',
                element_type='user',
                value=user_state,
                confidence=0.5,
                source='state_analyzer',
                timestamp=current_time,
                metadata={'state': user_state}
            )
            
            # Current task context
            current_task = self._infer_current_task(command, system_state)
            elements['current_task'] = ContextElement(
                element_id='current_task',
                element_type='user',
                value=current_task,
                confidence=0.6,
                source='task_analyzer',
                timestamp=current_time,
                metadata={'task': current_task}
            )
            
        except Exception as e:
            logger.error(f"Error extracting user context: {e}")
        
        return elements
    
    async def _extract_environmental_context(self, command: ProcessedCommand, 
                                           system_state: Dict[str, Any]) -> Dict[str, ContextElement]:
        """Extract environmental context elements."""
        elements = {}
        current_time = time.time()
        
        try:
            # Current working directory
            current_dir = system_state.get('current_directory', '')
            elements['working_directory'] = ContextElement(
                element_id='working_directory',
                element_type='environmental',
                value=current_dir,
                confidence=0.9,
                source='file_system',
                timestamp=current_time,
                metadata={'path': current_dir, 'type': self._classify_directory(current_dir)}
            )
            
            # Recent file operations
            recent_files = system_state.get('recent_files', [])
            elements['recent_files'] = ContextElement(
                element_id='recent_files',
                element_type='environmental',
                value=len(recent_files),
                confidence=0.8,
                source='file_tracker',
                timestamp=current_time,
                metadata={'files': recent_files, 'count': len(recent_files)}
            )
            
            # System load
            system_load = system_state.get('system_load', 0.5)
            elements['system_load'] = ContextElement(
                element_id='system_load',
                element_type='environmental',
                value=system_load,
                confidence=0.8,
                source='load_monitor',
                timestamp=current_time,
                metadata={'load': system_load, 'level': self._get_load_level(system_load)}
            )
            
        except Exception as e:
            logger.error(f"Error extracting environmental context: {e}")
        
        return elements
    
    async def _extract_semantic_context(self, command: ProcessedCommand, 
                                      system_state: Dict[str, Any]) -> Dict[str, ContextElement]:
        """Extract semantic context elements."""
        elements = {}
        current_time = time.time()
        
        try:
            # Command complexity
            complexity = self._calculate_command_complexity(command)
            elements['command_complexity'] = ContextElement(
                element_id='command_complexity',
                element_type='semantic',
                value=complexity,
                confidence=0.7,
                source='complexity_analyzer',
                timestamp=current_time,
                metadata={'complexity': complexity, 'level': self._get_complexity_level(complexity)}
            )
            
            # Intent confidence
            elements['intent_confidence'] = ContextElement(
                element_id='intent_confidence',
                element_type='semantic',
                value=command.confidence,
                confidence=0.9,
                source='intent_classifier',
                timestamp=current_time,
                metadata={'confidence': command.confidence}
            )
            
            # Entity richness
            entity_count = len(command.entities)
            elements['entity_richness'] = ContextElement(
                element_id='entity_richness',
                element_type='semantic',
                value=entity_count,
                confidence=0.8,
                source='entity_extractor',
                timestamp=current_time,
                metadata={'count': entity_count, 'entities': command.entities}
            )
            
            # Command urgency (inferred from language)
            urgency = self._infer_command_urgency(command)
            elements['command_urgency'] = ContextElement(
                element_id='command_urgency',
                element_type='semantic',
                value=urgency,
                confidence=0.6,
                source='urgency_analyzer',
                timestamp=current_time,
                metadata={'urgency': urgency}
            )
            
        except Exception as e:
            logger.error(f"Error extracting semantic context: {e}")
        
        return elements
    
    def _get_time_period(self, hour: int) -> str:
        """Get time period from hour."""
        if 6 <= hour < 12:
            return 'morning'
        elif 12 <= hour < 18:
            return 'afternoon'
        elif 18 <= hour < 22:
            return 'evening'
        else:
            return 'night'
    
    def _get_usage_level(self, usage: float) -> str:
        """Get usage level description."""
        if usage < 30:
            return 'low'
        elif usage < 70:
            return 'medium'
        else:
            return 'high'
    
    def _get_load_level(self, load: float) -> str:
        """Get system load level."""
        if load < 0.3:
            return 'light'
        elif load < 0.7:
            return 'moderate'
        else:
            return 'heavy'
    
    def _calculate_activity_level(self, frequency: float) -> str:
        """Calculate user activity level."""
        if frequency < 0.3:
            return 'low'
        elif frequency < 0.7:
            return 'moderate'
        else:
            return 'high'
    
    def _infer_expertise_level(self, command: ProcessedCommand, system_state: Dict[str, Any]) -> str:
        """Infer user expertise level."""
        # Simple heuristic based on command complexity and system usage
        complexity = self._calculate_command_complexity(command)
        if complexity > 0.7:
            return 'expert'
        elif complexity > 0.4:
            return 'intermediate'
        else:
            return 'beginner'
    
    def _infer_user_state(self, command: ProcessedCommand, system_state: Dict[str, Any]) -> str:
        """Infer user emotional/cognitive state."""
        # Simple heuristic based on command patterns
        text = command.text.lower()
        if any(word in text for word in ['urgent', 'quickly', 'fast', 'now', 'immediately']):
            return 'urgent'
        elif any(word in text for word in ['help', 'confused', 'stuck', 'problem']):
            return 'frustrated'
        elif any(word in text for word in ['thanks', 'good', 'great', 'perfect']):
            return 'satisfied'
        else:
            return 'neutral'
    
    def _infer_current_task(self, command: ProcessedCommand, system_state: Dict[str, Any]) -> str:
        """Infer current user task."""
        intent = command.intent.value
        if intent in ['file_operation', 'file_search']:
            return 'file_management'
        elif intent in ['system_info', 'system_control']:
            return 'system_administration'
        elif intent in ['web_search', 'information']:
            return 'research'
        else:
            return 'general'
    
    def _classify_directory(self, path: str) -> str:
        """Classify directory type."""
        path_lower = path.lower()
        if 'desktop' in path_lower:
            return 'desktop'
        elif 'documents' in path_lower:
            return 'documents'
        elif 'downloads' in path_lower:
            return 'downloads'
        elif 'projects' in path_lower or 'code' in path_lower:
            return 'development'
        else:
            return 'other'
    
    def _calculate_command_complexity(self, command: ProcessedCommand) -> float:
        """Calculate command complexity score."""
        complexity = 0.0
        
        # Length factor
        complexity += min(len(command.text) / 100.0, 0.3)
        
        # Entity factor
        complexity += min(len(command.entities) / 10.0, 0.3)
        
        # Intent complexity
        complex_intents = ['system_control', 'automation', 'advanced_search']
        if command.intent.value in complex_intents:
            complexity += 0.4
        
        return min(complexity, 1.0)
    
    def _get_complexity_level(self, complexity: float) -> str:
        """Get complexity level description."""
        if complexity < 0.3:
            return 'simple'
        elif complexity < 0.7:
            return 'moderate'
        else:
            return 'complex'
    
    def _infer_command_urgency(self, command: ProcessedCommand) -> str:
        """Infer command urgency from language."""
        text = command.text.lower()
        urgent_words = ['urgent', 'emergency', 'critical', 'immediately', 'asap', 'now']
        
        if any(word in text for word in urgent_words):
            return 'high'
        elif any(word in text for word in ['quick', 'fast', 'soon']):
            return 'medium'
        else:
            return 'low'
    
    async def _analyze_context_patterns(self):
        """Analyze patterns in context elements."""
        try:
            # Look for recurring context combinations
            if len(self.context_history) >= self.pattern_threshold:
                recent_contexts = self.context_history[-10:]  # Last 10 contexts
                
                # Analyze element co-occurrence
                element_combinations = defaultdict(int)
                for context in recent_contexts:
                    elements = list(context.get('elements', {}).keys())
                    for i in range(len(elements)):
                        for j in range(i + 1, len(elements)):
                            combo = tuple(sorted([elements[i], elements[j]]))
                            element_combinations[combo] += 1
                
                # Create patterns for frequent combinations
                for combo, frequency in element_combinations.items():
                    if frequency >= self.pattern_threshold:
                        pattern_id = f"pattern_{'_'.join(combo)}"
                        if pattern_id not in self.context_patterns:
                            self.context_patterns[pattern_id] = ContextPattern(
                                pattern_id=pattern_id,
                                pattern_type='co_occurrence',
                                elements=list(combo),
                                frequency=frequency,
                                confidence=min(frequency / 10.0, 1.0),
                                last_seen=time.time(),
                                conditions={}
                            )
                        else:
                            pattern = self.context_patterns[pattern_id]
                            pattern.frequency = frequency
                            pattern.last_seen = time.time()
            
        except Exception as e:
            logger.error(f"Error analyzing context patterns: {e}")
    
    async def _calculate_relevance_scores(self, command: ProcessedCommand) -> Dict[str, float]:
        """Calculate relevance scores for context elements."""
        relevance_scores = {}
        
        try:
            for element_id, element in self.current_context.items():
                score = 0.0
                
                # Base confidence
                score += element.confidence * 0.3
                
                # Temporal relevance
                age = time.time() - element.timestamp
                temporal_score = max(0, 1 - (age / 3600))  # Decay over 1 hour
                score += temporal_score * 0.2
                
                # Type relevance
                if element.element_type in ['semantic', 'user']:
                    score += 0.3
                elif element.element_type in ['system', 'temporal']:
                    score += 0.2
                
                # Intent relevance
                if command.intent.value in ['system_info', 'system_control'] and element.element_type == 'system':
                    score += 0.2
                elif command.intent.value in ['file_operation'] and element.element_type == 'environmental':
                    score += 0.2
                
                relevance_scores[element_id] = min(score, 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating relevance scores: {e}")
        
        return relevance_scores
    
    def _cleanup_old_context(self):
        """Clean up old context data."""
        try:
            # Keep only recent history
            max_history = 100
            if len(self.context_history) > max_history:
                self.context_history = self.context_history[-max_history:]
            
            # Remove old context elements
            current_time = time.time()
            cutoff_time = current_time - (self.context_window * 60)
            
            expired_elements = [
                element_id for element_id, element in self.current_context.items()
                if element.timestamp < cutoff_time
            ]
            
            for element_id in expired_elements:
                del self.current_context[element_id]
            
        except Exception as e:
            logger.error(f"Error cleaning up old context: {e}")
    
    async def get_context_summary(self) -> Dict[str, Any]:
        """Get a summary of current context."""
        try:
            summary = {
                'active_elements': len(self.current_context),
                'patterns_detected': len(self.context_patterns),
                'context_types': {},
                'high_relevance_elements': [],
                'recent_patterns': []
            }
            
            # Count by type
            for element in self.current_context.values():
                element_type = element.element_type
                summary['context_types'][element_type] = summary['context_types'].get(element_type, 0) + 1
            
            # High relevance elements (placeholder - would need relevance calculation)
            summary['high_relevance_elements'] = list(self.current_context.keys())[:5]
            
            # Recent patterns
            recent_patterns = sorted(
                self.context_patterns.values(),
                key=lambda p: p.last_seen,
                reverse=True
            )[:5]
            summary['recent_patterns'] = [p.pattern_id for p in recent_patterns]
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting context summary: {e}")
            return {}


# Factory functions
def create_context_element(element_id: str, element_type: str, value: Any) -> ContextElement:
    """Create a new context element."""
    return ContextElement(
        element_id=element_id,
        element_type=element_type,
        value=value,
        confidence=0.7,
        source='manual',
        timestamp=time.time(),
        metadata={}
    )
