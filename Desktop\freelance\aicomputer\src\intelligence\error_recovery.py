"""
Enhanced Error Recovery System - Phase 2 Component

Advanced error detection, analysis, and recovery mechanisms with learning capabilities.
"""

import asyncio
import time
import traceback
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import json

from loguru import logger

from ..utils.config_manager import ConfigManager
from ..core.ai_processor import ProcessedCommand, CommandIntent


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RecoveryStrategy(Enum):
    """Recovery strategy types."""
    RETRY = "retry"
    ALTERNATIVE = "alternative"
    FALLBACK = "fallback"
    USER_INTERVENTION = "user_intervention"
    SYSTEM_RESTART = "system_restart"


@dataclass
class ErrorContext:
    """Error context information."""
    error_id: str
    timestamp: float
    component: str
    error_type: str
    error_message: str
    severity: ErrorSeverity
    command_context: Optional[ProcessedCommand]
    system_state: Dict[str, Any]
    stack_trace: str
    recovery_attempts: int = 0


@dataclass
class RecoveryAction:
    """Recovery action definition."""
    action_id: str
    strategy: RecoveryStrategy
    description: str
    parameters: Dict[str, Any]
    success_rate: float
    execution_time: float
    prerequisites: List[str]


@dataclass
class ErrorPattern:
    """Error pattern for learning."""
    pattern_id: str
    error_signature: str
    frequency: int
    successful_recoveries: List[str]
    failed_recoveries: List[str]
    last_occurrence: float


class ErrorRecoverySystem:
    """
    Advanced error recovery system with learning and adaptation.
    
    Features:
    - Intelligent error classification and analysis
    - Context-aware recovery strategy selection
    - Learning from recovery success/failure patterns
    - Proactive error prevention
    - Automated recovery execution
    - User notification and intervention management
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        
        # Error tracking
        self.error_history: List[ErrorContext] = []
        self.error_patterns: Dict[str, ErrorPattern] = {}
        self.recovery_actions: Dict[str, RecoveryAction] = {}
        
        # Recovery state
        self.active_recoveries: Dict[str, Dict[str, Any]] = {}
        self.recovery_statistics: Dict[str, Any] = {}
        
        # Configuration
        self.max_retry_attempts = self.config.get("intelligence.error_recovery.max_retries", 3)
        self.recovery_timeout = self.config.get("intelligence.error_recovery.timeout", 300)
        self.learning_enabled = self.config.get("intelligence.error_recovery.learning", True)
        
        # Callbacks
        self.on_error_detected: Optional[Callable[[ErrorContext], None]] = None
        self.on_recovery_started: Optional[Callable[[ErrorContext, RecoveryAction], None]] = None
        self.on_recovery_completed: Optional[Callable[[ErrorContext, bool], None]] = None
        self.on_critical_error: Optional[Callable[[ErrorContext], None]] = None
        
        # Initialize recovery actions
        self._initialize_recovery_actions()
        
        # Load error patterns
        self._load_error_patterns()
        
        logger.info("Error Recovery System initialized")
    
    def _initialize_recovery_actions(self):
        """Initialize predefined recovery actions."""
        
        # Voice engine recovery actions
        self.recovery_actions["restart_voice_engine"] = RecoveryAction(
            action_id="restart_voice_engine",
            strategy=RecoveryStrategy.RETRY,
            description="Restart voice recognition engine",
            parameters={"component": "voice_engine"},
            success_rate=0.8,
            execution_time=5.0,
            prerequisites=[]
        )
        
        self.recovery_actions["switch_recognition_engine"] = RecoveryAction(
            action_id="switch_recognition_engine",
            strategy=RecoveryStrategy.ALTERNATIVE,
            description="Switch to alternative recognition engine",
            parameters={"fallback_engine": "google"},
            success_rate=0.7,
            execution_time=3.0,
            prerequisites=[]
        )
        
        # AI processor recovery actions
        self.recovery_actions["retry_ai_processing"] = RecoveryAction(
            action_id="retry_ai_processing",
            strategy=RecoveryStrategy.RETRY,
            description="Retry AI command processing",
            parameters={"max_attempts": 3},
            success_rate=0.6,
            execution_time=2.0,
            prerequisites=[]
        )
        
        self.recovery_actions["use_fallback_parser"] = RecoveryAction(
            action_id="use_fallback_parser",
            strategy=RecoveryStrategy.FALLBACK,
            description="Use fallback command parser",
            parameters={"parser_type": "pattern_matching"},
            success_rate=0.5,
            execution_time=1.0,
            prerequisites=[]
        )
        
        # Command execution recovery actions
        self.recovery_actions["retry_command_execution"] = RecoveryAction(
            action_id="retry_command_execution",
            strategy=RecoveryStrategy.RETRY,
            description="Retry command execution",
            parameters={"delay": 1.0},
            success_rate=0.7,
            execution_time=2.0,
            prerequisites=[]
        )
        
        self.recovery_actions["request_user_confirmation"] = RecoveryAction(
            action_id="request_user_confirmation",
            strategy=RecoveryStrategy.USER_INTERVENTION,
            description="Request user confirmation or alternative",
            parameters={"timeout": 30},
            success_rate=0.9,
            execution_time=15.0,
            prerequisites=[]
        )
        
        # System-level recovery actions
        self.recovery_actions["restart_system_controller"] = RecoveryAction(
            action_id="restart_system_controller",
            strategy=RecoveryStrategy.SYSTEM_RESTART,
            description="Restart system controller",
            parameters={"preserve_state": True},
            success_rate=0.9,
            execution_time=10.0,
            prerequisites=[]
        )
    
    async def handle_error(self, error: Exception, component: str, 
                          command_context: Optional[ProcessedCommand] = None,
                          system_state: Optional[Dict[str, Any]] = None) -> bool:
        """
        Handle an error with intelligent recovery.
        
        Args:
            error: The exception that occurred
            component: Component where error occurred
            command_context: Command being processed when error occurred
            system_state: Current system state
            
        Returns:
            True if recovery was successful, False otherwise
        """
        
        try:
            # Create error context
            error_context = self._create_error_context(
                error, component, command_context, system_state
            )
            
            # Classify error severity
            error_context.severity = self._classify_error_severity(error_context)
            
            # Log error
            logger.error(f"Error detected in {component}: {error_context.error_message}")
            
            # Trigger callback
            if self.on_error_detected:
                self.on_error_detected(error_context)
            
            # Handle critical errors immediately
            if error_context.severity == ErrorSeverity.CRITICAL:
                if self.on_critical_error:
                    self.on_critical_error(error_context)
                return await self._handle_critical_error(error_context)
            
            # Attempt recovery
            recovery_success = await self._attempt_recovery(error_context)
            
            # Update learning data
            if self.learning_enabled:
                await self._update_error_patterns(error_context, recovery_success)
            
            return recovery_success
            
        except Exception as recovery_error:
            logger.error(f"Error in error recovery system: {recovery_error}")
            return False
    
    def _create_error_context(self, error: Exception, component: str,
                            command_context: Optional[ProcessedCommand],
                            system_state: Optional[Dict[str, Any]]) -> ErrorContext:
        """Create error context from exception."""
        
        error_id = f"{component}_{int(time.time())}_{hash(str(error)) % 10000}"
        
        return ErrorContext(
            error_id=error_id,
            timestamp=time.time(),
            component=component,
            error_type=type(error).__name__,
            error_message=str(error),
            severity=ErrorSeverity.MEDIUM,  # Will be updated by classification
            command_context=command_context,
            system_state=system_state or {},
            stack_trace=traceback.format_exc()
        )
    
    def _classify_error_severity(self, error_context: ErrorContext) -> ErrorSeverity:
        """Classify error severity based on context."""
        
        error_type = error_context.error_type
        component = error_context.component
        error_message = error_context.error_message.lower()
        
        # Critical errors
        if any(keyword in error_message for keyword in [
            "system", "critical", "fatal", "crash", "corruption"
        ]):
            return ErrorSeverity.CRITICAL
        
        # High severity errors
        if component in ["system_controller", "safety_manager"]:
            return ErrorSeverity.HIGH
        
        if error_type in ["MemoryError", "SystemError", "OSError"]:
            return ErrorSeverity.HIGH
        
        # Medium severity errors
        if component in ["voice_engine", "ai_processor"]:
            return ErrorSeverity.MEDIUM
        
        if error_type in ["ConnectionError", "TimeoutError", "PermissionError"]:
            return ErrorSeverity.MEDIUM
        
        # Low severity errors
        return ErrorSeverity.LOW
    
    async def _attempt_recovery(self, error_context: ErrorContext) -> bool:
        """Attempt to recover from error using appropriate strategy."""
        
        try:
            # Get recovery strategy
            recovery_actions = self._select_recovery_actions(error_context)
            
            if not recovery_actions:
                logger.warning(f"No recovery actions available for error: {error_context.error_id}")
                return False
            
            # Try recovery actions in order of preference
            for action in recovery_actions:
                if error_context.recovery_attempts >= self.max_retry_attempts:
                    logger.warning(f"Maximum recovery attempts reached for error: {error_context.error_id}")
                    break
                
                logger.info(f"Attempting recovery: {action.description}")
                
                # Trigger callback
                if self.on_recovery_started:
                    self.on_recovery_started(error_context, action)
                
                # Execute recovery action
                success = await self._execute_recovery_action(error_context, action)
                
                error_context.recovery_attempts += 1
                
                if success:
                    logger.info(f"Recovery successful: {action.description}")
                    
                    # Trigger callback
                    if self.on_recovery_completed:
                        self.on_recovery_completed(error_context, True)
                    
                    return True
                else:
                    logger.warning(f"Recovery failed: {action.description}")
            
            # All recovery attempts failed
            if self.on_recovery_completed:
                self.on_recovery_completed(error_context, False)
            
            return False
            
        except Exception as e:
            logger.error(f"Error during recovery attempt: {e}")
            return False
    
    def _select_recovery_actions(self, error_context: ErrorContext) -> List[RecoveryAction]:
        """Select appropriate recovery actions for the error."""
        
        component = error_context.component
        error_type = error_context.error_type
        severity = error_context.severity
        
        candidate_actions = []
        
        # Component-specific actions
        if component == "voice_engine":
            candidate_actions.extend([
                self.recovery_actions["restart_voice_engine"],
                self.recovery_actions["switch_recognition_engine"]
            ])
        elif component == "ai_processor":
            candidate_actions.extend([
                self.recovery_actions["retry_ai_processing"],
                self.recovery_actions["use_fallback_parser"]
            ])
        elif component == "command_executor":
            candidate_actions.extend([
                self.recovery_actions["retry_command_execution"],
                self.recovery_actions["request_user_confirmation"]
            ])
        
        # Severity-based actions
        if severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            candidate_actions.append(self.recovery_actions["restart_system_controller"])
        
        # Error type-specific actions
        if error_type in ["TimeoutError", "ConnectionError"]:
            # Prefer retry strategies for transient errors
            candidate_actions = [a for a in candidate_actions if a.strategy == RecoveryStrategy.RETRY] + \
                              [a for a in candidate_actions if a.strategy != RecoveryStrategy.RETRY]
        
        # Check for learned patterns
        error_signature = self._create_error_signature(error_context)
        if error_signature in self.error_patterns:
            pattern = self.error_patterns[error_signature]
            # Prioritize actions that have been successful for this pattern
            successful_actions = [
                action for action in candidate_actions
                if action.action_id in pattern.successful_recoveries
            ]
            if successful_actions:
                candidate_actions = successful_actions + [
                    a for a in candidate_actions if a not in successful_actions
                ]
        
        # Sort by success rate
        candidate_actions.sort(key=lambda a: a.success_rate, reverse=True)
        
        return candidate_actions[:3]  # Return top 3 actions
    
    async def _execute_recovery_action(self, error_context: ErrorContext, 
                                     action: RecoveryAction) -> bool:
        """Execute a specific recovery action."""
        
        try:
            action_id = action.action_id
            
            # Track active recovery
            self.active_recoveries[error_context.error_id] = {
                "action": action,
                "start_time": time.time(),
                "status": "running"
            }
            
            # Execute action based on strategy
            if action.strategy == RecoveryStrategy.RETRY:
                success = await self._execute_retry_action(error_context, action)
            elif action.strategy == RecoveryStrategy.ALTERNATIVE:
                success = await self._execute_alternative_action(error_context, action)
            elif action.strategy == RecoveryStrategy.FALLBACK:
                success = await self._execute_fallback_action(error_context, action)
            elif action.strategy == RecoveryStrategy.USER_INTERVENTION:
                success = await self._execute_user_intervention(error_context, action)
            elif action.strategy == RecoveryStrategy.SYSTEM_RESTART:
                success = await self._execute_system_restart(error_context, action)
            else:
                logger.warning(f"Unknown recovery strategy: {action.strategy}")
                success = False
            
            # Update recovery tracking
            if error_context.error_id in self.active_recoveries:
                self.active_recoveries[error_context.error_id]["status"] = "completed" if success else "failed"
                self.active_recoveries[error_context.error_id]["end_time"] = time.time()
            
            return success
            
        except Exception as e:
            logger.error(f"Error executing recovery action {action.action_id}: {e}")
            return False
    
    async def _execute_retry_action(self, error_context: ErrorContext, 
                                  action: RecoveryAction) -> bool:
        """Execute retry-based recovery action."""
        
        try:
            component = error_context.component
            
            if action.action_id == "restart_voice_engine":
                # Simulate voice engine restart
                await asyncio.sleep(1)
                logger.info("Voice engine restarted")
                return True
            
            elif action.action_id == "retry_ai_processing":
                # Simulate AI processing retry
                await asyncio.sleep(0.5)
                logger.info("AI processing retried")
                return True
            
            elif action.action_id == "retry_command_execution":
                # Simulate command execution retry
                delay = action.parameters.get("delay", 1.0)
                await asyncio.sleep(delay)
                logger.info("Command execution retried")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error in retry action: {e}")
            return False
    
    async def _execute_alternative_action(self, error_context: ErrorContext, 
                                        action: RecoveryAction) -> bool:
        """Execute alternative-based recovery action."""
        
        try:
            if action.action_id == "switch_recognition_engine":
                fallback_engine = action.parameters.get("fallback_engine", "google")
                logger.info(f"Switched to fallback recognition engine: {fallback_engine}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error in alternative action: {e}")
            return False
    
    async def _execute_fallback_action(self, error_context: ErrorContext, 
                                     action: RecoveryAction) -> bool:
        """Execute fallback-based recovery action."""
        
        try:
            if action.action_id == "use_fallback_parser":
                parser_type = action.parameters.get("parser_type", "pattern_matching")
                logger.info(f"Using fallback parser: {parser_type}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error in fallback action: {e}")
            return False
    
    async def _execute_user_intervention(self, error_context: ErrorContext, 
                                       action: RecoveryAction) -> bool:
        """Execute user intervention recovery action."""
        
        try:
            timeout = action.parameters.get("timeout", 30)
            
            # In a real implementation, this would show a dialog to the user
            logger.info(f"Requesting user intervention for error: {error_context.error_message}")
            
            # Simulate user response (in real implementation, wait for actual user input)
            await asyncio.sleep(1)
            
            # Assume user provides guidance
            return True
            
        except Exception as e:
            logger.error(f"Error in user intervention: {e}")
            return False
    
    async def _execute_system_restart(self, error_context: ErrorContext, 
                                    action: RecoveryAction) -> bool:
        """Execute system restart recovery action."""
        
        try:
            preserve_state = action.parameters.get("preserve_state", True)
            
            logger.info("Initiating system controller restart")
            
            # In a real implementation, this would restart the system controller
            await asyncio.sleep(2)
            
            logger.info("System controller restarted")
            return True
            
        except Exception as e:
            logger.error(f"Error in system restart: {e}")
            return False
    
    async def _handle_critical_error(self, error_context: ErrorContext) -> bool:
        """Handle critical errors with immediate action."""
        
        logger.critical(f"Critical error detected: {error_context.error_message}")
        
        # Immediate system restart for critical errors
        return await self._execute_recovery_action(
            error_context, 
            self.recovery_actions["restart_system_controller"]
        )
    
    def _create_error_signature(self, error_context: ErrorContext) -> str:
        """Create a signature for error pattern matching."""
        
        return f"{error_context.component}_{error_context.error_type}_{hash(error_context.error_message) % 1000}"
    
    async def _update_error_patterns(self, error_context: ErrorContext, recovery_success: bool):
        """Update error patterns for learning."""
        
        try:
            signature = self._create_error_signature(error_context)
            
            if signature in self.error_patterns:
                pattern = self.error_patterns[signature]
                pattern.frequency += 1
                pattern.last_occurrence = error_context.timestamp
            else:
                pattern = ErrorPattern(
                    pattern_id=signature,
                    error_signature=signature,
                    frequency=1,
                    successful_recoveries=[],
                    failed_recoveries=[],
                    last_occurrence=error_context.timestamp
                )
                self.error_patterns[signature] = pattern
            
            # Update recovery success/failure tracking
            if error_context.error_id in self.active_recoveries:
                action_id = self.active_recoveries[error_context.error_id]["action"].action_id
                
                if recovery_success:
                    if action_id not in pattern.successful_recoveries:
                        pattern.successful_recoveries.append(action_id)
                else:
                    if action_id not in pattern.failed_recoveries:
                        pattern.failed_recoveries.append(action_id)
            
        except Exception as e:
            logger.error(f"Error updating error patterns: {e}")
    
    def _load_error_patterns(self):
        """Load error patterns from storage."""
        
        try:
            patterns_file = Path("data/error_patterns.json")
            if patterns_file.exists():
                with open(patterns_file, "r") as f:
                    patterns_data = json.load(f)
                    self.error_patterns = {
                        k: ErrorPattern(**v) for k, v in patterns_data.items()
                    }
                logger.info("Error patterns loaded successfully")
        except Exception as e:
            logger.error(f"Error loading error patterns: {e}")
    
    def save_error_patterns(self):
        """Save error patterns to storage."""
        
        try:
            Path("data").mkdir(exist_ok=True)
            with open("data/error_patterns.json", "w") as f:
                patterns_data = {k: asdict(v) for k, v in self.error_patterns.items()}
                json.dump(patterns_data, f, indent=2)
            logger.info("Error patterns saved successfully")
        except Exception as e:
            logger.error(f"Error saving error patterns: {e}")
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get comprehensive error and recovery statistics."""
        
        total_errors = len(self.error_history)
        if total_errors == 0:
            return {"total_errors": 0}
        
        # Error distribution by component
        component_errors = {}
        severity_distribution = {}
        recovery_success_rate = 0
        
        for error in self.error_history:
            # Component distribution
            component = error.component
            component_errors[component] = component_errors.get(component, 0) + 1
            
            # Severity distribution
            severity = error.severity.value
            severity_distribution[severity] = severity_distribution.get(severity, 0) + 1
            
            # Recovery success rate
            if error.recovery_attempts > 0:
                # Assume recovery was successful if attempts were made
                # In real implementation, track actual success
                recovery_success_rate += 1
        
        recovery_success_rate = recovery_success_rate / total_errors if total_errors > 0 else 0
        
        return {
            "total_errors": total_errors,
            "component_distribution": component_errors,
            "severity_distribution": severity_distribution,
            "recovery_success_rate": recovery_success_rate,
            "error_patterns_learned": len(self.error_patterns),
            "active_recoveries": len(self.active_recoveries),
            "most_common_errors": list(self.error_patterns.keys())[:5]
        }
