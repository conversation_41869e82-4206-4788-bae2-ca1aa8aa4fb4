"""
Intent Classifier - Phase 2 Component

Advanced intent classification for understanding user commands and requests.
"""

import asyncio
import time
import json
import re
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
from datetime import datetime, timedelta
import numpy as np

from loguru import logger

from ..utils.config_manager import ConfigManager
from ..core.ai_processor import CommandIntent


@dataclass
class IntentPrediction:
    """Intent prediction result."""
    intent: CommandIntent
    confidence: float
    features: List[float]
    reasoning: str
    alternatives: List[Tuple[CommandIntent, float]]
    timestamp: float


@dataclass
class IntentPattern:
    """Intent pattern for classification."""
    pattern_id: str
    intent: CommandIntent
    keywords: List[str]
    patterns: List[str]
    weight: float
    frequency: int
    accuracy: float
    last_used: float


class IntentClassifier:
    """
    Advanced intent classification system.
    
    Features:
    - Multi-modal intent detection
    - Context-aware classification
    - Learning from user feedback
    - Pattern-based classification
    - Confidence scoring
    - Intent disambiguation
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        
        # Classification data
        self.intent_patterns: Dict[str, IntentPattern] = {}
        self.classification_history: List[IntentPrediction] = []
        self.feedback_data: List[Dict[str, Any]] = []
        
        # Classification models
        self.keyword_weights: Dict[str, Dict[CommandIntent, float]] = defaultdict(lambda: defaultdict(float))
        self.pattern_weights: Dict[str, Dict[CommandIntent, float]] = defaultdict(lambda: defaultdict(float))
        
        # Configuration
        self.confidence_threshold = self.config.get("intelligence.intent.confidence_threshold", 0.7)
        self.max_alternatives = self.config.get("intelligence.intent.max_alternatives", 3)
        self.learning_rate = self.config.get("intelligence.intent.learning_rate", 0.1)
        
        # Initialize classification patterns
        self._initialize_intent_patterns()
        
        logger.info("Intent Classifier initialized")
    
    def _initialize_intent_patterns(self):
        """Initialize predefined intent patterns."""
        try:
            # File operation patterns
            self.intent_patterns["file_ops"] = IntentPattern(
                pattern_id="file_ops",
                intent=CommandIntent.FILE_OPERATION,
                keywords=["file", "folder", "directory", "create", "delete", "copy", "move", "rename"],
                patterns=[
                    r"(create|make|new)\s+(file|folder|directory)",
                    r"(delete|remove|rm)\s+",
                    r"(copy|cp)\s+",
                    r"(move|mv)\s+",
                    r"(rename|ren)\s+"
                ],
                weight=1.0,
                frequency=0,
                accuracy=0.8,
                last_used=time.time()
            )
            
            # System information patterns
            self.intent_patterns["system_info"] = IntentPattern(
                pattern_id="system_info",
                intent=CommandIntent.SYSTEM_INFO,
                keywords=["system", "info", "status", "cpu", "memory", "disk", "performance"],
                patterns=[
                    r"(system|computer)\s+(info|information|status)",
                    r"(cpu|memory|ram|disk)\s+(usage|status)",
                    r"(performance|speed|load)",
                    r"(check|show|display)\s+(system|status)"
                ],
                weight=1.0,
                frequency=0,
                accuracy=0.85,
                last_used=time.time()
            )
            
            # Web search patterns
            self.intent_patterns["web_search"] = IntentPattern(
                pattern_id="web_search",
                intent=CommandIntent.WEB_SEARCH,
                keywords=["search", "google", "find", "look", "web", "internet", "online"],
                patterns=[
                    r"(search|google|find)\s+(for|about)",
                    r"(look\s+up|lookup)",
                    r"(web|internet|online)\s+search",
                    r"(find|search)\s+.*\s+(online|web)"
                ],
                weight=1.0,
                frequency=0,
                accuracy=0.9,
                last_used=time.time()
            )
            
            # Application control patterns
            self.intent_patterns["app_control"] = IntentPattern(
                pattern_id="app_control",
                intent=CommandIntent.APPLICATION_CONTROL,
                keywords=["open", "close", "start", "stop", "launch", "quit", "exit", "application", "app"],
                patterns=[
                    r"(open|launch|start)\s+",
                    r"(close|quit|exit|stop)\s+",
                    r"(run|execute)\s+",
                    r"(kill|terminate)\s+"
                ],
                weight=1.0,
                frequency=0,
                accuracy=0.88,
                last_used=time.time()
            )
            
            # System control patterns
            self.intent_patterns["system_control"] = IntentPattern(
                pattern_id="system_control",
                intent=CommandIntent.SYSTEM_CONTROL,
                keywords=["shutdown", "restart", "reboot", "sleep", "hibernate", "lock", "logout"],
                patterns=[
                    r"(shutdown|power\s+off)",
                    r"(restart|reboot)",
                    r"(sleep|hibernate)",
                    r"(lock|logout|log\s+out)"
                ],
                weight=1.0,
                frequency=0,
                accuracy=0.95,
                last_used=time.time()
            )
            
            # Information request patterns
            self.intent_patterns["information"] = IntentPattern(
                pattern_id="information",
                intent=CommandIntent.INFORMATION,
                keywords=["what", "how", "when", "where", "why", "who", "explain", "tell", "show"],
                patterns=[
                    r"(what|how|when|where|why|who)\s+",
                    r"(explain|tell\s+me|show\s+me)",
                    r"(help|assistance|support)",
                    r"(define|definition)"
                ],
                weight=1.0,
                frequency=0,
                accuracy=0.75,
                last_used=time.time()
            )
            
            logger.info(f"Initialized {len(self.intent_patterns)} intent patterns")
            
        except Exception as e:
            logger.error(f"Error initializing intent patterns: {e}")
    
    async def classify_intent(self, text: str, context: Dict[str, Any] = None) -> IntentPrediction:
        """Classify the intent of a text command."""
        try:
            if context is None:
                context = {}
            
            # Extract features
            features = self._extract_features(text, context)
            
            # Calculate intent scores
            intent_scores = await self._calculate_intent_scores(text, features, context)
            
            # Get best intent and alternatives
            sorted_intents = sorted(intent_scores.items(), key=lambda x: x[1], reverse=True)
            
            best_intent, best_score = sorted_intents[0] if sorted_intents else (CommandIntent.UNKNOWN, 0.0)
            alternatives = [(intent, score) for intent, score in sorted_intents[1:self.max_alternatives+1]]
            
            # Generate reasoning
            reasoning = self._generate_reasoning(text, best_intent, best_score, features)
            
            # Create prediction
            prediction = IntentPrediction(
                intent=best_intent,
                confidence=best_score,
                features=features,
                reasoning=reasoning,
                alternatives=alternatives,
                timestamp=time.time()
            )
            
            # Store in history
            self.classification_history.append(prediction)
            self._cleanup_old_history()
            
            # Update pattern usage
            await self._update_pattern_usage(best_intent, text)
            
            return prediction
            
        except Exception as e:
            logger.error(f"Error classifying intent: {e}")
            return IntentPrediction(
                intent=CommandIntent.UNKNOWN,
                confidence=0.0,
                features=[],
                reasoning="Classification error occurred",
                alternatives=[],
                timestamp=time.time()
            )
    
    def _extract_features(self, text: str, context: Dict[str, Any]) -> List[float]:
        """Extract numerical features from text and context."""
        features = []
        
        try:
            text_lower = text.lower()
            
            # Text length features
            features.append(len(text) / 100.0)  # Normalized length
            features.append(len(text.split()) / 20.0)  # Normalized word count
            
            # Keyword presence features
            for pattern in self.intent_patterns.values():
                keyword_score = sum(1 for keyword in pattern.keywords if keyword in text_lower)
                features.append(keyword_score / len(pattern.keywords))
            
            # Pattern match features
            for pattern in self.intent_patterns.values():
                pattern_score = 0
                for regex_pattern in pattern.patterns:
                    if re.search(regex_pattern, text_lower):
                        pattern_score += 1
                features.append(pattern_score / len(pattern.patterns))
            
            # Context features
            features.append(context.get('session_length', 0) / 3600.0)  # Session length
            features.append(context.get('command_count', 0) / 100.0)  # Command count
            features.append(context.get('error_rate', 0))  # Error rate
            
            # Time features
            current_hour = datetime.now().hour
            features.append(current_hour / 24.0)  # Normalized hour
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting features: {e}")
            return [0.0] * 20  # Return default features
    
    async def _calculate_intent_scores(self, text: str, features: List[float], 
                                     context: Dict[str, Any]) -> Dict[CommandIntent, float]:
        """Calculate scores for each intent."""
        intent_scores = defaultdict(float)
        text_lower = text.lower()
        
        try:
            # Pattern-based scoring
            for pattern in self.intent_patterns.values():
                score = 0.0
                
                # Keyword matching
                keyword_matches = sum(1 for keyword in pattern.keywords if keyword in text_lower)
                keyword_score = (keyword_matches / len(pattern.keywords)) * 0.4
                
                # Regex pattern matching
                pattern_matches = sum(1 for regex_pattern in pattern.patterns 
                                    if re.search(regex_pattern, text_lower))
                pattern_score = (pattern_matches / len(pattern.patterns)) * 0.6
                
                # Combine scores
                score = keyword_score + pattern_score
                
                # Apply pattern weight and accuracy
                score *= pattern.weight * pattern.accuracy
                
                # Context boost
                if self._has_context_boost(pattern.intent, context):
                    score *= 1.2
                
                intent_scores[pattern.intent] = score
            
            # Normalize scores
            max_score = max(intent_scores.values()) if intent_scores else 1.0
            if max_score > 0:
                for intent in intent_scores:
                    intent_scores[intent] = min(intent_scores[intent] / max_score, 1.0)
            
            # Add unknown intent as fallback
            if not intent_scores or max(intent_scores.values()) < 0.3:
                intent_scores[CommandIntent.UNKNOWN] = 0.5
            
            return dict(intent_scores)
            
        except Exception as e:
            logger.error(f"Error calculating intent scores: {e}")
            return {CommandIntent.UNKNOWN: 0.5}
    
    def _has_context_boost(self, intent: CommandIntent, context: Dict[str, Any]) -> bool:
        """Check if intent should get context boost."""
        try:
            # File operations boost in file-related contexts
            if intent == CommandIntent.FILE_OPERATION:
                return context.get('current_directory') is not None
            
            # System info boost during high resource usage
            if intent == CommandIntent.SYSTEM_INFO:
                return (context.get('cpu_usage', 0) > 70 or 
                       context.get('memory_usage', 0) > 80)
            
            # Web search boost during research sessions
            if intent == CommandIntent.WEB_SEARCH:
                return context.get('browser_active', False)
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking context boost: {e}")
            return False
    
    def _generate_reasoning(self, text: str, intent: CommandIntent, 
                          confidence: float, features: List[float]) -> str:
        """Generate human-readable reasoning for classification."""
        try:
            if intent == CommandIntent.UNKNOWN:
                return "Could not determine intent with sufficient confidence"
            
            # Find matching pattern
            matching_pattern = None
            for pattern in self.intent_patterns.values():
                if pattern.intent == intent:
                    matching_pattern = pattern
                    break
            
            if matching_pattern:
                matched_keywords = [kw for kw in matching_pattern.keywords if kw in text.lower()]
                matched_patterns = [p for p in matching_pattern.patterns if re.search(p, text.lower())]
                
                reasoning_parts = []
                
                if matched_keywords:
                    reasoning_parts.append(f"Keywords: {', '.join(matched_keywords)}")
                
                if matched_patterns:
                    reasoning_parts.append(f"Pattern matches: {len(matched_patterns)}")
                
                reasoning_parts.append(f"Confidence: {confidence:.2f}")
                
                return "; ".join(reasoning_parts)
            
            return f"Classified as {intent.value} with {confidence:.2f} confidence"
            
        except Exception as e:
            logger.error(f"Error generating reasoning: {e}")
            return "Classification reasoning unavailable"
    
    async def _update_pattern_usage(self, intent: CommandIntent, text: str):
        """Update pattern usage statistics."""
        try:
            for pattern in self.intent_patterns.values():
                if pattern.intent == intent:
                    pattern.frequency += 1
                    pattern.last_used = time.time()
                    break
            
        except Exception as e:
            logger.error(f"Error updating pattern usage: {e}")
    
    async def learn_from_feedback(self, text: str, correct_intent: CommandIntent, 
                                predicted_intent: CommandIntent, confidence: float):
        """Learn from user feedback to improve classification."""
        try:
            feedback_entry = {
                'text': text,
                'correct_intent': correct_intent.value,
                'predicted_intent': predicted_intent.value,
                'confidence': confidence,
                'timestamp': time.time(),
                'processed': False
            }
            
            self.feedback_data.append(feedback_entry)
            
            # Process feedback immediately
            await self._process_feedback(feedback_entry)
            
            logger.debug(f"Learned from feedback: {text} -> {correct_intent.value}")
            
        except Exception as e:
            logger.error(f"Error learning from feedback: {e}")
    
    async def _process_feedback(self, feedback: Dict[str, Any]):
        """Process feedback to update classification models."""
        try:
            text = feedback['text'].lower()
            correct_intent = CommandIntent(feedback['correct_intent'])
            predicted_intent = CommandIntent(feedback['predicted_intent'])
            
            # Update pattern weights
            for pattern in self.intent_patterns.values():
                if pattern.intent == correct_intent:
                    # Boost correct pattern
                    pattern.weight = min(pattern.weight + self.learning_rate, 2.0)
                    
                    # Update accuracy
                    if correct_intent == predicted_intent:
                        pattern.accuracy = min(pattern.accuracy + 0.05, 1.0)
                    else:
                        pattern.accuracy = max(pattern.accuracy - 0.02, 0.1)
                
                elif pattern.intent == predicted_intent and correct_intent != predicted_intent:
                    # Reduce incorrect pattern weight
                    pattern.weight = max(pattern.weight - self.learning_rate, 0.1)
            
            # Extract and update keyword weights
            words = text.split()
            for word in words:
                if len(word) > 2:  # Skip short words
                    self.keyword_weights[word][correct_intent] += self.learning_rate
                    if correct_intent != predicted_intent:
                        self.keyword_weights[word][predicted_intent] -= self.learning_rate * 0.5
            
            feedback['processed'] = True
            
        except Exception as e:
            logger.error(f"Error processing feedback: {e}")
    
    def _cleanup_old_history(self):
        """Clean up old classification history."""
        try:
            max_history = 1000
            if len(self.classification_history) > max_history:
                self.classification_history = self.classification_history[-max_history:]
            
            # Clean up old feedback
            if len(self.feedback_data) > 500:
                self.feedback_data = self.feedback_data[-500:]
                
        except Exception as e:
            logger.error(f"Error cleaning up history: {e}")
    
    async def get_classification_stats(self) -> Dict[str, Any]:
        """Get classification statistics."""
        try:
            stats = {
                'total_classifications': len(self.classification_history),
                'pattern_stats': {},
                'intent_distribution': {},
                'accuracy_metrics': {},
                'recent_performance': {}
            }
            
            # Pattern statistics
            for pattern_id, pattern in self.intent_patterns.items():
                stats['pattern_stats'][pattern_id] = {
                    'frequency': pattern.frequency,
                    'accuracy': pattern.accuracy,
                    'weight': pattern.weight,
                    'last_used': pattern.last_used
                }
            
            # Intent distribution
            if self.classification_history:
                intent_counts = Counter(pred.intent for pred in self.classification_history)
                total = len(self.classification_history)
                stats['intent_distribution'] = {
                    intent.value: count / total for intent, count in intent_counts.items()
                }
            
            # Recent performance (last 100 classifications)
            if len(self.classification_history) >= 10:
                recent = self.classification_history[-100:]
                avg_confidence = sum(pred.confidence for pred in recent) / len(recent)
                stats['recent_performance'] = {
                    'average_confidence': avg_confidence,
                    'high_confidence_rate': sum(1 for pred in recent if pred.confidence > 0.8) / len(recent)
                }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting classification stats: {e}")
            return {}
    
    async def retrain_classifier(self):
        """Retrain classifier based on accumulated feedback."""
        try:
            if not self.feedback_data:
                return
            
            # Process all unprocessed feedback
            unprocessed = [f for f in self.feedback_data if not f.get('processed', False)]
            
            for feedback in unprocessed:
                await self._process_feedback(feedback)
            
            # Normalize pattern weights
            total_weight = sum(pattern.weight for pattern in self.intent_patterns.values())
            if total_weight > 0:
                for pattern in self.intent_patterns.values():
                    pattern.weight = pattern.weight / total_weight * len(self.intent_patterns)
            
            logger.info(f"Retrained classifier with {len(unprocessed)} feedback samples")
            
        except Exception as e:
            logger.error(f"Error retraining classifier: {e}")


# Factory functions
def create_intent_prediction(intent: CommandIntent, confidence: float) -> IntentPrediction:
    """Create a new intent prediction."""
    return IntentPrediction(
        intent=intent,
        confidence=confidence,
        features=[],
        reasoning="Manual creation",
        alternatives=[],
        timestamp=time.time()
    )

def create_intent_pattern(pattern_id: str, intent: CommandIntent, keywords: List[str]) -> IntentPattern:
    """Create a new intent pattern."""
    return IntentPattern(
        pattern_id=pattern_id,
        intent=intent,
        keywords=keywords,
        patterns=[],
        weight=1.0,
        frequency=0,
        accuracy=0.7,
        last_used=time.time()
    )
