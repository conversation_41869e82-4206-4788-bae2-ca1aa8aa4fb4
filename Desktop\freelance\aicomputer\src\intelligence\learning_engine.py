"""
Learning and Adaptation Engine - Phase 2 Component

Learns from user interactions, adapts to preferences, and improves system performance over time.
"""

import asyncio
import json
import time
import pickle
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
from pathlib import Path
import numpy as np

from loguru import logger

from ..utils.config_manager import ConfigManager
from ..core.ai_processor import ProcessedCommand, CommandIntent


@dataclass
class UserPreference:
    """User preference data."""
    category: str
    preference_type: str
    value: Any
    confidence: float
    last_updated: float
    usage_count: int


@dataclass
class CommandPattern:
    """Command usage pattern."""
    pattern_id: str
    commands: List[str]
    frequency: int
    success_rate: float
    avg_execution_time: float
    context: Dict[str, Any]
    last_used: float


@dataclass
class AdaptationRule:
    """System adaptation rule."""
    rule_id: str
    condition: str
    action: str
    parameters: Dict[str, Any]
    confidence: float
    created_time: float
    usage_count: int


class LearningEngine:
    """
    Advanced learning and adaptation engine.
    
    Features:
    - User behavior pattern recognition
    - Preference learning and adaptation
    - Command optimization suggestions
    - Personalized response generation
    - Performance improvement recommendations
    - Adaptive interface customization
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        
        # Learning data storage
        self.user_preferences: Dict[str, UserPreference] = {}
        self.command_patterns: Dict[str, CommandPattern] = {}
        self.adaptation_rules: Dict[str, AdaptationRule] = {}
        
        # Usage tracking
        self.command_history: List[Dict[str, Any]] = []
        self.interaction_sessions: List[Dict[str, Any]] = []
        self.performance_metrics: Dict[str, List[float]] = defaultdict(list)
        
        # Learning parameters
        self.min_pattern_frequency = self.config.get("intelligence.learning.min_pattern_frequency", 3)
        self.confidence_threshold = self.config.get("intelligence.learning.confidence_threshold", 0.7)
        self.adaptation_sensitivity = self.config.get("intelligence.learning.adaptation_sensitivity", 0.8)
        self.max_history_size = self.config.get("intelligence.learning.max_history", 10000)
        
        # Learning state
        self.learning_enabled = self.config.get("intelligence.learning.enabled", True)
        self.current_session_id = None
        self.session_start_time = None
        
        # Load existing learning data
        self._load_learning_data()
        
        logger.info("Learning Engine initialized")
    
    async def process_command_feedback(self, command: ProcessedCommand, 
                                     execution_result: Any, execution_time: float):
        """
        Process feedback from command execution for learning.
        
        Args:
            command: The processed command
            execution_result: Result of command execution
            execution_time: Time taken to execute command
        """
        
        if not self.learning_enabled:
            return
        
        try:
            # Record command interaction
            interaction = {
                "timestamp": time.time(),
                "command_text": command.raw_text,
                "intent": command.intent.value,
                "action": command.action,
                "entities": command.entities,
                "confidence": command.confidence,
                "execution_time": execution_time,
                "success": execution_result.success if hasattr(execution_result, 'success') else True,
                "session_id": self.current_session_id
            }
            
            self.command_history.append(interaction)
            
            # Update patterns
            await self._update_command_patterns(interaction)
            
            # Update preferences
            await self._update_user_preferences(interaction)
            
            # Update performance metrics
            self._update_performance_metrics(interaction)
            
            # Generate adaptation rules
            await self._generate_adaptation_rules(interaction)
            
            # Cleanup old data
            self._cleanup_old_data()
            
        except Exception as e:
            logger.error(f"Error processing command feedback: {e}")
    
    async def _update_command_patterns(self, interaction: Dict[str, Any]):
        """Update command usage patterns."""
        
        try:
            command_text = interaction["command_text"].lower().strip()
            intent = interaction["intent"]
            
            # Create pattern key
            pattern_key = f"{intent}_{hash(command_text) % 10000}"
            
            if pattern_key in self.command_patterns:
                # Update existing pattern
                pattern = self.command_patterns[pattern_key]
                pattern.frequency += 1
                pattern.last_used = interaction["timestamp"]
                
                # Update success rate
                if interaction["success"]:
                    pattern.success_rate = (pattern.success_rate * (pattern.frequency - 1) + 1) / pattern.frequency
                else:
                    pattern.success_rate = (pattern.success_rate * (pattern.frequency - 1)) / pattern.frequency
                
                # Update average execution time
                pattern.avg_execution_time = (
                    (pattern.avg_execution_time * (pattern.frequency - 1) + interaction["execution_time"]) 
                    / pattern.frequency
                )
            else:
                # Create new pattern
                pattern = CommandPattern(
                    pattern_id=pattern_key,
                    commands=[command_text],
                    frequency=1,
                    success_rate=1.0 if interaction["success"] else 0.0,
                    avg_execution_time=interaction["execution_time"],
                    context={"intent": intent, "entities": interaction["entities"]},
                    last_used=interaction["timestamp"]
                )
                self.command_patterns[pattern_key] = pattern
            
        except Exception as e:
            logger.error(f"Error updating command patterns: {e}")
    
    async def _update_user_preferences(self, interaction: Dict[str, Any]):
        """Update user preferences based on interaction."""
        
        try:
            # Application preferences
            if interaction["intent"] == "app_control":
                app_name = interaction["entities"].get("target", "")
                if app_name:
                    pref_key = f"preferred_app_{app_name}"
                    self._update_preference(
                        pref_key, "application", app_name, 
                        0.1, interaction["timestamp"]
                    )
            
            # File location preferences
            elif interaction["intent"] == "file_operation":
                location = interaction["entities"].get("location", "")
                if location and location != "current":
                    pref_key = f"preferred_location_{location}"
                    self._update_preference(
                        pref_key, "file_location", location,
                        0.1, interaction["timestamp"]
                    )
            
            # Command style preferences
            command_length = len(interaction["command_text"].split())
            if command_length <= 3:
                style = "concise"
            elif command_length <= 6:
                style = "normal"
            else:
                style = "detailed"
            
            self._update_preference(
                "command_style", "interaction", style,
                0.05, interaction["timestamp"]
            )
            
            # Time-based preferences
            hour = time.localtime(interaction["timestamp"]).tm_hour
            if 6 <= hour < 12:
                time_period = "morning"
            elif 12 <= hour < 18:
                time_period = "afternoon"
            elif 18 <= hour < 22:
                time_period = "evening"
            else:
                time_period = "night"
            
            self._update_preference(
                f"active_time_{time_period}", "temporal", time_period,
                0.1, interaction["timestamp"]
            )
            
        except Exception as e:
            logger.error(f"Error updating user preferences: {e}")
    
    def _update_preference(self, key: str, category: str, value: Any, 
                          weight: float, timestamp: float):
        """Update a specific user preference."""
        
        if key in self.user_preferences:
            pref = self.user_preferences[key]
            pref.confidence = min(pref.confidence + weight, 1.0)
            pref.last_updated = timestamp
            pref.usage_count += 1
        else:
            pref = UserPreference(
                category=category,
                preference_type=key,
                value=value,
                confidence=weight,
                last_updated=timestamp,
                usage_count=1
            )
            self.user_preferences[key] = pref
    
    def _update_performance_metrics(self, interaction: Dict[str, Any]):
        """Update system performance metrics."""
        
        try:
            # Execution time metrics
            self.performance_metrics["execution_time"].append(interaction["execution_time"])
            
            # Success rate metrics
            self.performance_metrics["success_rate"].append(1.0 if interaction["success"] else 0.0)
            
            # Confidence metrics
            self.performance_metrics["confidence"].append(interaction["confidence"])
            
            # Intent distribution
            intent_key = f"intent_{interaction['intent']}"
            if intent_key not in self.performance_metrics:
                self.performance_metrics[intent_key] = []
            self.performance_metrics[intent_key].append(1.0)
            
            # Keep only recent metrics
            for key, values in self.performance_metrics.items():
                if len(values) > 1000:
                    self.performance_metrics[key] = values[-500:]
                    
        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")
    
    async def _generate_adaptation_rules(self, interaction: Dict[str, Any]):
        """Generate adaptation rules based on patterns."""
        
        try:
            # Rule: Suggest shortcuts for frequent commands
            command_text = interaction["command_text"]
            if len(command_text.split()) > 5:  # Long command
                pattern_key = f"{interaction['intent']}_{hash(command_text) % 10000}"
                if pattern_key in self.command_patterns:
                    pattern = self.command_patterns[pattern_key]
                    if pattern.frequency >= self.min_pattern_frequency:
                        rule_id = f"shortcut_{pattern_key}"
                        if rule_id not in self.adaptation_rules:
                            rule = AdaptationRule(
                                rule_id=rule_id,
                                condition=f"command_frequency >= {self.min_pattern_frequency}",
                                action="suggest_shortcut",
                                parameters={"original_command": command_text, "frequency": pattern.frequency},
                                confidence=min(pattern.frequency / 10.0, 1.0),
                                created_time=time.time(),
                                usage_count=0
                            )
                            self.adaptation_rules[rule_id] = rule
            
            # Rule: Suggest alternative applications
            if interaction["intent"] == "app_control" and not interaction["success"]:
                app_name = interaction["entities"].get("target", "")
                if app_name:
                    rule_id = f"alt_app_{app_name}"
                    if rule_id not in self.adaptation_rules:
                        rule = AdaptationRule(
                            rule_id=rule_id,
                            condition=f"app_failure_{app_name}",
                            action="suggest_alternative_app",
                            parameters={"failed_app": app_name},
                            confidence=0.6,
                            created_time=time.time(),
                            usage_count=0
                        )
                        self.adaptation_rules[rule_id] = rule
            
            # Rule: Optimize slow commands
            if interaction["execution_time"] > 5.0:  # Slow command
                rule_id = f"optimize_{interaction['intent']}"
                if rule_id not in self.adaptation_rules:
                    rule = AdaptationRule(
                        rule_id=rule_id,
                        condition="execution_time > 5.0",
                        action="suggest_optimization",
                        parameters={"intent": interaction["intent"], "avg_time": interaction["execution_time"]},
                        confidence=0.7,
                        created_time=time.time(),
                        usage_count=0
                    )
                    self.adaptation_rules[rule_id] = rule
                    
        except Exception as e:
            logger.error(f"Error generating adaptation rules: {e}")
    
    def get_personalized_suggestions(self, current_command: str = None) -> List[Dict[str, Any]]:
        """Get personalized suggestions based on learned patterns."""
        
        suggestions = []
        
        try:
            # Suggest frequent commands
            frequent_patterns = sorted(
                self.command_patterns.values(),
                key=lambda p: p.frequency,
                reverse=True
            )[:5]
            
            for pattern in frequent_patterns:
                if pattern.frequency >= self.min_pattern_frequency:
                    suggestions.append({
                        "type": "frequent_command",
                        "suggestion": pattern.commands[0],
                        "reason": f"You use this command frequently ({pattern.frequency} times)",
                        "confidence": min(pattern.frequency / 10.0, 1.0)
                    })
            
            # Suggest based on time patterns
            current_hour = time.localtime().tm_hour
            if 6 <= current_hour < 12:
                time_period = "morning"
            elif 12 <= current_hour < 18:
                time_period = "afternoon"
            elif 18 <= current_hour < 22:
                time_period = "evening"
            else:
                time_period = "night"
            
            time_pref_key = f"active_time_{time_period}"
            if time_pref_key in self.user_preferences:
                suggestions.append({
                    "type": "time_based",
                    "suggestion": f"You're usually active during {time_period}",
                    "reason": "Based on your usage patterns",
                    "confidence": self.user_preferences[time_pref_key].confidence
                })
            
            # Suggest improvements based on adaptation rules
            for rule in self.adaptation_rules.values():
                if rule.confidence >= self.confidence_threshold:
                    if rule.action == "suggest_shortcut":
                        suggestions.append({
                            "type": "shortcut",
                            "suggestion": f"Create shortcut for: {rule.parameters['original_command']}",
                            "reason": f"Used {rule.parameters['frequency']} times",
                            "confidence": rule.confidence
                        })
                    elif rule.action == "suggest_optimization":
                        suggestions.append({
                            "type": "optimization",
                            "suggestion": f"Optimize {rule.parameters['intent']} commands",
                            "reason": f"Average execution time: {rule.parameters['avg_time']:.1f}s",
                            "confidence": rule.confidence
                        })
            
            # Sort by confidence and return top suggestions
            suggestions.sort(key=lambda s: s["confidence"], reverse=True)
            return suggestions[:10]
            
        except Exception as e:
            logger.error(f"Error generating suggestions: {e}")
            return []
    
    def get_user_profile(self) -> Dict[str, Any]:
        """Get comprehensive user profile based on learned data."""
        
        try:
            # Most used intents
            intent_counts = Counter()
            for interaction in self.command_history[-1000:]:  # Recent interactions
                intent_counts[interaction["intent"]] += 1
            
            # Preferred applications
            app_prefs = {k: v for k, v in self.user_preferences.items() 
                        if v.category == "application"}
            
            # Command style analysis
            command_lengths = [len(interaction["command_text"].split()) 
                             for interaction in self.command_history[-100:]]
            avg_command_length = np.mean(command_lengths) if command_lengths else 0
            
            # Activity patterns
            activity_hours = [time.localtime(interaction["timestamp"]).tm_hour 
                            for interaction in self.command_history[-500:]]
            peak_hours = Counter(activity_hours).most_common(3)
            
            # Success rates by intent
            success_by_intent = defaultdict(list)
            for interaction in self.command_history[-1000:]:
                success_by_intent[interaction["intent"]].append(interaction["success"])
            
            intent_success_rates = {
                intent: np.mean(successes) 
                for intent, successes in success_by_intent.items()
            }
            
            return {
                "total_interactions": len(self.command_history),
                "most_used_intents": dict(intent_counts.most_common(5)),
                "preferred_applications": {k: v.value for k, v in app_prefs.items()},
                "average_command_length": avg_command_length,
                "peak_activity_hours": [hour for hour, count in peak_hours],
                "success_rates_by_intent": intent_success_rates,
                "learning_confidence": self._calculate_overall_confidence(),
                "adaptation_rules_count": len(self.adaptation_rules),
                "last_interaction": max([i["timestamp"] for i in self.command_history]) if self.command_history else None
            }
            
        except Exception as e:
            logger.error(f"Error generating user profile: {e}")
            return {"error": str(e)}
    
    def _calculate_overall_confidence(self) -> float:
        """Calculate overall learning confidence."""
        
        if not self.user_preferences:
            return 0.0
        
        confidences = [pref.confidence for pref in self.user_preferences.values()]
        return np.mean(confidences)
    
    def start_session(self) -> str:
        """Start a new learning session."""
        
        session_id = f"session_{int(time.time())}"
        self.current_session_id = session_id
        self.session_start_time = time.time()
        
        logger.info(f"Started learning session: {session_id}")
        return session_id
    
    def end_session(self):
        """End current learning session."""
        
        if self.current_session_id and self.session_start_time:
            session_data = {
                "session_id": self.current_session_id,
                "start_time": self.session_start_time,
                "end_time": time.time(),
                "interactions_count": len([i for i in self.command_history 
                                         if i.get("session_id") == self.current_session_id])
            }
            
            self.interaction_sessions.append(session_data)
            
            logger.info(f"Ended learning session: {self.current_session_id}")
            
            # Save learning data
            self._save_learning_data()
        
        self.current_session_id = None
        self.session_start_time = None
    
    def _cleanup_old_data(self):
        """Clean up old learning data to manage memory."""
        
        # Keep only recent command history
        if len(self.command_history) > self.max_history_size:
            self.command_history = self.command_history[-self.max_history_size//2:]
        
        # Remove old, unused patterns
        current_time = time.time()
        old_patterns = [
            pattern_id for pattern_id, pattern in self.command_patterns.items()
            if current_time - pattern.last_used > 86400 * 30  # 30 days
            and pattern.frequency < 3
        ]
        
        for pattern_id in old_patterns:
            del self.command_patterns[pattern_id]
        
        # Remove low-confidence preferences
        low_conf_prefs = [
            pref_key for pref_key, pref in self.user_preferences.items()
            if pref.confidence < 0.1 and current_time - pref.last_updated > 86400 * 7  # 7 days
        ]
        
        for pref_key in low_conf_prefs:
            del self.user_preferences[pref_key]
    
    def _save_learning_data(self):
        """Save learning data to disk."""
        
        try:
            data_dir = Path("data/learning")
            data_dir.mkdir(parents=True, exist_ok=True)
            
            # Save preferences
            with open(data_dir / "preferences.json", "w") as f:
                prefs_data = {k: asdict(v) for k, v in self.user_preferences.items()}
                json.dump(prefs_data, f, indent=2)
            
            # Save patterns (using pickle for complex data)
            with open(data_dir / "patterns.pkl", "wb") as f:
                pickle.dump(self.command_patterns, f)
            
            # Save adaptation rules
            with open(data_dir / "adaptation_rules.json", "w") as f:
                rules_data = {k: asdict(v) for k, v in self.adaptation_rules.items()}
                json.dump(rules_data, f, indent=2)
            
            logger.debug("Learning data saved successfully")
            
        except Exception as e:
            logger.error(f"Error saving learning data: {e}")
    
    def _load_learning_data(self):
        """Load learning data from disk."""
        
        try:
            data_dir = Path("data/learning")
            
            # Load preferences
            prefs_file = data_dir / "preferences.json"
            if prefs_file.exists():
                with open(prefs_file, "r") as f:
                    prefs_data = json.load(f)
                    self.user_preferences = {
                        k: UserPreference(**v) for k, v in prefs_data.items()
                    }
            
            # Load patterns
            patterns_file = data_dir / "patterns.pkl"
            if patterns_file.exists():
                with open(patterns_file, "rb") as f:
                    self.command_patterns = pickle.load(f)
            
            # Load adaptation rules
            rules_file = data_dir / "adaptation_rules.json"
            if rules_file.exists():
                with open(rules_file, "r") as f:
                    rules_data = json.load(f)
                    self.adaptation_rules = {
                        k: AdaptationRule(**v) for k, v in rules_data.items()
                    }
            
            logger.info("Learning data loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading learning data: {e}")
    
    def reset_learning_data(self):
        """Reset all learning data (for testing or privacy)."""
        
        self.user_preferences.clear()
        self.command_patterns.clear()
        self.adaptation_rules.clear()
        self.command_history.clear()
        self.interaction_sessions.clear()
        self.performance_metrics.clear()
        
        # Remove saved files
        try:
            data_dir = Path("data/learning")
            for file in data_dir.glob("*"):
                file.unlink()
        except Exception as e:
            logger.error(f"Error removing learning data files: {e}")
        
        logger.info("Learning data reset successfully")
