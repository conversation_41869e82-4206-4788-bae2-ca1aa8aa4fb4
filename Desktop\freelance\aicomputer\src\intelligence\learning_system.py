"""
Learning System - Phase 2 Component

Advanced machine learning system for continuous improvement and adaptation.
"""

import asyncio
import time
import json
import pickle
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from datetime import datetime, timedelta
import numpy as np
from pathlib import Path

from loguru import logger

from ..utils.config_manager import ConfigManager
from ..core.ai_processor import ProcessedCommand, CommandIntent


@dataclass
class LearningPattern:
    """Learning pattern data structure."""
    pattern_id: str
    pattern_type: str
    input_features: List[float]
    output_target: Any
    confidence: float
    frequency: int
    last_updated: float
    metadata: Dict[str, Any]


@dataclass
class ModelMetrics:
    """Model performance metrics."""
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    training_samples: int
    last_trained: float


class LearningSystem:
    """
    Advanced learning system for continuous improvement.
    
    Features:
    - Online learning from user interactions
    - Pattern recognition and adaptation
    - Performance optimization
    - Personalization learning
    - Feedback incorporation
    - Model performance tracking
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        
        # Learning data storage
        self.learning_patterns: Dict[str, LearningPattern] = {}
        self.training_data: List[Dict[str, Any]] = []
        self.feedback_data: List[Dict[str, Any]] = []
        
        # Models and metrics
        self.models: Dict[str, Any] = {}
        self.model_metrics: Dict[str, ModelMetrics] = {}
        
        # Learning configuration
        self.learning_rate = self.config.get("intelligence.learning.rate", 0.01)
        self.batch_size = self.config.get("intelligence.learning.batch_size", 32)
        self.max_patterns = self.config.get("intelligence.learning.max_patterns", 10000)
        self.adaptation_threshold = self.config.get("intelligence.learning.adaptation_threshold", 0.8)
        
        # Initialize learning models
        self._initialize_models()
        
        logger.info("Learning System initialized")
    
    def _initialize_models(self):
        """Initialize learning models."""
        try:
            # Simple learning models for demonstration
            self.models = {
                'command_success': {'weights': np.random.random(10), 'bias': 0.0},
                'user_preference': {'weights': np.random.random(15), 'bias': 0.0},
                'response_quality': {'weights': np.random.random(8), 'bias': 0.0},
                'efficiency': {'weights': np.random.random(12), 'bias': 0.0}
            }
            
            # Initialize metrics
            for model_name in self.models.keys():
                self.model_metrics[model_name] = ModelMetrics(
                    accuracy=0.5,
                    precision=0.5,
                    recall=0.5,
                    f1_score=0.5,
                    training_samples=0,
                    last_trained=time.time()
                )
            
            logger.info("Learning models initialized")
            
        except Exception as e:
            logger.error(f"Error initializing learning models: {e}")
    
    async def learn_from_interaction(self, command: ProcessedCommand, 
                                   context: Dict[str, Any], 
                                   outcome: Dict[str, Any]):
        """Learn from user interaction."""
        try:
            # Extract features from interaction
            features = self._extract_interaction_features(command, context, outcome)
            
            # Create learning pattern
            pattern = LearningPattern(
                pattern_id=f"interaction_{int(time.time())}_{hash(str(features)) % 10000}",
                pattern_type="interaction",
                input_features=features,
                output_target=outcome.get('success', False),
                confidence=outcome.get('confidence', 0.5),
                frequency=1,
                last_updated=time.time(),
                metadata={'command': command.text, 'context': context}
            )
            
            # Store pattern
            self.learning_patterns[pattern.pattern_id] = pattern
            
            # Add to training data
            training_sample = {
                'features': features,
                'target': outcome.get('success', False),
                'timestamp': time.time(),
                'command_type': command.intent.value
            }
            self.training_data.append(training_sample)
            
            # Trigger online learning
            await self._update_models_online(pattern)
            
            # Cleanup old patterns
            self._cleanup_old_patterns()
            
            logger.debug(f"Learned from interaction: {command.text}")
            
        except Exception as e:
            logger.error(f"Error learning from interaction: {e}")
    
    async def learn_from_feedback(self, feedback: Dict[str, Any]):
        """Learn from user feedback."""
        try:
            # Store feedback
            feedback_entry = {
                'feedback': feedback,
                'timestamp': time.time(),
                'processed': False
            }
            self.feedback_data.append(feedback_entry)
            
            # Process feedback for learning
            await self._process_feedback(feedback_entry)
            
            logger.debug(f"Learned from feedback: {feedback.get('type', 'unknown')}")
            
        except Exception as e:
            logger.error(f"Error learning from feedback: {e}")
    
    def _extract_interaction_features(self, command: ProcessedCommand, 
                                    context: Dict[str, Any], 
                                    outcome: Dict[str, Any]) -> List[float]:
        """Extract numerical features from interaction."""
        features = []
        
        # Command features
        features.append(len(command.text) / 100.0)  # Normalized command length
        features.append(command.confidence)  # AI confidence
        features.append(len(command.entities) / 10.0)  # Normalized entity count
        
        # Context features
        features.append(context.get('session_length', 0) / 3600.0)  # Session length (hours)
        features.append(context.get('commands_count', 0) / 100.0)  # Commands in session
        features.append(context.get('error_rate', 0))  # Current error rate
        
        # Time features
        current_hour = datetime.now().hour
        features.append(current_hour / 24.0)  # Normalized hour
        features.append(datetime.now().weekday() / 7.0)  # Normalized day of week
        
        # System features
        features.append(context.get('cpu_usage', 0) / 100.0)  # CPU usage
        features.append(context.get('memory_usage', 0) / 100.0)  # Memory usage
        
        return features
    
    async def _update_models_online(self, pattern: LearningPattern):
        """Update models with online learning."""
        try:
            # Simple gradient descent update for demonstration
            for model_name, model in self.models.items():
                if len(pattern.input_features) == len(model['weights']):
                    # Calculate prediction
                    prediction = np.dot(pattern.input_features, model['weights']) + model['bias']
                    
                    # Calculate error
                    target = 1.0 if pattern.output_target else 0.0
                    error = target - prediction
                    
                    # Update weights
                    model['weights'] += self.learning_rate * error * np.array(pattern.input_features)
                    model['bias'] += self.learning_rate * error
                    
                    # Update metrics
                    metrics = self.model_metrics[model_name]
                    metrics.training_samples += 1
                    metrics.last_trained = time.time()
                    
                    # Simple accuracy update
                    if abs(error) < 0.5:
                        metrics.accuracy = min(metrics.accuracy + 0.01, 1.0)
                    else:
                        metrics.accuracy = max(metrics.accuracy - 0.005, 0.0)
            
        except Exception as e:
            logger.error(f"Error in online model update: {e}")
    
    async def _process_feedback(self, feedback_entry: Dict[str, Any]):
        """Process user feedback for learning."""
        try:
            feedback = feedback_entry['feedback']
            
            # Extract learning signals from feedback
            if feedback.get('type') == 'command_success':
                # Learn from command success/failure
                success_rate = feedback.get('success_rate', 0.5)
                await self._update_success_model(feedback, success_rate)
                
            elif feedback.get('type') == 'user_satisfaction':
                # Learn from user satisfaction
                satisfaction = feedback.get('rating', 0.5)
                await self._update_preference_model(feedback, satisfaction)
                
            elif feedback.get('type') == 'response_quality':
                # Learn from response quality
                quality = feedback.get('quality_score', 0.5)
                await self._update_quality_model(feedback, quality)
            
            feedback_entry['processed'] = True
            
        except Exception as e:
            logger.error(f"Error processing feedback: {e}")
    
    async def _update_success_model(self, feedback: Dict[str, Any], success_rate: float):
        """Update command success prediction model."""
        try:
            model = self.models.get('command_success')
            if model:
                # Simple model update based on feedback
                adjustment = (success_rate - 0.5) * 0.1
                model['bias'] += adjustment
                
                # Update metrics
                metrics = self.model_metrics['command_success']
                metrics.accuracy = (metrics.accuracy + success_rate) / 2
                
        except Exception as e:
            logger.error(f"Error updating success model: {e}")
    
    async def _update_preference_model(self, feedback: Dict[str, Any], satisfaction: float):
        """Update user preference model."""
        try:
            model = self.models.get('user_preference')
            if model:
                # Update based on user satisfaction
                adjustment = (satisfaction - 0.5) * 0.05
                model['bias'] += adjustment
                
                # Update metrics
                metrics = self.model_metrics['user_preference']
                metrics.accuracy = (metrics.accuracy + satisfaction) / 2
                
        except Exception as e:
            logger.error(f"Error updating preference model: {e}")
    
    async def _update_quality_model(self, feedback: Dict[str, Any], quality: float):
        """Update response quality model."""
        try:
            model = self.models.get('response_quality')
            if model:
                # Update based on quality feedback
                adjustment = (quality - 0.5) * 0.08
                model['bias'] += adjustment
                
                # Update metrics
                metrics = self.model_metrics['response_quality']
                metrics.accuracy = (metrics.accuracy + quality) / 2
                
        except Exception as e:
            logger.error(f"Error updating quality model: {e}")
    
    def _cleanup_old_patterns(self):
        """Remove old learning patterns to manage memory."""
        try:
            if len(self.learning_patterns) > self.max_patterns:
                # Sort by last_updated and remove oldest
                sorted_patterns = sorted(
                    self.learning_patterns.items(),
                    key=lambda x: x[1].last_updated
                )
                
                # Remove oldest 20%
                remove_count = len(sorted_patterns) // 5
                for i in range(remove_count):
                    pattern_id = sorted_patterns[i][0]
                    del self.learning_patterns[pattern_id]
                
                logger.debug(f"Cleaned up {remove_count} old learning patterns")
            
            # Cleanup old training data
            if len(self.training_data) > self.max_patterns:
                self.training_data = self.training_data[-self.max_patterns:]
            
            # Cleanup old feedback data
            if len(self.feedback_data) > 1000:
                self.feedback_data = self.feedback_data[-1000:]
                
        except Exception as e:
            logger.error(f"Error cleaning up old patterns: {e}")
    
    async def predict_command_success(self, command: ProcessedCommand, 
                                    context: Dict[str, Any]) -> float:
        """Predict likelihood of command success."""
        try:
            features = self._extract_interaction_features(command, context, {})
            model = self.models.get('command_success')
            
            if model and len(features) == len(model['weights']):
                prediction = np.dot(features, model['weights']) + model['bias']
                return max(0.0, min(1.0, prediction))  # Clamp to [0, 1]
            
            return 0.7  # Default prediction
            
        except Exception as e:
            logger.error(f"Error predicting command success: {e}")
            return 0.5
    
    async def get_learning_insights(self) -> Dict[str, Any]:
        """Get insights from learning system."""
        try:
            insights = {
                'total_patterns': len(self.learning_patterns),
                'training_samples': len(self.training_data),
                'feedback_samples': len(self.feedback_data),
                'model_metrics': {},
                'learning_trends': {},
                'adaptation_status': {}
            }
            
            # Add model metrics
            for model_name, metrics in self.model_metrics.items():
                insights['model_metrics'][model_name] = asdict(metrics)
            
            # Calculate learning trends
            if self.training_data:
                recent_data = self.training_data[-100:]  # Last 100 samples
                success_rate = sum(1 for d in recent_data if d.get('target', False)) / len(recent_data)
                insights['learning_trends']['recent_success_rate'] = success_rate
            
            # Adaptation status
            for model_name, metrics in self.model_metrics.items():
                insights['adaptation_status'][model_name] = {
                    'adapting': metrics.accuracy > self.adaptation_threshold,
                    'performance': metrics.accuracy
                }
            
            return insights
            
        except Exception as e:
            logger.error(f"Error getting learning insights: {e}")
            return {}
    
    async def save_learning_state(self, filepath: str):
        """Save learning state to file."""
        try:
            state = {
                'learning_patterns': {k: asdict(v) for k, v in self.learning_patterns.items()},
                'models': self.models,
                'model_metrics': {k: asdict(v) for k, v in self.model_metrics.items()},
                'training_data': self.training_data[-1000:],  # Save recent data
                'timestamp': time.time()
            }
            
            with open(filepath, 'wb') as f:
                pickle.dump(state, f)
            
            logger.info(f"Learning state saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving learning state: {e}")
    
    async def load_learning_state(self, filepath: str):
        """Load learning state from file."""
        try:
            if Path(filepath).exists():
                with open(filepath, 'rb') as f:
                    state = pickle.load(f)
                
                # Restore learning patterns
                self.learning_patterns = {
                    k: LearningPattern(**v) for k, v in state.get('learning_patterns', {}).items()
                }
                
                # Restore models
                self.models = state.get('models', {})
                
                # Restore metrics
                self.model_metrics = {
                    k: ModelMetrics(**v) for k, v in state.get('model_metrics', {}).items()
                }
                
                # Restore training data
                self.training_data = state.get('training_data', [])
                
                logger.info(f"Learning state loaded from {filepath}")
            
        except Exception as e:
            logger.error(f"Error loading learning state: {e}")


# Factory functions
def create_learning_pattern(pattern_id: str, pattern_type: str, features: List[float]) -> LearningPattern:
    """Create a new learning pattern."""
    return LearningPattern(
        pattern_id=pattern_id,
        pattern_type=pattern_type,
        input_features=features,
        output_target=None,
        confidence=0.5,
        frequency=1,
        last_updated=time.time(),
        metadata={}
    )
