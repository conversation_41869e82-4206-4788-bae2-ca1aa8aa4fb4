"""
Predictive Intelligence - Phase 2 Component

Advanced predictive intelligence for anticipating user needs and system optimization.
"""

import asyncio
import time
import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from datetime import datetime, timedelta
import numpy as np

from loguru import logger

from ..utils.config_manager import ConfigManager
from ..core.ai_processor import ProcessedCommand, CommandIntent


@dataclass
class PredictiveInsight:
    """Predictive insight data structure."""
    insight_id: str
    insight_type: str
    prediction: str
    confidence: float
    time_horizon: float
    context: Dict[str, Any]
    created_at: float
    expires_at: float


@dataclass
class TrendAnalysis:
    """Trend analysis result."""
    trend_id: str
    trend_type: str
    direction: str  # 'increasing', 'decreasing', 'stable'
    strength: float
    data_points: List[float]
    time_range: Tuple[float, float]
    metadata: Dict[str, Any]


class PredictiveIntelligence:
    """
    Advanced predictive intelligence system.
    
    Features:
    - User behavior prediction
    - System performance forecasting
    - Resource usage prediction
    - Trend analysis and detection
    - Anomaly prediction
    - Optimization recommendations
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        
        # Prediction data
        self.insights: Dict[str, PredictiveInsight] = {}
        self.trends: Dict[str, TrendAnalysis] = {}
        self.historical_data: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        
        # Prediction models
        self.prediction_models: Dict[str, Any] = {}
        
        # Configuration
        self.prediction_window = self.config.get("intelligence.prediction.window_hours", 24)
        self.confidence_threshold = self.config.get("intelligence.prediction.confidence_threshold", 0.7)
        self.max_insights = self.config.get("intelligence.prediction.max_insights", 50)
        
        # Initialize prediction models
        self._initialize_prediction_models()
        
        logger.info("Predictive Intelligence initialized")
    
    def _initialize_prediction_models(self):
        """Initialize predictive models."""
        try:
            # Simple predictive models for demonstration
            self.prediction_models = {
                'user_behavior': {
                    'type': 'time_series',
                    'parameters': {'window_size': 10, 'trend_weight': 0.7},
                    'last_updated': time.time()
                },
                'system_performance': {
                    'type': 'regression',
                    'parameters': {'coefficients': [0.8, 0.6, 0.4], 'intercept': 0.2},
                    'last_updated': time.time()
                },
                'resource_usage': {
                    'type': 'exponential_smoothing',
                    'parameters': {'alpha': 0.3, 'beta': 0.2, 'gamma': 0.1},
                    'last_updated': time.time()
                }
            }
            
            logger.info("Predictive models initialized")
            
        except Exception as e:
            logger.error(f"Error initializing predictive models: {e}")
    
    async def analyze_and_predict(self, context: Dict[str, Any]) -> List[PredictiveInsight]:
        """Analyze current context and generate predictions."""
        try:
            insights = []
            
            # Update historical data
            await self._update_historical_data(context)
            
            # Generate behavior predictions
            behavior_insights = await self._predict_user_behavior(context)
            insights.extend(behavior_insights)
            
            # Generate performance predictions
            performance_insights = await self._predict_system_performance(context)
            insights.extend(performance_insights)
            
            # Generate resource predictions
            resource_insights = await self._predict_resource_usage(context)
            insights.extend(resource_insights)
            
            # Generate trend-based predictions
            trend_insights = await self._predict_from_trends(context)
            insights.extend(trend_insights)
            
            # Filter and store insights
            filtered_insights = self._filter_insights(insights)
            for insight in filtered_insights:
                self.insights[insight.insight_id] = insight
            
            # Cleanup expired insights
            self._cleanup_expired_insights()
            
            return filtered_insights
            
        except Exception as e:
            logger.error(f"Error in predictive analysis: {e}")
            return []
    
    async def _update_historical_data(self, context: Dict[str, Any]):
        """Update historical data with current context."""
        try:
            current_time = time.time()
            
            # Store various metrics
            metrics = {
                'cpu_usage': context.get('cpu_usage', 0),
                'memory_usage': context.get('memory_usage', 0),
                'disk_usage': context.get('disk_usage', 0),
                'network_activity': context.get('network_activity', 0),
                'command_frequency': context.get('command_frequency', 0),
                'error_rate': context.get('error_rate', 0),
                'response_time': context.get('response_time', 1.0),
                'user_activity': context.get('user_activity', 0.5)
            }
            
            for metric_name, value in metrics.items():
                self.historical_data[metric_name].append({
                    'value': value,
                    'timestamp': current_time
                })
            
        except Exception as e:
            logger.error(f"Error updating historical data: {e}")
    
    async def _predict_user_behavior(self, context: Dict[str, Any]) -> List[PredictiveInsight]:
        """Predict user behavior patterns."""
        insights = []
        
        try:
            current_time = time.time()
            current_hour = datetime.now().hour
            
            # Analyze command frequency patterns
            if 'command_frequency' in self.historical_data:
                freq_data = list(self.historical_data['command_frequency'])
                if len(freq_data) >= 5:
                    recent_freq = [d['value'] for d in freq_data[-5:]]
                    avg_freq = sum(recent_freq) / len(recent_freq)
                    
                    # Predict next hour activity
                    if avg_freq > 0.7:
                        insight = PredictiveInsight(
                            insight_id=f"behavior_high_activity_{int(current_time)}",
                            insight_type="user_behavior",
                            prediction="High user activity expected in next hour",
                            confidence=0.8,
                            time_horizon=3600,  # 1 hour
                            context={"avg_frequency": avg_freq, "hour": current_hour},
                            created_at=current_time,
                            expires_at=current_time + 3600
                        )
                        insights.append(insight)
                    elif avg_freq < 0.3:
                        insight = PredictiveInsight(
                            insight_id=f"behavior_low_activity_{int(current_time)}",
                            insight_type="user_behavior",
                            prediction="Low user activity expected, system can optimize resources",
                            confidence=0.75,
                            time_horizon=3600,
                            context={"avg_frequency": avg_freq, "hour": current_hour},
                            created_at=current_time,
                            expires_at=current_time + 3600
                        )
                        insights.append(insight)
            
            # Predict based on time patterns
            if 9 <= current_hour <= 17:  # Work hours
                insight = PredictiveInsight(
                    insight_id=f"behavior_work_hours_{int(current_time)}",
                    insight_type="user_behavior",
                    prediction="Work-related commands likely during business hours",
                    confidence=0.7,
                    time_horizon=7200,  # 2 hours
                    context={"hour": current_hour, "period": "work_hours"},
                    created_at=current_time,
                    expires_at=current_time + 7200
                )
                insights.append(insight)
            
        except Exception as e:
            logger.error(f"Error predicting user behavior: {e}")
        
        return insights
    
    async def _predict_system_performance(self, context: Dict[str, Any]) -> List[PredictiveInsight]:
        """Predict system performance trends."""
        insights = []
        
        try:
            current_time = time.time()
            
            # Analyze CPU usage trends
            if 'cpu_usage' in self.historical_data:
                cpu_data = list(self.historical_data['cpu_usage'])
                if len(cpu_data) >= 10:
                    recent_cpu = [d['value'] for d in cpu_data[-10:]]
                    cpu_trend = self._calculate_trend(recent_cpu)
                    
                    if cpu_trend > 0.1 and recent_cpu[-1] > 70:
                        insight = PredictiveInsight(
                            insight_id=f"performance_cpu_high_{int(current_time)}",
                            insight_type="system_performance",
                            prediction="CPU usage trending high, performance degradation likely",
                            confidence=0.85,
                            time_horizon=1800,  # 30 minutes
                            context={"cpu_trend": cpu_trend, "current_cpu": recent_cpu[-1]},
                            created_at=current_time,
                            expires_at=current_time + 1800
                        )
                        insights.append(insight)
            
            # Analyze memory usage trends
            if 'memory_usage' in self.historical_data:
                mem_data = list(self.historical_data['memory_usage'])
                if len(mem_data) >= 10:
                    recent_mem = [d['value'] for d in mem_data[-10:]]
                    mem_trend = self._calculate_trend(recent_mem)
                    
                    if mem_trend > 0.15 and recent_mem[-1] > 80:
                        insight = PredictiveInsight(
                            insight_id=f"performance_memory_high_{int(current_time)}",
                            insight_type="system_performance",
                            prediction="Memory usage increasing rapidly, system slowdown expected",
                            confidence=0.8,
                            time_horizon=2400,  # 40 minutes
                            context={"memory_trend": mem_trend, "current_memory": recent_mem[-1]},
                            created_at=current_time,
                            expires_at=current_time + 2400
                        )
                        insights.append(insight)
            
        except Exception as e:
            logger.error(f"Error predicting system performance: {e}")
        
        return insights
    
    async def _predict_resource_usage(self, context: Dict[str, Any]) -> List[PredictiveInsight]:
        """Predict resource usage patterns."""
        insights = []
        
        try:
            current_time = time.time()
            
            # Predict disk usage
            if 'disk_usage' in self.historical_data:
                disk_data = list(self.historical_data['disk_usage'])
                if len(disk_data) >= 5:
                    recent_disk = [d['value'] for d in disk_data[-5:]]
                    disk_trend = self._calculate_trend(recent_disk)
                    
                    if disk_trend > 0.05 and recent_disk[-1] > 85:
                        insight = PredictiveInsight(
                            insight_id=f"resource_disk_full_{int(current_time)}",
                            insight_type="resource_usage",
                            prediction="Disk space running low, cleanup recommended",
                            confidence=0.9,
                            time_horizon=86400,  # 24 hours
                            context={"disk_trend": disk_trend, "current_disk": recent_disk[-1]},
                            created_at=current_time,
                            expires_at=current_time + 86400
                        )
                        insights.append(insight)
            
            # Predict network activity
            if 'network_activity' in self.historical_data:
                net_data = list(self.historical_data['network_activity'])
                if len(net_data) >= 8:
                    recent_net = [d['value'] for d in net_data[-8:]]
                    net_avg = sum(recent_net) / len(recent_net)
                    
                    if net_avg > 0.8:
                        insight = PredictiveInsight(
                            insight_id=f"resource_network_high_{int(current_time)}",
                            insight_type="resource_usage",
                            prediction="High network activity detected, bandwidth optimization needed",
                            confidence=0.75,
                            time_horizon=3600,  # 1 hour
                            context={"network_avg": net_avg, "activity_level": "high"},
                            created_at=current_time,
                            expires_at=current_time + 3600
                        )
                        insights.append(insight)
            
        except Exception as e:
            logger.error(f"Error predicting resource usage: {e}")
        
        return insights
    
    async def _predict_from_trends(self, context: Dict[str, Any]) -> List[PredictiveInsight]:
        """Generate predictions based on detected trends."""
        insights = []
        
        try:
            current_time = time.time()
            
            # Analyze trends in all metrics
            for metric_name, data in self.historical_data.items():
                if len(data) >= 15:
                    values = [d['value'] for d in list(data)[-15:]]
                    trend = self._calculate_trend(values)
                    
                    # Generate trend-based insights
                    if abs(trend) > 0.1:  # Significant trend
                        direction = "increasing" if trend > 0 else "decreasing"
                        
                        insight = PredictiveInsight(
                            insight_id=f"trend_{metric_name}_{direction}_{int(current_time)}",
                            insight_type="trend_analysis",
                            prediction=f"{metric_name.replace('_', ' ').title()} is {direction} significantly",
                            confidence=min(0.9, 0.5 + abs(trend)),
                            time_horizon=7200,  # 2 hours
                            context={"metric": metric_name, "trend": trend, "direction": direction},
                            created_at=current_time,
                            expires_at=current_time + 7200
                        )
                        insights.append(insight)
            
        except Exception as e:
            logger.error(f"Error predicting from trends: {e}")
        
        return insights
    
    def _calculate_trend(self, values: List[float]) -> float:
        """Calculate trend direction and strength."""
        try:
            if len(values) < 2:
                return 0.0
            
            # Simple linear regression slope
            n = len(values)
            x = list(range(n))
            
            sum_x = sum(x)
            sum_y = sum(values)
            sum_xy = sum(x[i] * values[i] for i in range(n))
            sum_x2 = sum(xi * xi for xi in x)
            
            # Calculate slope
            slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
            
            # Normalize by value range
            value_range = max(values) - min(values)
            if value_range > 0:
                slope = slope / value_range
            
            return slope
            
        except Exception as e:
            logger.error(f"Error calculating trend: {e}")
            return 0.0
    
    def _filter_insights(self, insights: List[PredictiveInsight]) -> List[PredictiveInsight]:
        """Filter insights by confidence and relevance."""
        # Filter by confidence threshold
        filtered = [i for i in insights if i.confidence >= self.confidence_threshold]
        
        # Remove duplicates by type
        unique_insights = {}
        for insight in filtered:
            key = f"{insight.insight_type}_{insight.prediction[:50]}"
            if key not in unique_insights or insight.confidence > unique_insights[key].confidence:
                unique_insights[key] = insight
        
        # Sort by confidence and limit
        sorted_insights = sorted(unique_insights.values(), key=lambda x: x.confidence, reverse=True)
        return sorted_insights[:self.max_insights]
    
    def _cleanup_expired_insights(self):
        """Remove expired insights."""
        current_time = time.time()
        expired_ids = [
            insight_id for insight_id, insight in self.insights.items()
            if insight.expires_at < current_time
        ]
        
        for insight_id in expired_ids:
            del self.insights[insight_id]
    
    async def get_active_insights(self) -> List[PredictiveInsight]:
        """Get all active predictive insights."""
        current_time = time.time()
        return [
            insight for insight in self.insights.values()
            if insight.expires_at > current_time
        ]
    
    async def get_insights_by_type(self, insight_type: str) -> List[PredictiveInsight]:
        """Get insights of a specific type."""
        return [
            insight for insight in self.insights.values()
            if insight.insight_type == insight_type
        ]


# Factory functions
def create_predictive_insight(insight_id: str, insight_type: str, prediction: str) -> PredictiveInsight:
    """Create a new predictive insight."""
    current_time = time.time()
    return PredictiveInsight(
        insight_id=insight_id,
        insight_type=insight_type,
        prediction=prediction,
        confidence=0.7,
        time_horizon=3600,
        context={},
        created_at=current_time,
        expires_at=current_time + 3600
    )
