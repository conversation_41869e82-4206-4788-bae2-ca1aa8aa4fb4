"""
Response Generator - Phase 2 Component

Advanced response generation for natural and contextual AI responses.
"""

import asyncio
import time
import json
import random
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from datetime import datetime, timedelta
import re

from loguru import logger

from ..utils.config_manager import ConfigManager
from ..core.ai_processor import ProcessedCommand, CommandIntent


@dataclass
class ResponseTemplate:
    """Response template data structure."""
    template_id: str
    intent: CommandIntent
    template: str
    variables: List[str]
    tone: str
    formality: str
    usage_count: int
    success_rate: float
    last_used: float


@dataclass
class GeneratedResponse:
    """Generated response data structure."""
    response_id: str
    text: str
    intent: CommandIntent
    confidence: float
    tone: str
    formality: str
    variables_used: Dict[str, Any]
    template_id: Optional[str]
    generation_time: float
    timestamp: float


class ResponseGenerator:
    """
    Advanced response generation system.
    
    Features:
    - Context-aware response generation
    - Multiple response templates
    - Tone and formality adaptation
    - Personalized responses
    - Dynamic variable substitution
    - Response quality optimization
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        
        # Response data
        self.response_templates: Dict[str, ResponseTemplate] = {}
        self.response_history: List[GeneratedResponse] = []
        self.user_preferences: Dict[str, Any] = {}
        
        # Generation configuration
        self.default_tone = self.config.get("intelligence.response.default_tone", "friendly")
        self.default_formality = self.config.get("intelligence.response.default_formality", "casual")
        self.max_response_length = self.config.get("intelligence.response.max_length", 500)
        self.personalization_enabled = self.config.get("intelligence.response.personalization", True)
        
        # Initialize response templates
        self._initialize_response_templates()
        
        logger.info("Response Generator initialized")
    
    def _initialize_response_templates(self):
        """Initialize predefined response templates."""
        try:
            # File operation responses
            self.response_templates["file_success"] = ResponseTemplate(
                template_id="file_success",
                intent=CommandIntent.FILE_OPERATION,
                template="Successfully {action} {target}. {details}",
                variables=["action", "target", "details"],
                tone="positive",
                formality="casual",
                usage_count=0,
                success_rate=0.9,
                last_used=time.time()
            )
            
            self.response_templates["file_error"] = ResponseTemplate(
                template_id="file_error",
                intent=CommandIntent.FILE_OPERATION,
                template="I couldn't {action} {target}. {error_reason} Would you like me to try a different approach?",
                variables=["action", "target", "error_reason"],
                tone="apologetic",
                formality="casual",
                usage_count=0,
                success_rate=0.8,
                last_used=time.time()
            )
            
            # System information responses
            self.response_templates["system_info"] = ResponseTemplate(
                template_id="system_info",
                intent=CommandIntent.SYSTEM_INFO,
                template="Here's your system information: {info_details}. {additional_context}",
                variables=["info_details", "additional_context"],
                tone="informative",
                formality="casual",
                usage_count=0,
                success_rate=0.95,
                last_used=time.time()
            )
            
            # Web search responses
            self.response_templates["search_results"] = ResponseTemplate(
                template_id="search_results",
                intent=CommandIntent.WEB_SEARCH,
                template="I found {result_count} results for '{query}'. {top_result}",
                variables=["result_count", "query", "top_result"],
                tone="helpful",
                formality="casual",
                usage_count=0,
                success_rate=0.85,
                last_used=time.time()
            )
            
            # Application control responses
            self.response_templates["app_launched"] = ResponseTemplate(
                template_id="app_launched",
                intent=CommandIntent.APPLICATION_CONTROL,
                template="{app_name} is now {action}. {status_info}",
                variables=["app_name", "action", "status_info"],
                tone="confirmative",
                formality="casual",
                usage_count=0,
                success_rate=0.92,
                last_used=time.time()
            )
            
            # Information responses
            self.response_templates["information_provided"] = ResponseTemplate(
                template_id="information_provided",
                intent=CommandIntent.INFORMATION,
                template="{answer} {additional_info}",
                variables=["answer", "additional_info"],
                tone="educational",
                formality="casual",
                usage_count=0,
                success_rate=0.88,
                last_used=time.time()
            )
            
            # Error and unknown responses
            self.response_templates["unknown_intent"] = ResponseTemplate(
                template_id="unknown_intent",
                intent=CommandIntent.UNKNOWN,
                template="I'm not sure what you'd like me to do. Could you please rephrase that? {suggestion}",
                variables=["suggestion"],
                tone="helpful",
                formality="casual",
                usage_count=0,
                success_rate=0.7,
                last_used=time.time()
            )
            
            self.response_templates["general_error"] = ResponseTemplate(
                template_id="general_error",
                intent=CommandIntent.UNKNOWN,
                template="I encountered an issue while processing your request. {error_details} Let me try again.",
                variables=["error_details"],
                tone="apologetic",
                formality="casual",
                usage_count=0,
                success_rate=0.75,
                last_used=time.time()
            )
            
            logger.info(f"Initialized {len(self.response_templates)} response templates")
            
        except Exception as e:
            logger.error(f"Error initializing response templates: {e}")
    
    async def generate_response(self, command: ProcessedCommand, 
                              execution_result: Dict[str, Any],
                              context: Dict[str, Any] = None) -> GeneratedResponse:
        """Generate a response based on command and execution result."""
        try:
            if context is None:
                context = {}
            
            start_time = time.time()
            
            # Determine response characteristics
            tone = self._determine_tone(command, execution_result, context)
            formality = self._determine_formality(command, context)
            
            # Select appropriate template
            template = self._select_template(command.intent, execution_result, tone)
            
            # Generate response text
            response_text = await self._generate_response_text(
                template, command, execution_result, context, tone, formality
            )
            
            # Create response object
            response = GeneratedResponse(
                response_id=f"resp_{int(time.time())}_{hash(response_text) % 10000}",
                text=response_text,
                intent=command.intent,
                confidence=self._calculate_response_confidence(template, execution_result),
                tone=tone,
                formality=formality,
                variables_used=self._extract_variables_used(template, execution_result),
                template_id=template.template_id if template else None,
                generation_time=time.time() - start_time,
                timestamp=time.time()
            )
            
            # Store in history
            self.response_history.append(response)
            self._cleanup_old_responses()
            
            # Update template usage
            if template:
                template.usage_count += 1
                template.last_used = time.time()
            
            return response
            
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return self._generate_fallback_response(command, execution_result)
    
    def _determine_tone(self, command: ProcessedCommand, 
                       execution_result: Dict[str, Any], 
                       context: Dict[str, Any]) -> str:
        """Determine appropriate tone for response."""
        try:
            # Check execution result
            if not execution_result.get('success', True):
                return "apologetic"
            
            # Check command urgency
            if any(word in command.text.lower() for word in ['urgent', 'quickly', 'fast', 'now']):
                return "efficient"
            
            # Check user preferences
            if self.personalization_enabled:
                preferred_tone = self.user_preferences.get('preferred_tone')
                if preferred_tone:
                    return preferred_tone
            
            # Check context
            if context.get('user_state') == 'frustrated':
                return "empathetic"
            elif context.get('user_state') == 'satisfied':
                return "positive"
            
            # Default tone
            return self.default_tone
            
        except Exception as e:
            logger.error(f"Error determining tone: {e}")
            return self.default_tone
    
    def _determine_formality(self, command: ProcessedCommand, context: Dict[str, Any]) -> str:
        """Determine appropriate formality level."""
        try:
            # Check user preferences
            if self.personalization_enabled:
                preferred_formality = self.user_preferences.get('preferred_formality')
                if preferred_formality:
                    return preferred_formality
            
            # Check command complexity
            if len(command.text) > 50 or command.intent in [CommandIntent.SYSTEM_CONTROL]:
                return "formal"
            
            # Check time of day
            current_hour = datetime.now().hour
            if 9 <= current_hour <= 17:  # Business hours
                return "semi_formal"
            
            return self.default_formality
            
        except Exception as e:
            logger.error(f"Error determining formality: {e}")
            return self.default_formality
    
    def _select_template(self, intent: CommandIntent, 
                        execution_result: Dict[str, Any], 
                        tone: str) -> Optional[ResponseTemplate]:
        """Select the best template for the response."""
        try:
            # Filter templates by intent
            candidate_templates = [
                template for template in self.response_templates.values()
                if template.intent == intent or template.intent == CommandIntent.UNKNOWN
            ]
            
            # Further filter by success/error
            if execution_result.get('success', True):
                # Success templates
                success_templates = [t for t in candidate_templates 
                                   if 'error' not in t.template_id and 'unknown' not in t.template_id]
                if success_templates:
                    candidate_templates = success_templates
            else:
                # Error templates
                error_templates = [t for t in candidate_templates 
                                 if 'error' in t.template_id or 'unknown' in t.template_id]
                if error_templates:
                    candidate_templates = error_templates
            
            # Select best template based on success rate and usage
            if candidate_templates:
                # Score templates
                scored_templates = []
                for template in candidate_templates:
                    score = template.success_rate * 0.7
                    
                    # Tone matching bonus
                    if template.tone == tone:
                        score += 0.2
                    
                    # Recency bonus (prefer recently successful templates)
                    recency = max(0, 1 - (time.time() - template.last_used) / 86400)  # 24 hours
                    score += recency * 0.1
                    
                    scored_templates.append((template, score))
                
                # Return best template
                scored_templates.sort(key=lambda x: x[1], reverse=True)
                return scored_templates[0][0]
            
            return None
            
        except Exception as e:
            logger.error(f"Error selecting template: {e}")
            return None
    
    async def _generate_response_text(self, template: Optional[ResponseTemplate],
                                    command: ProcessedCommand,
                                    execution_result: Dict[str, Any],
                                    context: Dict[str, Any],
                                    tone: str,
                                    formality: str) -> str:
        """Generate the actual response text."""
        try:
            if not template:
                return self._generate_generic_response(command, execution_result, tone)
            
            # Prepare variables for substitution
            variables = self._prepare_template_variables(
                template, command, execution_result, context
            )
            
            # Substitute variables in template
            response_text = template.template
            for var_name, var_value in variables.items():
                placeholder = f"{{{var_name}}}"
                response_text = response_text.replace(placeholder, str(var_value))
            
            # Apply tone and formality adjustments
            response_text = self._apply_tone_adjustments(response_text, tone)
            response_text = self._apply_formality_adjustments(response_text, formality)
            
            # Add personalization
            if self.personalization_enabled:
                response_text = self._add_personalization(response_text, context)
            
            # Ensure length limits
            if len(response_text) > self.max_response_length:
                response_text = response_text[:self.max_response_length-3] + "..."
            
            return response_text
            
        except Exception as e:
            logger.error(f"Error generating response text: {e}")
            return self._generate_generic_response(command, execution_result, tone)
    
    def _prepare_template_variables(self, template: ResponseTemplate,
                                  command: ProcessedCommand,
                                  execution_result: Dict[str, Any],
                                  context: Dict[str, Any]) -> Dict[str, str]:
        """Prepare variables for template substitution."""
        variables = {}
        
        try:
            # Common variables
            variables['action'] = execution_result.get('action', 'process')
            variables['target'] = execution_result.get('target', 'the request')
            variables['details'] = execution_result.get('details', '')
            variables['error_reason'] = execution_result.get('error', 'An unexpected error occurred')
            
            # Intent-specific variables
            if command.intent == CommandIntent.SYSTEM_INFO:
                variables['info_details'] = execution_result.get('system_info', 'System information')
                variables['additional_context'] = self._get_system_context(execution_result)
            
            elif command.intent == CommandIntent.WEB_SEARCH:
                variables['query'] = command.entities.get('search_query', command.text)
                variables['result_count'] = str(execution_result.get('result_count', 0))
                variables['top_result'] = execution_result.get('top_result', 'No results found')
            
            elif command.intent == CommandIntent.APPLICATION_CONTROL:
                variables['app_name'] = execution_result.get('application', 'the application')
                variables['status_info'] = execution_result.get('status', '')
            
            elif command.intent == CommandIntent.INFORMATION:
                variables['answer'] = execution_result.get('answer', 'Here is the information you requested')
                variables['additional_info'] = execution_result.get('additional_info', '')
            
            # Fallback variables
            variables['suggestion'] = self._generate_suggestion(command, context)
            variables['error_details'] = execution_result.get('error_details', '')
            
            # Clean up empty variables
            variables = {k: v for k, v in variables.items() if v}
            
            return variables
            
        except Exception as e:
            logger.error(f"Error preparing template variables: {e}")
            return {}
    
    def _get_system_context(self, execution_result: Dict[str, Any]) -> str:
        """Get additional system context for responses."""
        try:
            context_parts = []
            
            cpu_usage = execution_result.get('cpu_usage')
            if cpu_usage and cpu_usage > 80:
                context_parts.append("Your CPU usage is quite high.")
            
            memory_usage = execution_result.get('memory_usage')
            if memory_usage and memory_usage > 85:
                context_parts.append("Memory usage is elevated.")
            
            if not context_parts:
                context_parts.append("Everything looks good!")
            
            return " ".join(context_parts)
            
        except Exception as e:
            logger.error(f"Error getting system context: {e}")
            return ""
    
    def _generate_suggestion(self, command: ProcessedCommand, context: Dict[str, Any]) -> str:
        """Generate helpful suggestions for unclear commands."""
        try:
            suggestions = [
                "You can try being more specific about what you'd like me to do.",
                "For example, you could say 'show system info' or 'search for Python tutorials'.",
                "I can help with file operations, system information, web searches, and more."
            ]
            
            # Context-specific suggestions
            if context.get('recent_commands'):
                suggestions.append("You can also repeat a recent command if that helps.")
            
            return random.choice(suggestions)
            
        except Exception as e:
            logger.error(f"Error generating suggestion: {e}")
            return "Please try rephrasing your request."
    
    def _apply_tone_adjustments(self, text: str, tone: str) -> str:
        """Apply tone-specific adjustments to response text."""
        try:
            if tone == "apologetic":
                if not any(word in text.lower() for word in ['sorry', 'apologize', 'unfortunately']):
                    text = "I apologize, but " + text.lower()
            
            elif tone == "positive":
                positive_starters = ["Great! ", "Excellent! ", "Perfect! "]
                if not any(text.startswith(starter) for starter in positive_starters):
                    text = random.choice(positive_starters) + text
            
            elif tone == "empathetic":
                text = "I understand this might be frustrating. " + text
            
            elif tone == "efficient":
                # Remove unnecessary words for efficiency
                text = re.sub(r'\b(please|kindly|if you don\'t mind)\b', '', text, flags=re.IGNORECASE)
                text = re.sub(r'\s+', ' ', text).strip()
            
            return text
            
        except Exception as e:
            logger.error(f"Error applying tone adjustments: {e}")
            return text
    
    def _apply_formality_adjustments(self, text: str, formality: str) -> str:
        """Apply formality-specific adjustments to response text."""
        try:
            if formality == "formal":
                # Replace contractions
                text = text.replace("I'm", "I am")
                text = text.replace("you're", "you are")
                text = text.replace("can't", "cannot")
                text = text.replace("won't", "will not")
                text = text.replace("don't", "do not")
            
            elif formality == "casual":
                # Add casual elements
                if not any(word in text.lower() for word in ['!', 'great', 'awesome', 'cool']):
                    text = text.rstrip('.') + "!"
            
            return text
            
        except Exception as e:
            logger.error(f"Error applying formality adjustments: {e}")
            return text
    
    def _add_personalization(self, text: str, context: Dict[str, Any]) -> str:
        """Add personalization elements to response."""
        try:
            user_name = self.user_preferences.get('name')
            if user_name and random.random() < 0.3:  # 30% chance to use name
                text = f"{user_name}, {text.lower()}"
            
            # Add context-aware elements
            if context.get('time_of_day') == 'morning':
                greetings = ["Good morning! ", "Hope you're having a great morning! "]
                if random.random() < 0.2:
                    text = random.choice(greetings) + text
            
            return text
            
        except Exception as e:
            logger.error(f"Error adding personalization: {e}")
            return text
    
    def _generate_generic_response(self, command: ProcessedCommand, 
                                 execution_result: Dict[str, Any], 
                                 tone: str) -> str:
        """Generate a generic response when no template is available."""
        try:
            if execution_result.get('success', True):
                responses = [
                    "Task completed successfully.",
                    "Done! Your request has been processed.",
                    "All set! The operation completed successfully."
                ]
            else:
                responses = [
                    "I encountered an issue while processing your request.",
                    "Something went wrong, but I'm working on it.",
                    "There was a problem completing that task."
                ]
            
            base_response = random.choice(responses)
            return self._apply_tone_adjustments(base_response, tone)
            
        except Exception as e:
            logger.error(f"Error generating generic response: {e}")
            return "I've processed your request."
    
    def _calculate_response_confidence(self, template: Optional[ResponseTemplate], 
                                     execution_result: Dict[str, Any]) -> float:
        """Calculate confidence score for generated response."""
        try:
            confidence = 0.7  # Base confidence
            
            if template:
                confidence += template.success_rate * 0.2
            
            if execution_result.get('success', True):
                confidence += 0.1
            
            return min(confidence, 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating response confidence: {e}")
            return 0.5
    
    def _extract_variables_used(self, template: Optional[ResponseTemplate], 
                              execution_result: Dict[str, Any]) -> Dict[str, Any]:
        """Extract variables that were used in response generation."""
        try:
            if not template:
                return {}
            
            variables_used = {}
            for var_name in template.variables:
                if var_name in execution_result:
                    variables_used[var_name] = execution_result[var_name]
            
            return variables_used
            
        except Exception as e:
            logger.error(f"Error extracting variables used: {e}")
            return {}
    
    def _generate_fallback_response(self, command: ProcessedCommand, 
                                  execution_result: Dict[str, Any]) -> GeneratedResponse:
        """Generate a fallback response when generation fails."""
        return GeneratedResponse(
            response_id=f"fallback_{int(time.time())}",
            text="I've processed your request, but I'm having trouble generating a detailed response.",
            intent=command.intent,
            confidence=0.3,
            tone="neutral",
            formality="casual",
            variables_used={},
            template_id=None,
            generation_time=0.0,
            timestamp=time.time()
        )
    
    def _cleanup_old_responses(self):
        """Clean up old response history."""
        try:
            max_history = 500
            if len(self.response_history) > max_history:
                self.response_history = self.response_history[-max_history:]
                
        except Exception as e:
            logger.error(f"Error cleaning up old responses: {e}")
    
    async def update_user_preferences(self, preferences: Dict[str, Any]):
        """Update user preferences for personalization."""
        try:
            self.user_preferences.update(preferences)
            logger.info(f"Updated user preferences: {list(preferences.keys())}")
            
        except Exception as e:
            logger.error(f"Error updating user preferences: {e}")
    
    async def get_response_stats(self) -> Dict[str, Any]:
        """Get response generation statistics."""
        try:
            stats = {
                'total_responses': len(self.response_history),
                'template_usage': {},
                'tone_distribution': {},
                'formality_distribution': {},
                'average_confidence': 0.0,
                'average_generation_time': 0.0
            }
            
            if self.response_history:
                # Template usage
                for template_id, template in self.response_templates.items():
                    stats['template_usage'][template_id] = template.usage_count
                
                # Distributions
                tones = [resp.tone for resp in self.response_history]
                formalities = [resp.formality for resp in self.response_history]
                
                stats['tone_distribution'] = dict(Counter(tones))
                stats['formality_distribution'] = dict(Counter(formalities))
                
                # Averages
                stats['average_confidence'] = sum(resp.confidence for resp in self.response_history) / len(self.response_history)
                stats['average_generation_time'] = sum(resp.generation_time for resp in self.response_history) / len(self.response_history)
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting response stats: {e}")
            return {}


# Factory functions
def create_response_template(template_id: str, intent: CommandIntent, template: str) -> ResponseTemplate:
    """Create a new response template."""
    return ResponseTemplate(
        template_id=template_id,
        intent=intent,
        template=template,
        variables=[],
        tone="neutral",
        formality="casual",
        usage_count=0,
        success_rate=0.7,
        last_used=time.time()
    )
