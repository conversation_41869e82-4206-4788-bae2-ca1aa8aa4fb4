"""
Multi-Step Workflow Engine - Phase 2 Component

Manages complex multi-step workflows, task decomposition, and automated execution chains.
"""

import asyncio
import json
import time
import uuid
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path

from loguru import logger

from ..utils.config_manager import ConfigManager
from ..core.ai_processor import ProcessedCommand, CommandIntent
from ..core.command_executor import CommandExecutor, ExecutionResult


class WorkflowStatus(Enum):
    """Workflow execution status."""
    CREATED = "created"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class StepStatus(Enum):
    """Individual step status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass
class WorkflowStep:
    """Individual workflow step."""
    step_id: str
    name: str
    description: str
    command_template: str
    parameters: Dict[str, Any]
    dependencies: List[str]
    timeout: int
    retry_count: int
    status: StepStatus
    result: Optional[ExecutionResult] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    error_message: Optional[str] = None


@dataclass
class Workflow:
    """Complete workflow definition."""
    workflow_id: str
    name: str
    description: str
    steps: List[WorkflowStep]
    variables: Dict[str, Any]
    status: WorkflowStatus
    created_time: float
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    current_step_index: int = 0
    success_count: int = 0
    failure_count: int = 0
    metadata: Dict[str, Any] = None


class WorkflowEngine:
    """
    Advanced workflow engine for multi-step task execution.
    
    Features:
    - Complex workflow definition and execution
    - Step dependencies and conditional execution
    - Error handling and retry mechanisms
    - Workflow templates and reusable patterns
    - Progress tracking and monitoring
    - Variable passing between steps
    """
    
    def __init__(self, config_manager: ConfigManager, command_executor: CommandExecutor):
        self.config = config_manager
        self.command_executor = command_executor
        
        # Workflow storage
        self.active_workflows: Dict[str, Workflow] = {}
        self.workflow_templates: Dict[str, Dict[str, Any]] = {}
        self.workflow_history: List[Workflow] = []
        
        # Execution control
        self.max_concurrent_workflows = self.config.get("intelligence.workflow.max_concurrent", 5)
        self.default_step_timeout = self.config.get("intelligence.workflow.step_timeout", 300)
        self.max_retry_count = self.config.get("intelligence.workflow.max_retries", 3)
        
        # Callbacks
        self.on_workflow_started: Optional[Callable[[Workflow], None]] = None
        self.on_workflow_completed: Optional[Callable[[Workflow], None]] = None
        self.on_workflow_failed: Optional[Callable[[Workflow, str], None]] = None
        self.on_step_completed: Optional[Callable[[Workflow, WorkflowStep], None]] = None
        
        # Load predefined templates
        self._load_workflow_templates()
        
        logger.info("Workflow Engine initialized")
    
    def _load_workflow_templates(self):
        """Load predefined workflow templates."""
        
        # File organization workflow
        self.workflow_templates["organize_files"] = {
            "name": "Organize Files by Type",
            "description": "Organize files in a directory by their type/extension",
            "steps": [
                {
                    "name": "list_files",
                    "description": "List all files in target directory",
                    "command_template": "list files in {directory}",
                    "parameters": {"directory": ""},
                    "dependencies": [],
                    "timeout": 30
                },
                {
                    "name": "create_folders",
                    "description": "Create folders for different file types",
                    "command_template": "create folder {folder_name}",
                    "parameters": {"folder_name": ""},
                    "dependencies": ["list_files"],
                    "timeout": 60
                },
                {
                    "name": "move_files",
                    "description": "Move files to appropriate folders",
                    "command_template": "move {source} to {destination}",
                    "parameters": {"source": "", "destination": ""},
                    "dependencies": ["create_folders"],
                    "timeout": 120
                }
            ]
        }
        
        # Project setup workflow
        self.workflow_templates["setup_project"] = {
            "name": "Setup New Project",
            "description": "Create a new project with standard structure",
            "steps": [
                {
                    "name": "create_project_folder",
                    "description": "Create main project folder",
                    "command_template": "create folder {project_name}",
                    "parameters": {"project_name": ""},
                    "dependencies": [],
                    "timeout": 30
                },
                {
                    "name": "create_subfolders",
                    "description": "Create project subfolders",
                    "command_template": "create folder {subfolder}",
                    "parameters": {"subfolder": ""},
                    "dependencies": ["create_project_folder"],
                    "timeout": 60
                },
                {
                    "name": "create_readme",
                    "description": "Create README file",
                    "command_template": "create file README.md",
                    "parameters": {},
                    "dependencies": ["create_project_folder"],
                    "timeout": 30
                }
            ]
        }
        
        # System maintenance workflow
        self.workflow_templates["system_maintenance"] = {
            "name": "System Maintenance Check",
            "description": "Perform comprehensive system health check",
            "steps": [
                {
                    "name": "check_cpu",
                    "description": "Check CPU usage",
                    "command_template": "what's my CPU usage",
                    "parameters": {},
                    "dependencies": [],
                    "timeout": 30
                },
                {
                    "name": "check_memory",
                    "description": "Check memory usage",
                    "command_template": "show memory information",
                    "parameters": {},
                    "dependencies": [],
                    "timeout": 30
                },
                {
                    "name": "check_disk",
                    "description": "Check disk space",
                    "command_template": "check disk space",
                    "parameters": {},
                    "dependencies": [],
                    "timeout": 30
                }
            ]
        }
    
    async def create_workflow_from_template(self, template_name: str, 
                                          parameters: Dict[str, Any]) -> Optional[str]:
        """
        Create a workflow from a predefined template.
        
        Args:
            template_name: Name of the template to use
            parameters: Parameters to customize the workflow
            
        Returns:
            Workflow ID if successful, None otherwise
        """
        
        if template_name not in self.workflow_templates:
            logger.error(f"Workflow template '{template_name}' not found")
            return None
        
        try:
            template = self.workflow_templates[template_name]
            workflow_id = str(uuid.uuid4())
            
            # Create workflow steps
            steps = []
            for i, step_template in enumerate(template["steps"]):
                step = WorkflowStep(
                    step_id=f"{workflow_id}_step_{i}",
                    name=step_template["name"],
                    description=step_template["description"],
                    command_template=step_template["command_template"],
                    parameters={**step_template["parameters"], **parameters},
                    dependencies=step_template["dependencies"],
                    timeout=step_template.get("timeout", self.default_step_timeout),
                    retry_count=0,
                    status=StepStatus.PENDING
                )
                steps.append(step)
            
            # Create workflow
            workflow = Workflow(
                workflow_id=workflow_id,
                name=template["name"],
                description=template["description"],
                steps=steps,
                variables=parameters,
                status=WorkflowStatus.CREATED,
                created_time=time.time(),
                metadata={"template": template_name}
            )
            
            self.active_workflows[workflow_id] = workflow
            
            logger.info(f"Created workflow '{workflow.name}' with ID: {workflow_id}")
            return workflow_id
            
        except Exception as e:
            logger.error(f"Error creating workflow from template: {e}")
            return None
    
    async def create_custom_workflow(self, name: str, description: str, 
                                   commands: List[str]) -> Optional[str]:
        """
        Create a custom workflow from a list of commands.
        
        Args:
            name: Workflow name
            description: Workflow description
            commands: List of command strings
            
        Returns:
            Workflow ID if successful, None otherwise
        """
        
        try:
            workflow_id = str(uuid.uuid4())
            
            # Create steps from commands
            steps = []
            for i, command in enumerate(commands):
                step = WorkflowStep(
                    step_id=f"{workflow_id}_step_{i}",
                    name=f"Step {i+1}",
                    description=f"Execute: {command}",
                    command_template=command,
                    parameters={},
                    dependencies=[f"{workflow_id}_step_{i-1}"] if i > 0 else [],
                    timeout=self.default_step_timeout,
                    retry_count=0,
                    status=StepStatus.PENDING
                )
                steps.append(step)
            
            # Create workflow
            workflow = Workflow(
                workflow_id=workflow_id,
                name=name,
                description=description,
                steps=steps,
                variables={},
                status=WorkflowStatus.CREATED,
                created_time=time.time(),
                metadata={"type": "custom"}
            )
            
            self.active_workflows[workflow_id] = workflow
            
            logger.info(f"Created custom workflow '{name}' with ID: {workflow_id}")
            return workflow_id
            
        except Exception as e:
            logger.error(f"Error creating custom workflow: {e}")
            return None
    
    async def start_workflow(self, workflow_id: str) -> bool:
        """
        Start executing a workflow.
        
        Args:
            workflow_id: ID of the workflow to start
            
        Returns:
            True if started successfully, False otherwise
        """
        
        if workflow_id not in self.active_workflows:
            logger.error(f"Workflow {workflow_id} not found")
            return False
        
        workflow = self.active_workflows[workflow_id]
        
        if workflow.status != WorkflowStatus.CREATED:
            logger.error(f"Workflow {workflow_id} is not in created state")
            return False
        
        # Check concurrent workflow limit
        active_count = sum(1 for wf in self.active_workflows.values() 
                          if wf.status == WorkflowStatus.ACTIVE)
        
        if active_count >= self.max_concurrent_workflows:
            logger.warning(f"Maximum concurrent workflows ({self.max_concurrent_workflows}) reached")
            return False
        
        try:
            workflow.status = WorkflowStatus.ACTIVE
            workflow.start_time = time.time()
            
            # Trigger callback
            if self.on_workflow_started:
                self.on_workflow_started(workflow)
            
            # Start execution in background
            asyncio.create_task(self._execute_workflow(workflow))
            
            logger.info(f"Started workflow: {workflow.name}")
            return True
            
        except Exception as e:
            logger.error(f"Error starting workflow: {e}")
            workflow.status = WorkflowStatus.FAILED
            return False
    
    async def _execute_workflow(self, workflow: Workflow):
        """Execute a workflow step by step."""
        
        try:
            logger.info(f"Executing workflow: {workflow.name}")
            
            while workflow.current_step_index < len(workflow.steps):
                step = workflow.steps[workflow.current_step_index]
                
                # Check dependencies
                if not await self._check_step_dependencies(workflow, step):
                    logger.error(f"Step dependencies not met: {step.name}")
                    step.status = StepStatus.FAILED
                    step.error_message = "Dependencies not met"
                    break
                
                # Execute step
                success = await self._execute_step(workflow, step)
                
                if success:
                    workflow.success_count += 1
                    workflow.current_step_index += 1
                    
                    # Trigger callback
                    if self.on_step_completed:
                        self.on_step_completed(workflow, step)
                else:
                    workflow.failure_count += 1
                    
                    # Check if we should retry or fail
                    if step.retry_count < self.max_retry_count:
                        step.retry_count += 1
                        step.status = StepStatus.PENDING
                        logger.info(f"Retrying step: {step.name} (attempt {step.retry_count})")
                        continue
                    else:
                        logger.error(f"Step failed after {self.max_retry_count} retries: {step.name}")
                        break
            
            # Complete workflow
            if workflow.current_step_index >= len(workflow.steps):
                workflow.status = WorkflowStatus.COMPLETED
                workflow.end_time = time.time()
                
                logger.info(f"Workflow completed: {workflow.name}")
                
                if self.on_workflow_completed:
                    self.on_workflow_completed(workflow)
            else:
                workflow.status = WorkflowStatus.FAILED
                workflow.end_time = time.time()
                
                logger.error(f"Workflow failed: {workflow.name}")
                
                if self.on_workflow_failed:
                    self.on_workflow_failed(workflow, "Step execution failed")
            
            # Move to history
            self.workflow_history.append(workflow)
            del self.active_workflows[workflow.workflow_id]
            
        except Exception as e:
            logger.error(f"Error executing workflow: {e}")
            workflow.status = WorkflowStatus.FAILED
            workflow.end_time = time.time()
            
            if self.on_workflow_failed:
                self.on_workflow_failed(workflow, str(e))
    
    async def _check_step_dependencies(self, workflow: Workflow, step: WorkflowStep) -> bool:
        """Check if step dependencies are satisfied."""
        
        if not step.dependencies:
            return True
        
        for dep_step_id in step.dependencies:
            # Find dependency step
            dep_step = None
            for s in workflow.steps:
                if s.step_id == dep_step_id or s.name == dep_step_id:
                    dep_step = s
                    break
            
            if not dep_step:
                logger.error(f"Dependency step not found: {dep_step_id}")
                return False
            
            if dep_step.status != StepStatus.COMPLETED:
                logger.error(f"Dependency step not completed: {dep_step.name}")
                return False
        
        return True
    
    async def _execute_step(self, workflow: Workflow, step: WorkflowStep) -> bool:
        """Execute a single workflow step."""
        
        try:
            logger.info(f"Executing step: {step.name}")
            
            step.status = StepStatus.RUNNING
            step.start_time = time.time()
            
            # Substitute variables in command template
            command_text = self._substitute_variables(step.command_template, 
                                                    workflow.variables, step.parameters)
            
            # Parse and execute command
            from ..core.ai_processor import AIProcessor
            ai_processor = AIProcessor(self.config)
            
            command = await ai_processor.process_command(command_text)
            result = await self.command_executor.execute_command(command, True)
            
            step.result = result
            step.end_time = time.time()
            
            if result.success:
                step.status = StepStatus.COMPLETED
                
                # Update workflow variables with result data
                if result.data:
                    workflow.variables.update(result.data)
                
                logger.info(f"Step completed successfully: {step.name}")
                return True
            else:
                step.status = StepStatus.FAILED
                step.error_message = result.error or result.message
                
                logger.error(f"Step failed: {step.name} - {step.error_message}")
                return False
                
        except asyncio.TimeoutError:
            step.status = StepStatus.FAILED
            step.error_message = "Step timeout"
            step.end_time = time.time()
            
            logger.error(f"Step timeout: {step.name}")
            return False
            
        except Exception as e:
            step.status = StepStatus.FAILED
            step.error_message = str(e)
            step.end_time = time.time()
            
            logger.error(f"Step execution error: {step.name} - {e}")
            return False
    
    def _substitute_variables(self, template: str, workflow_vars: Dict[str, Any], 
                            step_params: Dict[str, Any]) -> str:
        """Substitute variables in command template."""
        
        # Combine workflow variables and step parameters
        all_vars = {**workflow_vars, **step_params}
        
        # Simple variable substitution
        result = template
        for key, value in all_vars.items():
            placeholder = f"{{{key}}}"
            if placeholder in result:
                result = result.replace(placeholder, str(value))
        
        return result
    
    async def pause_workflow(self, workflow_id: str) -> bool:
        """Pause a running workflow."""
        
        if workflow_id not in self.active_workflows:
            return False
        
        workflow = self.active_workflows[workflow_id]
        if workflow.status == WorkflowStatus.ACTIVE:
            workflow.status = WorkflowStatus.PAUSED
            logger.info(f"Paused workflow: {workflow.name}")
            return True
        
        return False
    
    async def resume_workflow(self, workflow_id: str) -> bool:
        """Resume a paused workflow."""
        
        if workflow_id not in self.active_workflows:
            return False
        
        workflow = self.active_workflows[workflow_id]
        if workflow.status == WorkflowStatus.PAUSED:
            workflow.status = WorkflowStatus.ACTIVE
            asyncio.create_task(self._execute_workflow(workflow))
            logger.info(f"Resumed workflow: {workflow.name}")
            return True
        
        return False
    
    async def cancel_workflow(self, workflow_id: str) -> bool:
        """Cancel a workflow."""
        
        if workflow_id not in self.active_workflows:
            return False
        
        workflow = self.active_workflows[workflow_id]
        workflow.status = WorkflowStatus.CANCELLED
        workflow.end_time = time.time()
        
        # Move to history
        self.workflow_history.append(workflow)
        del self.active_workflows[workflow_id]
        
        logger.info(f"Cancelled workflow: {workflow.name}")
        return True
    
    def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed workflow status."""
        
        # Check active workflows
        if workflow_id in self.active_workflows:
            workflow = self.active_workflows[workflow_id]
        else:
            # Check history
            workflow = next((wf for wf in self.workflow_history 
                           if wf.workflow_id == workflow_id), None)
        
        if not workflow:
            return None
        
        return {
            "workflow_id": workflow.workflow_id,
            "name": workflow.name,
            "status": workflow.status.value,
            "progress": f"{workflow.current_step_index}/{len(workflow.steps)}",
            "success_count": workflow.success_count,
            "failure_count": workflow.failure_count,
            "created_time": workflow.created_time,
            "start_time": workflow.start_time,
            "end_time": workflow.end_time,
            "steps": [
                {
                    "name": step.name,
                    "status": step.status.value,
                    "start_time": step.start_time,
                    "end_time": step.end_time,
                    "error_message": step.error_message
                }
                for step in workflow.steps
            ]
        }
    
    def get_active_workflows(self) -> List[Dict[str, Any]]:
        """Get list of all active workflows."""
        
        return [
            {
                "workflow_id": wf.workflow_id,
                "name": wf.name,
                "status": wf.status.value,
                "progress": f"{wf.current_step_index}/{len(wf.steps)}",
                "start_time": wf.start_time
            }
            for wf in self.active_workflows.values()
        ]
    
    def get_workflow_templates(self) -> List[str]:
        """Get list of available workflow templates."""
        return list(self.workflow_templates.keys())
