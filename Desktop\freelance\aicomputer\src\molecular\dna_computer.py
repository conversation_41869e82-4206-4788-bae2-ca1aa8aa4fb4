"""
Molecular Computing Interface - Phase 9 Component

DNA data storage and molecular computing system.
"""

import asyncio
import time
import json
import hashlib
import base64
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from datetime import datetime, <PERSON><PERSON><PERSON>
from enum import Enum
import threading
import math
import random

from loguru import logger

try:
    from ..utils.config_manager import ConfigManager
    from ..quantum.quantum_ai_processor import QuantumAIProcessor
except ImportError:
    from src.utils.config_manager import ConfigManager
    from src.quantum.quantum_ai_processor import QuantumAIProcessor


class DNABase(Enum):
    """DNA base pairs."""
    ADENINE = "A"
    THYMINE = "T"
    GUANINE = "G"
    CYTOSINE = "C"


class MolecularOperation(Enum):
    """Types of molecular operations."""
    ENCODE = "encode"
    DECODE = "decode"
    REPLICATE = "replicate"
    TRANSCRIBE = "transcribe"
    TRANSLATE = "translate"
    SYNTHESIZE = "synthesize"
    SEQUENCE = "sequence"
    AMPLIFY = "amplify"


class StorageFormat(Enum):
    """DNA storage formats."""
    BINARY = "binary"
    QUATERNARY = "quaternary"
    HUFFMAN = "huffman"
    FOUNTAIN = "fountain"
    REED_SOLOMON = "reed_solomon"


@dataclass
class DNASequence:
    """DNA sequence structure."""
    sequence_id: str
    sequence: str
    length: int
    gc_content: float
    melting_temperature: float
    encoded_data: str
    storage_format: StorageFormat
    error_correction: bool
    created_at: float
    metadata: Dict[str, Any]


@dataclass
class MolecularComputation:
    """Molecular computation structure."""
    computation_id: str
    operation: MolecularOperation
    input_sequences: List[str]
    output_sequences: List[str]
    reaction_conditions: Dict[str, Any]
    processing_time: float
    success_rate: float
    error_rate: float
    timestamp: float
    metadata: Dict[str, Any]


@dataclass
class DNAStorage:
    """DNA storage system structure."""
    storage_id: str
    total_capacity: int  # in base pairs
    used_capacity: int
    stored_sequences: List[str]
    redundancy_factor: int
    error_correction_level: float
    access_time: float
    durability_years: int
    created_at: float


class MolecularComputer:
    """
    Advanced Molecular Computing Interface.
    
    Features:
    - DNA data storage and retrieval
    - Molecular computation algorithms
    - Genetic programming
    - Bio-inspired optimization
    - Protein folding simulation
    - Synthetic biology protocols
    - Quantum-molecular hybrid computing
    """
    
    def __init__(self, config_manager: ConfigManager, quantum_processor: QuantumAIProcessor = None):
        self.config = config_manager
        self.quantum_processor = quantum_processor
        
        # Molecular computing state
        self.dna_sequences: Dict[str, DNASequence] = {}
        self.molecular_computations: deque = deque(maxlen=10000)
        self.storage_systems: Dict[str, DNAStorage] = {}
        self.active_reactions: Dict[str, MolecularComputation] = {}
        
        # Configuration
        self.max_sequence_length = self.config.get("molecular.max_sequence_length", 10000)
        self.default_redundancy = self.config.get("molecular.redundancy_factor", 3)
        self.error_correction_threshold = self.config.get("molecular.error_threshold", 0.001)
        self.synthesis_rate = self.config.get("molecular.synthesis_rate", 1000)  # bp/s
        
        # DNA encoding tables
        self.binary_to_dna = {
            "00": "A", "01": "T", "10": "G", "11": "C"
        }
        self.dna_to_binary = {v: k for k, v in self.binary_to_dna.items()}
        
        # Molecular algorithms
        self.molecular_algorithms = {
            MolecularOperation.ENCODE: self._encode_data_to_dna,
            MolecularOperation.DECODE: self._decode_dna_to_data,
            MolecularOperation.REPLICATE: self._replicate_dna,
            MolecularOperation.TRANSCRIBE: self._transcribe_dna,
            MolecularOperation.TRANSLATE: self._translate_rna,
            MolecularOperation.SYNTHESIZE: self._synthesize_dna,
            MolecularOperation.SEQUENCE: self._sequence_dna,
            MolecularOperation.AMPLIFY: self._amplify_dna
        }
        
        # Performance metrics
        self.total_operations = 0
        self.successful_operations = 0
        self.total_data_stored = 0  # in bytes
        self.storage_efficiency = 0.0
        
        # Monitoring
        self.molecular_active = False
        self.monitor_thread: Optional[threading.Thread] = None
        
        # Initialize molecular systems
        self._initialize_molecular_systems()
        
        logger.info("Molecular Computer initialized")
    
    def _initialize_molecular_systems(self):
        """Initialize molecular computing systems."""
        try:
            # Create default DNA storage system
            storage = DNAStorage(
                storage_id="primary_dna_storage",
                total_capacity=1000000,  # 1M base pairs
                used_capacity=0,
                stored_sequences=[],
                redundancy_factor=self.default_redundancy,
                error_correction_level=0.999,
                access_time=0.1,  # seconds
                durability_years=1000,
                created_at=time.time()
            )
            
            self.storage_systems[storage.storage_id] = storage
            
            logger.info(f"Initialized DNA storage with {storage.total_capacity} bp capacity")
            
        except Exception as e:
            logger.error(f"Error initializing molecular systems: {e}")
    
    async def start_molecular_computer(self) -> bool:
        """Start the molecular computing system."""
        try:
            if self.molecular_active:
                logger.warning("Molecular computer already active")
                return False
            
            # Start monitoring
            self.molecular_active = True
            self.monitor_thread = threading.Thread(
                target=self._molecular_monitoring_loop,
                daemon=True
            )
            self.monitor_thread.start()
            
            logger.info("🧬 Molecular Computer started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error starting molecular computer: {e}")
            return False
    
    def _molecular_monitoring_loop(self):
        """Molecular computing monitoring loop."""
        while self.molecular_active:
            try:
                # Monitor active reactions
                asyncio.run(self._monitor_reactions())
                
                # Maintain DNA integrity
                asyncio.run(self._maintain_dna_integrity())
                
                # Update performance metrics
                self._update_performance_metrics()
                
                time.sleep(10)  # 10 second monitoring interval
                
            except Exception as e:
                logger.error(f"Error in molecular monitoring loop: {e}")
                time.sleep(10)
    
    async def store_data_in_dna(self, data: str, storage_format: StorageFormat = StorageFormat.BINARY) -> str:
        """Store data in DNA sequences."""
        try:
            # Encode data to DNA
            dna_sequence = await self._encode_data_to_dna(data, storage_format)
            
            if not dna_sequence:
                raise ValueError("Failed to encode data to DNA")
            
            # Add error correction
            if storage_format in [StorageFormat.REED_SOLOMON, StorageFormat.FOUNTAIN]:
                dna_sequence = await self._add_error_correction(dna_sequence, storage_format)
            
            # Create DNA sequence object
            sequence_obj = DNASequence(
                sequence_id=f"seq_{int(time.time() * 1000000)}",
                sequence=dna_sequence,
                length=len(dna_sequence),
                gc_content=self._calculate_gc_content(dna_sequence),
                melting_temperature=self._calculate_melting_temp(dna_sequence),
                encoded_data=data,
                storage_format=storage_format,
                error_correction=storage_format in [StorageFormat.REED_SOLOMON, StorageFormat.FOUNTAIN],
                created_at=time.time(),
                metadata={"original_size": len(data), "compression_ratio": len(data) / len(dna_sequence)}
            )
            
            # Store in DNA storage system
            storage_id = await self._store_in_dna_storage(sequence_obj)
            
            if storage_id:
                self.dna_sequences[sequence_obj.sequence_id] = sequence_obj
                self.total_data_stored += len(data)
                self.successful_operations += 1
                
                logger.info(f"🧬 Data stored in DNA: {sequence_obj.sequence_id} ({len(data)} bytes → {len(dna_sequence)} bp)")
                return sequence_obj.sequence_id
            
            raise ValueError("Failed to store DNA sequence")
            
        except Exception as e:
            logger.error(f"Error storing data in DNA: {e}")
            return ""
    
    async def retrieve_data_from_dna(self, sequence_id: str) -> str:
        """Retrieve data from DNA sequences."""
        try:
            if sequence_id not in self.dna_sequences:
                raise ValueError(f"DNA sequence {sequence_id} not found")
            
            sequence_obj = self.dna_sequences[sequence_id]
            
            # Simulate DNA sequencing
            sequenced_dna = await self._sequence_dna(sequence_obj.sequence)
            
            # Apply error correction if needed
            if sequence_obj.error_correction:
                corrected_dna = await self._correct_dna_errors(sequenced_dna, sequence_obj.storage_format)
                if corrected_dna:
                    sequenced_dna = corrected_dna
            
            # Decode DNA to data
            decoded_data = await self._decode_dna_to_data(sequenced_dna, sequence_obj.storage_format)
            
            if decoded_data:
                self.successful_operations += 1
                logger.info(f"🧬 Data retrieved from DNA: {sequence_id}")
                return decoded_data
            
            raise ValueError("Failed to decode DNA sequence")
            
        except Exception as e:
            logger.error(f"Error retrieving data from DNA: {e}")
            return ""
    
    async def perform_molecular_computation(self, operation: MolecularOperation, 
                                          input_data: Dict[str, Any]) -> MolecularComputation:
        """Perform molecular computation."""
        try:
            start_time = time.time()
            
            # Execute molecular operation
            if operation in self.molecular_algorithms:
                algorithm = self.molecular_algorithms[operation]
                
                if operation in [MolecularOperation.ENCODE, MolecularOperation.DECODE]:
                    result = await algorithm(
                        input_data.get("data", ""),
                        input_data.get("format", StorageFormat.BINARY)
                    )
                else:
                    result = await algorithm(input_data.get("sequence", ""))
                
                # Create computation record
                computation = MolecularComputation(
                    computation_id=f"molcomp_{int(time.time() * 1000000)}",
                    operation=operation,
                    input_sequences=input_data.get("sequences", []),
                    output_sequences=[result] if isinstance(result, str) else result if isinstance(result, list) else [],
                    reaction_conditions=input_data.get("conditions", {}),
                    processing_time=time.time() - start_time,
                    success_rate=0.95 + random.uniform(-0.05, 0.05),
                    error_rate=0.001 + random.uniform(0, 0.004),
                    timestamp=time.time(),
                    metadata={"operation_type": operation.value}
                )
                
                self.molecular_computations.append(computation)
                self.total_operations += 1
                
                logger.info(f"🧬 Molecular computation completed: {computation.computation_id}")
                return computation
            
            else:
                raise ValueError(f"Unsupported molecular operation: {operation}")
                
        except Exception as e:
            logger.error(f"Error performing molecular computation: {e}")
            return MolecularComputation(
                computation_id=f"error_{int(time.time() * 1000000)}",
                operation=operation,
                input_sequences=[],
                output_sequences=[],
                reaction_conditions={},
                processing_time=time.time() - start_time,
                success_rate=0.0,
                error_rate=1.0,
                timestamp=time.time(),
                metadata={"error": str(e)}
            )

    async def _encode_data_to_dna(self, data: str, storage_format: StorageFormat) -> str:
        """Encode data to DNA sequence."""
        try:
            if storage_format == StorageFormat.BINARY:
                # Convert to binary
                binary_data = ''.join(format(ord(char), '08b') for char in data)

                # Pad to make divisible by 2
                if len(binary_data) % 2 != 0:
                    binary_data += '0'

                # Convert binary to DNA
                dna_sequence = ""
                for i in range(0, len(binary_data), 2):
                    binary_pair = binary_data[i:i+2]
                    dna_sequence += self.binary_to_dna[binary_pair]

                return dna_sequence

            elif storage_format == StorageFormat.QUATERNARY:
                # Direct quaternary encoding (4 bases = 4 states)
                dna_sequence = ""
                for char in data:
                    ascii_val = ord(char)
                    # Convert to base 4
                    base4_digits = []
                    temp = ascii_val
                    for _ in range(4):  # 4 digits can represent 0-255
                        base4_digits.append(temp % 4)
                        temp //= 4

                    # Map to DNA bases
                    base_map = {0: 'A', 1: 'T', 2: 'G', 3: 'C'}
                    for digit in reversed(base4_digits):
                        dna_sequence += base_map[digit]

                return dna_sequence

            elif storage_format == StorageFormat.HUFFMAN:
                # Simplified Huffman-like encoding
                char_freq = {}
                for char in data:
                    char_freq[char] = char_freq.get(char, 0) + 1

                # Create simple encoding based on frequency
                sorted_chars = sorted(char_freq.items(), key=lambda x: x[1], reverse=True)
                encoding_map = {}

                for i, (char, _) in enumerate(sorted_chars):
                    # Map to DNA sequences of varying length
                    if i < 4:
                        encoding_map[char] = ['A', 'T', 'G', 'C'][i]
                    else:
                        # Longer sequences for less frequent characters
                        binary_rep = format(i, '04b')
                        dna_rep = ""
                        for j in range(0, len(binary_rep), 2):
                            pair = binary_rep[j:j+2]
                            dna_rep += self.binary_to_dna.get(pair, 'A')
                        encoding_map[char] = dna_rep

                # Encode data
                dna_sequence = ""
                for char in data:
                    dna_sequence += encoding_map.get(char, 'A')

                return dna_sequence

            else:
                # Default to binary encoding
                return await self._encode_data_to_dna(data, StorageFormat.BINARY)

        except Exception as e:
            logger.error(f"Error encoding data to DNA: {e}")
            return ""

    async def _decode_dna_to_data(self, dna_sequence: str, storage_format: StorageFormat) -> str:
        """Decode DNA sequence to data."""
        try:
            if storage_format == StorageFormat.BINARY:
                # Convert DNA to binary
                binary_data = ""
                for base in dna_sequence:
                    binary_data += self.dna_to_binary.get(base, "00")

                # Convert binary to text
                decoded_data = ""
                for i in range(0, len(binary_data), 8):
                    byte = binary_data[i:i+8]
                    if len(byte) == 8:
                        try:
                            char = chr(int(byte, 2))
                            decoded_data += char
                        except ValueError:
                            continue

                return decoded_data

            elif storage_format == StorageFormat.QUATERNARY:
                # Decode quaternary
                base_map = {'A': 0, 'T': 1, 'G': 2, 'C': 3}
                decoded_data = ""

                for i in range(0, len(dna_sequence), 4):
                    if i + 3 < len(dna_sequence):
                        # Get 4 bases
                        bases = dna_sequence[i:i+4]
                        ascii_val = 0

                        for j, base in enumerate(bases):
                            digit = base_map.get(base, 0)
                            ascii_val += digit * (4 ** (3 - j))

                        if 0 <= ascii_val <= 255:
                            decoded_data += chr(ascii_val)

                return decoded_data

            else:
                # Default to binary decoding
                return await self._decode_dna_to_data(dna_sequence, StorageFormat.BINARY)

        except Exception as e:
            logger.error(f"Error decoding DNA to data: {e}")
            return ""

    async def _replicate_dna(self, sequence: str) -> str:
        """Simulate DNA replication."""
        try:
            # Simple replication with potential errors
            replicated = ""
            error_rate = 0.0001  # 1 in 10,000 bases

            for base in sequence:
                if random.random() < error_rate:
                    # Introduce replication error
                    bases = ['A', 'T', 'G', 'C']
                    bases.remove(base)
                    replicated += random.choice(bases)
                else:
                    replicated += base

            return replicated

        except Exception as e:
            logger.error(f"Error replicating DNA: {e}")
            return sequence

    async def _transcribe_dna(self, dna_sequence: str) -> str:
        """Transcribe DNA to RNA."""
        try:
            # DNA to RNA transcription (T → U)
            transcription_map = {'A': 'U', 'T': 'A', 'G': 'C', 'C': 'G'}

            rna_sequence = ""
            for base in dna_sequence:
                rna_sequence += transcription_map.get(base, base)

            return rna_sequence

        except Exception as e:
            logger.error(f"Error transcribing DNA: {e}")
            return dna_sequence

    async def _translate_rna(self, rna_sequence: str) -> str:
        """Translate RNA to protein sequence."""
        try:
            # Simplified genetic code
            codon_table = {
                'UUU': 'F', 'UUC': 'F', 'UUA': 'L', 'UUG': 'L',
                'UCU': 'S', 'UCC': 'S', 'UCA': 'S', 'UCG': 'S',
                'UAU': 'Y', 'UAC': 'Y', 'UAA': '*', 'UAG': '*',
                'UGU': 'C', 'UGC': 'C', 'UGA': '*', 'UGG': 'W',
                'CUU': 'L', 'CUC': 'L', 'CUA': 'L', 'CUG': 'L',
                'CCU': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
                'CAU': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
                'CGU': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
                'AUU': 'I', 'AUC': 'I', 'AUA': 'I', 'AUG': 'M',
                'ACU': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
                'AAU': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
                'AGU': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
                'GUU': 'V', 'GUC': 'V', 'GUA': 'V', 'GUG': 'V',
                'GCU': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
                'GAU': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
                'GGU': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
            }

            protein_sequence = ""
            for i in range(0, len(rna_sequence), 3):
                codon = rna_sequence[i:i+3]
                if len(codon) == 3:
                    amino_acid = codon_table.get(codon, 'X')  # X for unknown
                    if amino_acid == '*':  # Stop codon
                        break
                    protein_sequence += amino_acid

            return protein_sequence

        except Exception as e:
            logger.error(f"Error translating RNA: {e}")
            return ""

    async def _synthesize_dna(self, target_sequence: str) -> str:
        """Simulate DNA synthesis."""
        try:
            # Simulate synthesis with potential errors
            synthesized = ""
            synthesis_error_rate = 0.001  # 1 in 1,000 bases

            for base in target_sequence:
                if random.random() < synthesis_error_rate:
                    # Synthesis error
                    bases = ['A', 'T', 'G', 'C']
                    synthesized += random.choice(bases)
                else:
                    synthesized += base

            # Simulate synthesis time
            synthesis_time = len(target_sequence) / self.synthesis_rate
            await asyncio.sleep(min(synthesis_time, 0.1))  # Cap at 0.1 seconds for simulation

            return synthesized

        except Exception as e:
            logger.error(f"Error synthesizing DNA: {e}")
            return target_sequence

    async def _sequence_dna(self, dna_sequence: str) -> str:
        """Simulate DNA sequencing."""
        try:
            # Simulate sequencing with potential errors
            sequenced = ""
            sequencing_error_rate = 0.0001  # 1 in 10,000 bases

            for base in dna_sequence:
                if random.random() < sequencing_error_rate:
                    # Sequencing error
                    bases = ['A', 'T', 'G', 'C']
                    sequenced += random.choice(bases)
                else:
                    sequenced += base

            return sequenced

        except Exception as e:
            logger.error(f"Error sequencing DNA: {e}")
            return dna_sequence

    async def _amplify_dna(self, dna_sequence: str) -> str:
        """Simulate DNA amplification (PCR)."""
        try:
            # Simulate PCR amplification
            amplified_copies = []
            num_cycles = 30  # Typical PCR cycles

            current_template = dna_sequence

            for cycle in range(min(num_cycles, 10)):  # Limit for simulation
                # Replicate current templates
                new_copy = await self._replicate_dna(current_template)
                amplified_copies.append(new_copy)

                # Use best copy as template for next cycle
                if len(amplified_copies) > 1:
                    # Select copy with fewest errors (highest similarity to original)
                    best_copy = max(amplified_copies, key=lambda x: self._calculate_similarity(x, dna_sequence))
                    current_template = best_copy

            # Return the best amplified copy
            if amplified_copies:
                return max(amplified_copies, key=lambda x: self._calculate_similarity(x, dna_sequence))

            return dna_sequence

        except Exception as e:
            logger.error(f"Error amplifying DNA: {e}")
            return dna_sequence

    def _calculate_similarity(self, seq1: str, seq2: str) -> float:
        """Calculate similarity between two DNA sequences."""
        try:
            if not seq1 or not seq2:
                return 0.0

            min_len = min(len(seq1), len(seq2))
            matches = sum(1 for i in range(min_len) if seq1[i] == seq2[i])

            return matches / min_len if min_len > 0 else 0.0

        except Exception as e:
            logger.error(f"Error calculating similarity: {e}")
            return 0.0

    def _calculate_gc_content(self, sequence: str) -> float:
        """Calculate GC content of DNA sequence."""
        try:
            if not sequence:
                return 0.0

            gc_count = sequence.count('G') + sequence.count('C')
            return gc_count / len(sequence)

        except Exception as e:
            logger.error(f"Error calculating GC content: {e}")
            return 0.0

    def _calculate_melting_temp(self, sequence: str) -> float:
        """Calculate melting temperature of DNA sequence."""
        try:
            if not sequence:
                return 0.0

            # Simplified melting temperature calculation
            # Tm = 64.9 + 41 * (G+C-16.4) / length
            gc_content = self._calculate_gc_content(sequence)
            length = len(sequence)

            if length > 0:
                tm = 64.9 + 41 * (gc_content * length - 16.4) / length
                return max(tm, 0.0)

            return 0.0

        except Exception as e:
            logger.error(f"Error calculating melting temperature: {e}")
            return 0.0

    async def _add_error_correction(self, sequence: str, format_type: StorageFormat) -> str:
        """Add error correction to DNA sequence."""
        try:
            if format_type == StorageFormat.REED_SOLOMON:
                # Simplified Reed-Solomon-like error correction
                # Add redundant bases every 10 bases
                corrected = ""
                for i in range(0, len(sequence), 10):
                    chunk = sequence[i:i+10]
                    corrected += chunk

                    # Add parity base
                    gc_count = chunk.count('G') + chunk.count('C')
                    parity_base = 'G' if gc_count % 2 == 0 else 'C'
                    corrected += parity_base

                return corrected

            elif format_type == StorageFormat.FOUNTAIN:
                # Simplified fountain code
                # Add random linear combinations as redundancy
                redundant_sequences = []

                for _ in range(3):  # Add 3 redundant sequences
                    redundant = ""
                    for i in range(len(sequence)):
                        if random.random() < 0.5:
                            redundant += sequence[i]
                        else:
                            # XOR with previous base
                            prev_base = sequence[i-1] if i > 0 else 'A'
                            xor_result = self._xor_bases(sequence[i], prev_base)
                            redundant += xor_result

                    redundant_sequences.append(redundant)

                # Combine original with redundant sequences
                return sequence + "".join(redundant_sequences)

            return sequence

        except Exception as e:
            logger.error(f"Error adding error correction: {e}")
            return sequence

    def _xor_bases(self, base1: str, base2: str) -> str:
        """XOR operation on DNA bases."""
        base_to_num = {'A': 0, 'T': 1, 'G': 2, 'C': 3}
        num_to_base = {0: 'A', 1: 'T', 2: 'G', 3: 'C'}

        num1 = base_to_num.get(base1, 0)
        num2 = base_to_num.get(base2, 0)

        result_num = num1 ^ num2
        return num_to_base[result_num]

    async def _correct_dna_errors(self, sequence: str, format_type: StorageFormat) -> Optional[str]:
        """Correct errors in DNA sequence."""
        try:
            if format_type == StorageFormat.REED_SOLOMON:
                # Simple error correction using parity
                corrected = ""
                i = 0

                while i < len(sequence):
                    if i + 10 < len(sequence):
                        chunk = sequence[i:i+10]
                        parity_base = sequence[i+10]

                        # Check parity
                        gc_count = chunk.count('G') + chunk.count('C')
                        expected_parity = 'G' if gc_count % 2 == 0 else 'C'

                        if parity_base == expected_parity:
                            corrected += chunk
                        else:
                            # Try to correct single error
                            corrected += chunk  # Simplified - just use original

                        i += 11
                    else:
                        corrected += sequence[i]
                        i += 1

                return corrected

            return sequence

        except Exception as e:
            logger.error(f"Error correcting DNA errors: {e}")
            return sequence

    async def _store_in_dna_storage(self, sequence: DNASequence) -> Optional[str]:
        """Store DNA sequence in storage system."""
        try:
            # Find storage system with available capacity
            for storage_id, storage in self.storage_systems.items():
                required_capacity = sequence.length * self.default_redundancy

                if storage.used_capacity + required_capacity <= storage.total_capacity:
                    # Store sequence
                    storage.stored_sequences.append(sequence.sequence_id)
                    storage.used_capacity += required_capacity

                    logger.info(f"DNA sequence stored in {storage_id}")
                    return storage_id

            # No available storage
            logger.warning("No available DNA storage capacity")
            return None

        except Exception as e:
            logger.error(f"Error storing in DNA storage: {e}")
            return None

    async def _monitor_reactions(self):
        """Monitor active molecular reactions."""
        try:
            current_time = time.time()
            completed_reactions = []

            for reaction_id, reaction in self.active_reactions.items():
                # Check if reaction is complete (simplified)
                if current_time - reaction.timestamp > 10:  # 10 seconds max
                    completed_reactions.append(reaction_id)

            # Remove completed reactions
            for reaction_id in completed_reactions:
                del self.active_reactions[reaction_id]

        except Exception as e:
            logger.error(f"Error monitoring reactions: {e}")

    async def _maintain_dna_integrity(self):
        """Maintain DNA sequence integrity."""
        try:
            # Check for degradation and repair if needed
            for sequence_id, sequence in self.dna_sequences.items():
                age = time.time() - sequence.created_at

                # Simulate degradation over time
                if age > 3600:  # 1 hour
                    degradation_rate = min(age / 86400, 0.1)  # Max 10% per day

                    if random.random() < degradation_rate:
                        # Repair sequence
                        repaired = await self._repair_dna_sequence(sequence.sequence)
                        if repaired and repaired != sequence.sequence:
                            sequence.sequence = repaired
                            logger.info(f"DNA sequence repaired: {sequence_id}")

        except Exception as e:
            logger.error(f"Error maintaining DNA integrity: {e}")

    async def _repair_dna_sequence(self, sequence: str) -> str:
        """Repair damaged DNA sequence."""
        try:
            # Simple repair by consensus
            if len(sequence) > 10:
                # Check for obvious errors and fix
                repaired = ""
                for i, base in enumerate(sequence):
                    if base not in ['A', 'T', 'G', 'C']:
                        # Replace invalid base with most common neighbor
                        neighbors = []
                        if i > 0:
                            neighbors.append(sequence[i-1])
                        if i < len(sequence) - 1:
                            neighbors.append(sequence[i+1])

                        if neighbors:
                            # Use most common neighbor
                            base_counts = {}
                            for neighbor in neighbors:
                                if neighbor in ['A', 'T', 'G', 'C']:
                                    base_counts[neighbor] = base_counts.get(neighbor, 0) + 1

                            if base_counts:
                                repaired += max(base_counts.items(), key=lambda x: x[1])[0]
                            else:
                                repaired += 'A'  # Default
                        else:
                            repaired += 'A'  # Default
                    else:
                        repaired += base

                return repaired

            return sequence

        except Exception as e:
            logger.error(f"Error repairing DNA sequence: {e}")
            return sequence

    def _update_performance_metrics(self):
        """Update performance metrics."""
        try:
            if self.total_operations > 0:
                success_rate = self.successful_operations / self.total_operations

                # Calculate storage efficiency
                total_storage_used = sum(storage.used_capacity for storage in self.storage_systems.values())
                total_storage_capacity = sum(storage.total_capacity for storage in self.storage_systems.values())

                if total_storage_capacity > 0:
                    self.storage_efficiency = total_storage_used / total_storage_capacity

                # Log performance periodically
                if self.total_operations % 50 == 0:
                    logger.info(f"🧬 Molecular Performance: {success_rate:.1%} success rate, "
                              f"{self.storage_efficiency:.1%} storage efficiency, "
                              f"{self.total_data_stored} bytes stored")

        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")

    async def get_molecular_status(self) -> Dict[str, Any]:
        """Get molecular computing system status."""
        try:
            total_storage_capacity = sum(storage.total_capacity for storage in self.storage_systems.values())
            total_storage_used = sum(storage.used_capacity for storage in self.storage_systems.values())

            return {
                "molecular_active": self.molecular_active,
                "total_operations": self.total_operations,
                "successful_operations": self.successful_operations,
                "success_rate": self.successful_operations / max(self.total_operations, 1),
                "total_dna_sequences": len(self.dna_sequences),
                "total_data_stored": self.total_data_stored,
                "storage_systems": len(self.storage_systems),
                "total_storage_capacity": total_storage_capacity,
                "storage_used": total_storage_used,
                "storage_efficiency": self.storage_efficiency,
                "active_reactions": len(self.active_reactions),
                "computation_history": len(self.molecular_computations),
                "quantum_integration": self.quantum_processor is not None,
                "supported_operations": [op.value for op in MolecularOperation],
                "supported_formats": [fmt.value for fmt in StorageFormat]
            }

        except Exception as e:
            logger.error(f"Error getting molecular status: {e}")
            return {}

    async def shutdown_molecular_computer(self) -> Dict[str, Any]:
        """Shutdown the molecular computing system."""
        try:
            self.molecular_active = False

            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=5.0)

            shutdown_summary = {
                "total_operations_performed": self.total_operations,
                "successful_operations": self.successful_operations,
                "final_success_rate": self.successful_operations / max(self.total_operations, 1),
                "total_data_stored": self.total_data_stored,
                "dna_sequences_created": len(self.dna_sequences),
                "final_storage_efficiency": self.storage_efficiency,
                "shutdown_timestamp": time.time()
            }

            logger.info("🧬 Molecular Computer gracefully shutdown")
            return shutdown_summary

        except Exception as e:
            logger.error(f"Error shutting down molecular computer: {e}")
            return {"error": str(e)}


# Factory functions
def create_dna_sequence(data: str, storage_format: StorageFormat = StorageFormat.BINARY) -> DNASequence:
    """Create a new DNA sequence."""
    # Simple encoding for factory function
    binary_to_dna = {"00": "A", "01": "T", "10": "G", "11": "C"}
    binary_data = ''.join(format(ord(char), '08b') for char in data)

    if len(binary_data) % 2 != 0:
        binary_data += '0'

    dna_sequence = ""
    for i in range(0, len(binary_data), 2):
        binary_pair = binary_data[i:i+2]
        dna_sequence += binary_to_dna[binary_pair]

    return DNASequence(
        sequence_id=f"seq_{int(time.time() * 1000000)}",
        sequence=dna_sequence,
        length=len(dna_sequence),
        gc_content=(dna_sequence.count('G') + dna_sequence.count('C')) / len(dna_sequence),
        melting_temperature=64.9,  # Simplified
        encoded_data=data,
        storage_format=storage_format,
        error_correction=False,
        created_at=time.time(),
        metadata={}
    )
