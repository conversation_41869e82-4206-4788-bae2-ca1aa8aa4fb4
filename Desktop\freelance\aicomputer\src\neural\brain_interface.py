"""
Brain Interface - Phase 9 Component

Advanced neural interface for direct brain-computer interaction.
"""

import asyncio
import time
import json
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from datetime import datetime, timedelta
from enum import Enum
import threading
import math

from loguru import logger

try:
    from ..utils.config_manager import ConfigManager
    from ..quantum.quantum_ai_processor import QuantumAIProcessor
except ImportError:
    from src.utils.config_manager import ConfigManager
    from src.quantum.quantum_ai_processor import QuantumAIProcessor


class BrainwaveType(Enum):
    """Types of brainwaves."""
    DELTA = "delta"      # 0.5-4 Hz - Deep sleep
    THETA = "theta"      # 4-8 Hz - Meditation, creativity
    ALPHA = "alpha"      # 8-13 Hz - Relaxed awareness
    BETA = "beta"        # 13-30 Hz - Active thinking
    GAMMA = "gamma"      # 30-100 Hz - High-level cognitive processing


class CognitiveState(Enum):
    """Cognitive states."""
    FOCUSED = "focused"
    RELAXED = "relaxed"
    CREATIVE = "creative"
    ANALYTICAL = "analytical"
    MEDITATIVE = "meditative"
    STRESSED = "stressed"
    DROWSY = "drowsy"
    ALERT = "alert"


class ThoughtPattern(Enum):
    """Types of thought patterns."""
    COMMAND = "command"
    QUESTION = "question"
    EMOTION = "emotion"
    MEMORY = "memory"
    IMAGINATION = "imagination"
    CALCULATION = "calculation"
    DECISION = "decision"


@dataclass
class BrainSignal:
    """Brain signal data structure."""
    signal_id: str
    electrode_position: str
    frequency: float
    amplitude: float
    brainwave_type: BrainwaveType
    timestamp: float
    quality_score: float
    metadata: Dict[str, Any]


@dataclass
class ThoughtCommand:
    """Thought-based command."""
    command_id: str
    thought_pattern: ThoughtPattern
    command_text: str
    confidence: float
    cognitive_state: CognitiveState
    brain_signals: List[BrainSignal]
    processing_time: float
    timestamp: float


@dataclass
class CognitiveProfile:
    """User's cognitive profile."""
    user_id: str
    baseline_patterns: Dict[BrainwaveType, float]
    cognitive_preferences: Dict[str, Any]
    learning_style: str
    attention_span: float
    stress_indicators: List[str]
    optimal_states: Dict[str, CognitiveState]
    created_at: float
    updated_at: float


class BrainInterface:
    """
    Advanced Brain-Computer Interface System.
    
    Features:
    - Real-time EEG signal processing
    - Thought pattern recognition and classification
    - Direct thought-to-command translation
    - Cognitive state monitoring and analysis
    - Neural feedback and biofeedback systems
    - Brain-computer synchronization
    """
    
    def __init__(self, config_manager: ConfigManager, quantum_processor: QuantumAIProcessor = None):
        self.config = config_manager
        self.quantum_processor = quantum_processor
        
        # Neural interface data
        self.brain_signals: deque = deque(maxlen=10000)
        self.thought_commands: List[ThoughtCommand] = []
        self.cognitive_profiles: Dict[str, CognitiveProfile] = {}
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        
        # EEG configuration
        self.sampling_rate = self.config.get("neural.sampling_rate", 256)  # Hz
        self.electrode_count = self.config.get("neural.electrode_count", 14)
        self.signal_quality_threshold = self.config.get("neural.quality_threshold", 0.7)
        
        # Processing parameters
        self.thought_detection_threshold = self.config.get("neural.thought_threshold", 0.8)
        self.cognitive_update_interval = self.config.get("neural.update_interval", 1.0)
        self.neural_learning_rate = self.config.get("neural.learning_rate", 0.01)
        
        # Signal processing
        self.signal_buffer: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.frequency_bands = self._initialize_frequency_bands()
        self.thought_patterns = self._initialize_thought_patterns()
        
        # Monitoring state
        self.monitoring_active = False
        self.monitor_thread: Optional[threading.Thread] = None
        
        # Performance metrics
        self.total_thoughts_processed = 0
        self.successful_commands = 0
        self.average_accuracy = 0.0
        
        logger.info("Brain Interface initialized")
    
    def _initialize_frequency_bands(self) -> Dict[BrainwaveType, Tuple[float, float]]:
        """Initialize frequency bands for brainwave classification."""
        return {
            BrainwaveType.DELTA: (0.5, 4.0),
            BrainwaveType.THETA: (4.0, 8.0),
            BrainwaveType.ALPHA: (8.0, 13.0),
            BrainwaveType.BETA: (13.0, 30.0),
            BrainwaveType.GAMMA: (30.0, 100.0)
        }
    
    def _initialize_thought_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Initialize thought pattern recognition templates."""
        return {
            "command_patterns": {
                "open": {"keywords": ["open", "start", "launch"], "confidence_boost": 0.2},
                "close": {"keywords": ["close", "stop", "end"], "confidence_boost": 0.2},
                "search": {"keywords": ["find", "search", "look"], "confidence_boost": 0.15},
                "create": {"keywords": ["create", "make", "new"], "confidence_boost": 0.15}
            },
            "emotion_patterns": {
                "positive": {"indicators": ["joy", "happy", "excited"], "brainwave": BrainwaveType.ALPHA},
                "negative": {"indicators": ["sad", "angry", "frustrated"], "brainwave": BrainwaveType.BETA},
                "calm": {"indicators": ["peaceful", "relaxed", "serene"], "brainwave": BrainwaveType.THETA}
            }
        }
    
    async def start_neural_monitoring(self, user_id: str) -> bool:
        """Start neural monitoring for a user."""
        try:
            if self.monitoring_active:
                logger.warning("Neural monitoring already active")
                return False
            
            # Initialize user session
            self.active_sessions[user_id] = {
                "start_time": time.time(),
                "signal_count": 0,
                "commands_detected": 0,
                "cognitive_state": CognitiveState.ALERT,
                "session_quality": 0.0
            }
            
            # Load or create cognitive profile
            if user_id not in self.cognitive_profiles:
                await self._create_cognitive_profile(user_id)
            
            # Start monitoring
            self.monitoring_active = True
            self.monitor_thread = threading.Thread(
                target=self._neural_monitoring_loop, 
                args=(user_id,), 
                daemon=True
            )
            self.monitor_thread.start()
            
            logger.info(f"Neural monitoring started for user: {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error starting neural monitoring: {e}")
            return False
    
    async def stop_neural_monitoring(self, user_id: str) -> Dict[str, Any]:
        """Stop neural monitoring and return session summary."""
        try:
            self.monitoring_active = False
            
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=5.0)
            
            # Generate session summary
            session_summary = {}
            if user_id in self.active_sessions:
                session = self.active_sessions[user_id]
                session_summary = {
                    "user_id": user_id,
                    "session_duration": time.time() - session["start_time"],
                    "signals_processed": session["signal_count"],
                    "commands_detected": session["commands_detected"],
                    "final_cognitive_state": session["cognitive_state"].value,
                    "average_session_quality": session["session_quality"],
                    "total_thoughts_processed": self.total_thoughts_processed,
                    "command_accuracy": self.successful_commands / max(self.total_thoughts_processed, 1)
                }
                
                # Clean up session
                del self.active_sessions[user_id]
            
            logger.info(f"Neural monitoring stopped for user: {user_id}")
            return session_summary
            
        except Exception as e:
            logger.error(f"Error stopping neural monitoring: {e}")
            return {}
    
    def _neural_monitoring_loop(self, user_id: str):
        """Main neural monitoring loop."""
        while self.monitoring_active:
            try:
                # Simulate EEG signal acquisition
                brain_signals = self._acquire_brain_signals()
                
                # Process signals
                for signal in brain_signals:
                    self.brain_signals.append(signal)
                    self.signal_buffer[signal.electrode_position].append(signal)
                    
                    # Update session stats
                    if user_id in self.active_sessions:
                        self.active_sessions[user_id]["signal_count"] += 1
                
                # Analyze cognitive state
                cognitive_state = self._analyze_cognitive_state(brain_signals)
                if user_id in self.active_sessions:
                    self.active_sessions[user_id]["cognitive_state"] = cognitive_state
                
                # Detect thought patterns
                asyncio.run(self._detect_thought_patterns(brain_signals, user_id))
                
                # Update cognitive profile
                asyncio.run(self._update_cognitive_profile(user_id, brain_signals, cognitive_state))
                
                time.sleep(1.0 / self.sampling_rate)  # Maintain sampling rate
                
            except Exception as e:
                logger.error(f"Error in neural monitoring loop: {e}")
                time.sleep(0.1)
    
    def _acquire_brain_signals(self) -> List[BrainSignal]:
        """Simulate EEG signal acquisition."""
        try:
            signals = []
            current_time = time.time()
            
            # Simulate signals from different electrode positions
            electrode_positions = ["Fp1", "Fp2", "F3", "F4", "C3", "C4", "P3", "P4", "O1", "O2", "F7", "F8", "T3", "T4"]
            
            for i, position in enumerate(electrode_positions[:self.electrode_count]):
                # Simulate realistic EEG signal
                base_frequency = 10.0 + np.random.normal(0, 2)  # Alpha wave base
                amplitude = 50.0 + np.random.normal(0, 10)  # Microvolts
                
                # Add some cognitive activity simulation
                if np.random.random() > 0.8:  # 20% chance of cognitive activity
                    amplitude *= 1.5  # Increase amplitude during activity
                    base_frequency += np.random.uniform(-2, 5)  # Frequency shift
                
                # Classify brainwave type
                brainwave_type = self._classify_brainwave(base_frequency)
                
                # Calculate signal quality
                quality_score = max(0.0, min(1.0, 0.8 + np.random.normal(0, 0.1)))
                
                signal = BrainSignal(
                    signal_id=f"signal_{int(current_time * 1000000)}_{i}",
                    electrode_position=position,
                    frequency=base_frequency,
                    amplitude=amplitude,
                    brainwave_type=brainwave_type,
                    timestamp=current_time,
                    quality_score=quality_score,
                    metadata={"simulated": True}
                )
                
                signals.append(signal)
            
            return signals
            
        except Exception as e:
            logger.error(f"Error acquiring brain signals: {e}")
            return []
    
    def _classify_brainwave(self, frequency: float) -> BrainwaveType:
        """Classify brainwave type based on frequency."""
        for brainwave_type, (min_freq, max_freq) in self.frequency_bands.items():
            if min_freq <= frequency < max_freq:
                return brainwave_type
        return BrainwaveType.BETA  # Default
    
    def _analyze_cognitive_state(self, brain_signals: List[BrainSignal]) -> CognitiveState:
        """Analyze current cognitive state from brain signals."""
        try:
            if not brain_signals:
                return CognitiveState.ALERT
            
            # Analyze brainwave distribution
            brainwave_counts = defaultdict(int)
            total_amplitude = 0
            
            for signal in brain_signals:
                brainwave_counts[signal.brainwave_type] += 1
                total_amplitude += signal.amplitude
            
            avg_amplitude = total_amplitude / len(brain_signals)
            
            # Determine cognitive state based on dominant brainwaves
            dominant_brainwave = max(brainwave_counts.items(), key=lambda x: x[1])[0]
            
            if dominant_brainwave == BrainwaveType.ALPHA and avg_amplitude < 60:
                return CognitiveState.RELAXED
            elif dominant_brainwave == BrainwaveType.BETA and avg_amplitude > 70:
                return CognitiveState.FOCUSED
            elif dominant_brainwave == BrainwaveType.THETA:
                return CognitiveState.CREATIVE
            elif dominant_brainwave == BrainwaveType.GAMMA:
                return CognitiveState.ANALYTICAL
            elif dominant_brainwave == BrainwaveType.DELTA:
                return CognitiveState.DROWSY
            elif avg_amplitude > 80:
                return CognitiveState.STRESSED
            else:
                return CognitiveState.ALERT
                
        except Exception as e:
            logger.error(f"Error analyzing cognitive state: {e}")
            return CognitiveState.ALERT

    async def _detect_thought_patterns(self, brain_signals: List[BrainSignal], user_id: str):
        """Detect and classify thought patterns from brain signals."""
        try:
            if not brain_signals:
                return

            # Analyze signal patterns for thought detection
            thought_indicators = self._extract_thought_indicators(brain_signals)

            # Check if thought pattern meets detection threshold
            if thought_indicators["confidence"] >= self.thought_detection_threshold:
                # Classify thought pattern
                thought_pattern = self._classify_thought_pattern(thought_indicators)

                # Convert to command if applicable
                if thought_pattern["type"] == ThoughtPattern.COMMAND:
                    command = await self._process_thought_command(
                        thought_pattern, brain_signals, user_id
                    )

                    if command:
                        self.thought_commands.append(command)
                        self.total_thoughts_processed += 1

                        # Update session stats
                        if user_id in self.active_sessions:
                            self.active_sessions[user_id]["commands_detected"] += 1

                        logger.info(f"Thought command detected: {command.command_text}")

        except Exception as e:
            logger.error(f"Error detecting thought patterns: {e}")

    def _extract_thought_indicators(self, brain_signals: List[BrainSignal]) -> Dict[str, Any]:
        """Extract indicators of thought activity from brain signals."""
        try:
            indicators = {
                "confidence": 0.0,
                "intensity": 0.0,
                "focus_level": 0.0,
                "pattern_type": "unknown",
                "dominant_frequency": 0.0,
                "signal_quality": 0.0
            }

            if not brain_signals:
                return indicators

            # Calculate average signal quality
            indicators["signal_quality"] = sum(s.quality_score for s in brain_signals) / len(brain_signals)

            # Calculate signal intensity
            indicators["intensity"] = sum(s.amplitude for s in brain_signals) / len(brain_signals)

            # Find dominant frequency
            frequencies = [s.frequency for s in brain_signals]
            indicators["dominant_frequency"] = sum(frequencies) / len(frequencies)

            # Calculate focus level based on beta waves
            beta_signals = [s for s in brain_signals if s.brainwave_type == BrainwaveType.BETA]
            if beta_signals:
                beta_intensity = sum(s.amplitude for s in beta_signals) / len(beta_signals)
                indicators["focus_level"] = min(1.0, beta_intensity / 100.0)

            # Calculate confidence based on signal consistency and quality
            if indicators["signal_quality"] > self.signal_quality_threshold:
                base_confidence = indicators["signal_quality"] * 0.7
                intensity_bonus = min(0.3, indicators["intensity"] / 200.0)
                indicators["confidence"] = base_confidence + intensity_bonus

            return indicators

        except Exception as e:
            logger.error(f"Error extracting thought indicators: {e}")
            return {"confidence": 0.0}

    def _classify_thought_pattern(self, thought_indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Classify the type of thought pattern."""
        try:
            pattern = {
                "type": ThoughtPattern.COMMAND,
                "subtype": "unknown",
                "confidence": thought_indicators["confidence"],
                "characteristics": {}
            }

            dominant_freq = thought_indicators["dominant_frequency"]
            focus_level = thought_indicators["focus_level"]
            intensity = thought_indicators["intensity"]

            # Classify based on brainwave characteristics
            if 13 <= dominant_freq <= 30 and focus_level > 0.7:
                # High beta activity suggests command/decision making
                pattern["type"] = ThoughtPattern.COMMAND
                pattern["subtype"] = "action_command"

            elif 8 <= dominant_freq <= 13 and intensity < 60:
                # Alpha waves suggest relaxed thinking or memory access
                pattern["type"] = ThoughtPattern.MEMORY
                pattern["subtype"] = "memory_recall"

            elif 4 <= dominant_freq <= 8:
                # Theta waves suggest creativity or deep thinking
                pattern["type"] = ThoughtPattern.IMAGINATION
                pattern["subtype"] = "creative_thinking"

            elif dominant_freq > 30:
                # Gamma waves suggest high-level cognitive processing
                pattern["type"] = ThoughtPattern.CALCULATION
                pattern["subtype"] = "analytical_thinking"

            elif intensity > 80:
                # High intensity might indicate emotional thinking
                pattern["type"] = ThoughtPattern.EMOTION
                pattern["subtype"] = "emotional_response"

            # Add characteristics
            pattern["characteristics"] = {
                "dominant_frequency": dominant_freq,
                "focus_level": focus_level,
                "intensity": intensity,
                "complexity": self._calculate_pattern_complexity(thought_indicators)
            }

            return pattern

        except Exception as e:
            logger.error(f"Error classifying thought pattern: {e}")
            return {"type": ThoughtPattern.COMMAND, "confidence": 0.0}

    def _calculate_pattern_complexity(self, thought_indicators: Dict[str, Any]) -> float:
        """Calculate the complexity of the thought pattern."""
        try:
            # Simple complexity calculation based on multiple factors
            complexity = 0.0

            # Frequency diversity contributes to complexity
            complexity += min(0.4, thought_indicators.get("dominant_frequency", 0) / 50.0)

            # Intensity variation contributes to complexity
            complexity += min(0.3, thought_indicators.get("intensity", 0) / 100.0)

            # Focus level contributes to complexity
            complexity += min(0.3, thought_indicators.get("focus_level", 0))

            return min(1.0, complexity)

        except Exception as e:
            logger.error(f"Error calculating pattern complexity: {e}")
            return 0.5

    async def _process_thought_command(self, thought_pattern: Dict[str, Any],
                                     brain_signals: List[BrainSignal],
                                     user_id: str) -> Optional[ThoughtCommand]:
        """Process thought pattern into executable command."""
        try:
            # Use quantum processing for enhanced thought interpretation
            if self.quantum_processor:
                quantum_result = await self.quantum_processor.quantum_process(
                    input_data={
                        "thought_pattern": thought_pattern,
                        "brain_signals": [asdict(s) for s in brain_signals],
                        "user_id": user_id
                    },
                    algorithm="quantum_neural_network",
                    qubits_requested=8
                )

                if quantum_result.success:
                    # Extract command from quantum processing result
                    command_text = self._extract_command_from_quantum_result(quantum_result)
                    confidence_boost = min(0.2, quantum_result.quantum_advantage / 10.0)
                else:
                    command_text = self._extract_command_classical(thought_pattern)
                    confidence_boost = 0.0
            else:
                command_text = self._extract_command_classical(thought_pattern)
                confidence_boost = 0.0

            # Create thought command
            if command_text:
                command = ThoughtCommand(
                    command_id=f"thought_cmd_{int(time.time() * 1000000)}",
                    thought_pattern=thought_pattern["type"],
                    command_text=command_text,
                    confidence=min(1.0, thought_pattern["confidence"] + confidence_boost),
                    cognitive_state=self._get_current_cognitive_state(user_id),
                    brain_signals=brain_signals,
                    processing_time=time.time(),
                    timestamp=time.time()
                )

                # Update success metrics
                if command.confidence > 0.8:
                    self.successful_commands += 1

                return command

            return None

        except Exception as e:
            logger.error(f"Error processing thought command: {e}")
            return None

    def _extract_command_from_quantum_result(self, quantum_result) -> str:
        """Extract command text from quantum processing result."""
        try:
            if hasattr(quantum_result, 'classical_result') and quantum_result.classical_result:
                result = quantum_result.classical_result

                if isinstance(result, dict) and "processed_output" in result:
                    output = result["processed_output"]

                    # Map quantum output to commands
                    if len(output) >= 2:
                        if output[0] > 0.8:
                            return "open application"
                        elif output[1] > 0.8:
                            return "close application"
                        elif output[0] > 0.6:
                            return "search for information"
                        elif output[1] > 0.6:
                            return "create new file"
                        else:
                            return "show system status"

            return "unknown command"

        except Exception as e:
            logger.error(f"Error extracting command from quantum result: {e}")
            return "unknown command"

    def _extract_command_classical(self, thought_pattern: Dict[str, Any]) -> str:
        """Extract command using classical pattern matching."""
        try:
            subtype = thought_pattern.get("subtype", "unknown")
            characteristics = thought_pattern.get("characteristics", {})

            # Map thought patterns to commands
            if subtype == "action_command":
                focus_level = characteristics.get("focus_level", 0)
                if focus_level > 0.8:
                    return "execute primary action"
                elif focus_level > 0.6:
                    return "open application"
                else:
                    return "show menu"

            elif subtype == "memory_recall":
                return "search recent files"

            elif subtype == "creative_thinking":
                return "open creative tools"

            elif subtype == "analytical_thinking":
                return "show analytics dashboard"

            elif subtype == "emotional_response":
                intensity = characteristics.get("intensity", 0)
                if intensity > 90:
                    return "emergency assistance"
                else:
                    return "adjust environment"

            return "show help"

        except Exception as e:
            logger.error(f"Error extracting classical command: {e}")
            return "unknown command"

    def _get_current_cognitive_state(self, user_id: str) -> CognitiveState:
        """Get current cognitive state for user."""
        if user_id in self.active_sessions:
            return self.active_sessions[user_id]["cognitive_state"]
        return CognitiveState.ALERT

    async def _create_cognitive_profile(self, user_id: str):
        """Create a new cognitive profile for user."""
        try:
            # Initialize baseline patterns
            baseline_patterns = {
                BrainwaveType.DELTA: 10.0,
                BrainwaveType.THETA: 15.0,
                BrainwaveType.ALPHA: 25.0,
                BrainwaveType.BETA: 35.0,
                BrainwaveType.GAMMA: 15.0
            }

            profile = CognitiveProfile(
                user_id=user_id,
                baseline_patterns=baseline_patterns,
                cognitive_preferences={
                    "preferred_interaction_style": "direct",
                    "response_speed": "medium",
                    "feedback_level": "moderate"
                },
                learning_style="visual",
                attention_span=300.0,  # 5 minutes default
                stress_indicators=["high_beta", "low_alpha", "irregular_patterns"],
                optimal_states={
                    "work": CognitiveState.FOCUSED,
                    "creativity": CognitiveState.CREATIVE,
                    "relaxation": CognitiveState.RELAXED
                },
                created_at=time.time(),
                updated_at=time.time()
            )

            self.cognitive_profiles[user_id] = profile
            logger.info(f"Cognitive profile created for user: {user_id}")

        except Exception as e:
            logger.error(f"Error creating cognitive profile: {e}")

    async def _update_cognitive_profile(self, user_id: str, brain_signals: List[BrainSignal],
                                      cognitive_state: CognitiveState):
        """Update user's cognitive profile based on new data."""
        try:
            if user_id not in self.cognitive_profiles:
                return

            profile = self.cognitive_profiles[user_id]

            # Update baseline patterns with exponential moving average
            if brain_signals:
                brainwave_averages = defaultdict(list)

                for signal in brain_signals:
                    brainwave_averages[signal.brainwave_type].append(signal.amplitude)

                for brainwave_type, amplitudes in brainwave_averages.items():
                    if amplitudes:
                        current_avg = sum(amplitudes) / len(amplitudes)
                        old_baseline = profile.baseline_patterns.get(brainwave_type, current_avg)

                        # Exponential moving average with learning rate
                        new_baseline = (1 - self.neural_learning_rate) * old_baseline + \
                                     self.neural_learning_rate * current_avg

                        profile.baseline_patterns[brainwave_type] = new_baseline

            # Update preferences based on cognitive state patterns
            self._update_cognitive_preferences(profile, cognitive_state)

            profile.updated_at = time.time()

        except Exception as e:
            logger.error(f"Error updating cognitive profile: {e}")

    def _update_cognitive_preferences(self, profile: CognitiveProfile,
                                    cognitive_state: CognitiveState):
        """Update cognitive preferences based on observed patterns."""
        try:
            # Track state preferences
            if "state_history" not in profile.cognitive_preferences:
                profile.cognitive_preferences["state_history"] = defaultdict(int)

            profile.cognitive_preferences["state_history"][cognitive_state.value] += 1

            # Determine preferred interaction style
            state_counts = profile.cognitive_preferences["state_history"]
            most_common_state = max(state_counts.items(), key=lambda x: x[1])[0]

            if most_common_state in ["focused", "analytical"]:
                profile.cognitive_preferences["preferred_interaction_style"] = "direct"
                profile.cognitive_preferences["response_speed"] = "fast"
            elif most_common_state in ["creative", "relaxed"]:
                profile.cognitive_preferences["preferred_interaction_style"] = "gentle"
                profile.cognitive_preferences["response_speed"] = "medium"
            elif most_common_state == "stressed":
                profile.cognitive_preferences["preferred_interaction_style"] = "supportive"
                profile.cognitive_preferences["response_speed"] = "slow"

        except Exception as e:
            logger.error(f"Error updating cognitive preferences: {e}")

    async def get_thought_commands(self, user_id: str = None, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent thought commands."""
        try:
            commands = self.thought_commands

            if user_id:
                # Filter by user if specified (would need user tracking in commands)
                pass

            # Sort by timestamp (newest first)
            commands.sort(key=lambda x: x.timestamp, reverse=True)

            return [asdict(cmd) for cmd in commands[:limit]]

        except Exception as e:
            logger.error(f"Error getting thought commands: {e}")
            return []

    async def get_cognitive_analysis(self, user_id: str) -> Dict[str, Any]:
        """Get comprehensive cognitive analysis for user."""
        try:
            analysis = {
                "user_id": user_id,
                "current_session": {},
                "cognitive_profile": {},
                "recent_activity": {},
                "recommendations": []
            }

            # Current session info
            if user_id in self.active_sessions:
                session = self.active_sessions[user_id]
                analysis["current_session"] = {
                    "active": True,
                    "duration": time.time() - session["start_time"],
                    "cognitive_state": session["cognitive_state"].value,
                    "signals_processed": session["signal_count"],
                    "commands_detected": session["commands_detected"],
                    "session_quality": session["session_quality"]
                }
            else:
                analysis["current_session"]["active"] = False

            # Cognitive profile
            if user_id in self.cognitive_profiles:
                profile = self.cognitive_profiles[user_id]
                analysis["cognitive_profile"] = {
                    "baseline_patterns": profile.baseline_patterns,
                    "learning_style": profile.learning_style,
                    "attention_span": profile.attention_span,
                    "preferred_interaction": profile.cognitive_preferences.get("preferred_interaction_style", "direct"),
                    "profile_age": time.time() - profile.created_at
                }

            # Recent activity analysis
            recent_signals = list(self.brain_signals)[-100:]  # Last 100 signals
            if recent_signals:
                analysis["recent_activity"] = {
                    "signal_count": len(recent_signals),
                    "average_quality": sum(s.quality_score for s in recent_signals) / len(recent_signals),
                    "dominant_brainwave": self._get_dominant_brainwave(recent_signals),
                    "activity_level": sum(s.amplitude for s in recent_signals) / len(recent_signals)
                }

            # Generate recommendations
            analysis["recommendations"] = self._generate_cognitive_recommendations(analysis)

            return analysis

        except Exception as e:
            logger.error(f"Error getting cognitive analysis: {e}")
            return {}

    def _get_dominant_brainwave(self, signals: List[BrainSignal]) -> str:
        """Get dominant brainwave type from signals."""
        try:
            brainwave_counts = defaultdict(int)
            for signal in signals:
                brainwave_counts[signal.brainwave_type] += 1

            if brainwave_counts:
                dominant = max(brainwave_counts.items(), key=lambda x: x[1])[0]
                return dominant.value

            return "unknown"

        except Exception as e:
            logger.error(f"Error getting dominant brainwave: {e}")
            return "unknown"

    def _generate_cognitive_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate cognitive optimization recommendations."""
        try:
            recommendations = []

            # Session quality recommendations
            session = analysis.get("current_session", {})
            if session.get("active", False):
                session_quality = session.get("session_quality", 0)
                if session_quality < 0.6:
                    recommendations.append("Consider taking a break to improve signal quality")

                duration = session.get("duration", 0)
                if duration > 3600:  # 1 hour
                    recommendations.append("Long session detected - consider rest for optimal performance")

            # Cognitive state recommendations
            recent_activity = analysis.get("recent_activity", {})
            dominant_brainwave = recent_activity.get("dominant_brainwave", "")

            if dominant_brainwave == "beta" and recent_activity.get("activity_level", 0) > 80:
                recommendations.append("High stress indicators detected - consider relaxation techniques")
            elif dominant_brainwave == "delta":
                recommendations.append("Low alertness detected - consider stimulating activities")
            elif dominant_brainwave == "theta":
                recommendations.append("Creative state detected - optimal time for brainstorming")

            # Profile-based recommendations
            profile = analysis.get("cognitive_profile", {})
            attention_span = profile.get("attention_span", 300)
            if attention_span < 180:  # Less than 3 minutes
                recommendations.append("Short attention span detected - consider focus training exercises")

            return recommendations

        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return []

    async def get_interface_status(self) -> Dict[str, Any]:
        """Get brain interface system status."""
        try:
            return {
                "monitoring_active": self.monitoring_active,
                "active_sessions": len(self.active_sessions),
                "total_signals_collected": len(self.brain_signals),
                "total_thoughts_processed": self.total_thoughts_processed,
                "successful_commands": self.successful_commands,
                "command_accuracy": self.successful_commands / max(self.total_thoughts_processed, 1),
                "average_accuracy": self.average_accuracy,
                "electrode_count": self.electrode_count,
                "sampling_rate": self.sampling_rate,
                "signal_quality_threshold": self.signal_quality_threshold,
                "thought_detection_threshold": self.thought_detection_threshold,
                "cognitive_profiles": len(self.cognitive_profiles),
                "quantum_integration": self.quantum_processor is not None
            }

        except Exception as e:
            logger.error(f"Error getting interface status: {e}")
            return {}


# Factory functions
def create_brain_signal(electrode_position: str, frequency: float, amplitude: float) -> BrainSignal:
    """Create a new brain signal."""
    return BrainSignal(
        signal_id=f"signal_{int(time.time() * 1000000)}",
        electrode_position=electrode_position,
        frequency=frequency,
        amplitude=amplitude,
        brainwave_type=BrainwaveType.ALPHA,  # Default
        timestamp=time.time(),
        quality_score=0.8,
        metadata={}
    )

def create_cognitive_profile(user_id: str) -> CognitiveProfile:
    """Create a new cognitive profile."""
    return CognitiveProfile(
        user_id=user_id,
        baseline_patterns={
            BrainwaveType.DELTA: 10.0,
            BrainwaveType.THETA: 15.0,
            BrainwaveType.ALPHA: 25.0,
            BrainwaveType.BETA: 35.0,
            BrainwaveType.GAMMA: 15.0
        },
        cognitive_preferences={},
        learning_style="visual",
        attention_span=300.0,
        stress_indicators=[],
        optimal_states={},
        created_at=time.time(),
        updated_at=time.time()
    )
