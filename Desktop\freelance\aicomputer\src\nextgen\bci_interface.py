"""
Brain-Computer Interface System

This module provides brain-computer interface capabilities including neural signal
processing, thought-to-command translation, cognitive state monitoring, and
accessibility enhancements for next-generation human-computer interaction.
"""

import numpy as np
import logging
import threading
import time
import json
import asyncio
from typing import Dict, List, Any, Optional, Tuple, Union, Callable
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
import hashlib
from collections import deque, defaultdict
import scipy.signal as signal
from scipy.fft import fft, fftfreq

logger = logging.getLogger(__name__)

@dataclass
class NeuralSignal:
    """Neural signal data structure"""
    signal_id: str
    channel: int
    data: List[float]
    sampling_rate: float
    timestamp: str
    signal_type: str  # 'EEG', 'EMG', 'EOG', 'fNIRS'
    quality: float
    artifacts: List[str]

@dataclass
class BrainState:
    """Brain state representation"""
    state_id: str
    attention_level: float
    meditation_level: float
    cognitive_load: float
    emotional_state: str
    fatigue_level: float
    confidence: float
    timestamp: str

@dataclass
class ThoughtCommand:
    """Thought-based command structure"""
    command_id: str
    intent: str
    confidence: float
    parameters: Dict[str, Any]
    processing_time: float
    neural_patterns: List[str]
    timestamp: str

@dataclass
class CognitiveProfile:
    """User cognitive profile"""
    user_id: str
    baseline_patterns: Dict[str, List[float]]
    learned_commands: Dict[str, Dict[str, Any]]
    adaptation_parameters: Dict[str, float]
    training_sessions: int
    last_calibration: str

class NeuralSignalProcessor:
    """Neural signal processing and analysis"""
    
    def __init__(self, sampling_rate: float = 256.0):
        self.sampling_rate = sampling_rate
        self.signal_buffer = deque(maxlen=int(sampling_rate * 10))  # 10 seconds buffer
        self.filters = self._initialize_filters()
        self.artifact_detector = ArtifactDetector()
        
    def _initialize_filters(self) -> Dict[str, Any]:
        """Initialize signal processing filters"""
        nyquist = self.sampling_rate / 2
        
        filters = {
            # Band-pass filters for different brain waves
            "delta": signal.butter(4, [0.5, 4], btype='band', fs=self.sampling_rate),
            "theta": signal.butter(4, [4, 8], btype='band', fs=self.sampling_rate),
            "alpha": signal.butter(4, [8, 13], btype='band', fs=self.sampling_rate),
            "beta": signal.butter(4, [13, 30], btype='band', fs=self.sampling_rate),
            "gamma": signal.butter(4, [30, 100], btype='band', fs=self.sampling_rate),
            
            # Notch filter for power line interference
            "notch_50": signal.iirnotch(50, 30, fs=self.sampling_rate),
            "notch_60": signal.iirnotch(60, 30, fs=self.sampling_rate),
            
            # High-pass filter for baseline drift
            "highpass": signal.butter(4, 0.5, btype='high', fs=self.sampling_rate)
        }
        
        return filters
    
    def process_signal(self, neural_signal: NeuralSignal) -> Dict[str, Any]:
        """Process neural signal and extract features"""
        try:
            # Convert to numpy array
            signal_data = np.array(neural_signal.data)
            
            # Apply preprocessing
            cleaned_signal = self._preprocess_signal(signal_data)
            
            # Extract features
            features = self._extract_features(cleaned_signal)
            
            # Detect artifacts
            artifacts = self.artifact_detector.detect_artifacts(cleaned_signal)
            
            # Calculate signal quality
            quality = self._calculate_signal_quality(cleaned_signal, artifacts)
            
            return {
                "signal_id": neural_signal.signal_id,
                "processed_signal": cleaned_signal.tolist(),
                "features": features,
                "artifacts": artifacts,
                "quality": quality,
                "processing_time": time.time()
            }
            
        except Exception as e:
            logger.error(f"Error processing neural signal: {e}")
            return {"error": str(e)}
    
    def _preprocess_signal(self, signal_data: np.ndarray) -> np.ndarray:
        """Preprocess neural signal"""
        # Apply high-pass filter to remove baseline drift
        b, a = self.filters["highpass"]
        filtered_signal = signal.filtfilt(b, a, signal_data)
        
        # Apply notch filters for power line interference
        b, a = self.filters["notch_50"]
        filtered_signal = signal.filtfilt(b, a, filtered_signal)
        
        b, a = self.filters["notch_60"]
        filtered_signal = signal.filtfilt(b, a, filtered_signal)
        
        # Normalize signal
        normalized_signal = (filtered_signal - np.mean(filtered_signal)) / np.std(filtered_signal)
        
        return normalized_signal
    
    def _extract_features(self, signal_data: np.ndarray) -> Dict[str, Any]:
        """Extract features from neural signal"""
        features = {}
        
        # Time domain features
        features["mean"] = float(np.mean(signal_data))
        features["std"] = float(np.std(signal_data))
        features["variance"] = float(np.var(signal_data))
        features["rms"] = float(np.sqrt(np.mean(signal_data**2)))
        features["peak_to_peak"] = float(np.ptp(signal_data))
        
        # Frequency domain features
        fft_data = fft(signal_data)
        freqs = fftfreq(len(signal_data), 1/self.sampling_rate)
        power_spectrum = np.abs(fft_data)**2
        
        # Band power features
        for band_name, (b, a) in self.filters.items():
            if band_name.startswith("notch") or band_name == "highpass":
                continue
                
            # Filter signal for specific band
            band_signal = signal.filtfilt(b, a, signal_data)
            band_power = np.mean(band_signal**2)
            features[f"{band_name}_power"] = float(band_power)
        
        # Spectral features
        features["spectral_centroid"] = float(np.sum(freqs[:len(freqs)//2] * power_spectrum[:len(power_spectrum)//2]) / 
                                            np.sum(power_spectrum[:len(power_spectrum)//2]))
        features["spectral_bandwidth"] = float(np.sqrt(np.sum(((freqs[:len(freqs)//2] - features["spectral_centroid"])**2) * 
                                                             power_spectrum[:len(power_spectrum)//2]) / 
                                                     np.sum(power_spectrum[:len(power_spectrum)//2])))
        
        return features
    
    def _calculate_signal_quality(self, signal_data: np.ndarray, artifacts: List[str]) -> float:
        """Calculate signal quality score"""
        quality_score = 1.0
        
        # Reduce quality based on artifacts
        quality_score -= len(artifacts) * 0.1
        
        # Check signal-to-noise ratio
        signal_power = np.var(signal_data)
        if signal_power < 0.1:  # Very low signal
            quality_score -= 0.3
        elif signal_power > 10:  # Very high signal (likely artifacts)
            quality_score -= 0.2
        
        # Check for saturation
        if np.max(np.abs(signal_data)) > 3:  # 3 standard deviations
            quality_score -= 0.2
        
        return max(0.0, min(1.0, quality_score))

class ArtifactDetector:
    """Detect artifacts in neural signals"""
    
    def __init__(self):
        self.artifact_thresholds = {
            "eye_blink": 100,
            "muscle_artifact": 50,
            "electrode_pop": 200,
            "movement": 75
        }
    
    def detect_artifacts(self, signal_data: np.ndarray) -> List[str]:
        """Detect various types of artifacts"""
        artifacts = []
        
        # Eye blink detection (high amplitude, short duration)
        if self._detect_eye_blinks(signal_data):
            artifacts.append("eye_blink")
        
        # Muscle artifact detection (high frequency content)
        if self._detect_muscle_artifacts(signal_data):
            artifacts.append("muscle_artifact")
        
        # Electrode pop detection (sudden amplitude changes)
        if self._detect_electrode_pops(signal_data):
            artifacts.append("electrode_pop")
        
        # Movement artifact detection (low frequency, high amplitude)
        if self._detect_movement_artifacts(signal_data):
            artifacts.append("movement")
        
        return artifacts
    
    def _detect_eye_blinks(self, signal_data: np.ndarray) -> bool:
        """Detect eye blink artifacts"""
        # Simple threshold-based detection
        return np.max(np.abs(signal_data)) > self.artifact_thresholds["eye_blink"]
    
    def _detect_muscle_artifacts(self, signal_data: np.ndarray) -> bool:
        """Detect muscle artifacts"""
        # High frequency power detection
        high_freq_power = np.mean(signal_data[signal_data > np.percentile(signal_data, 90)]**2)
        return high_freq_power > self.artifact_thresholds["muscle_artifact"]
    
    def _detect_electrode_pops(self, signal_data: np.ndarray) -> bool:
        """Detect electrode pop artifacts"""
        # Sudden amplitude changes
        diff_signal = np.diff(signal_data)
        return np.max(np.abs(diff_signal)) > self.artifact_thresholds["electrode_pop"]
    
    def _detect_movement_artifacts(self, signal_data: np.ndarray) -> bool:
        """Detect movement artifacts"""
        # Low frequency, high amplitude detection
        low_freq_power = np.mean(signal_data[:len(signal_data)//4]**2)
        return low_freq_power > self.artifact_thresholds["movement"]

class CognitiveStateMonitor:
    """Monitor cognitive states from neural signals"""
    
    def __init__(self):
        self.state_history = deque(maxlen=100)
        self.baseline_states = {}
        self.state_classifiers = self._initialize_classifiers()
    
    def _initialize_classifiers(self) -> Dict[str, Any]:
        """Initialize cognitive state classifiers"""
        # Simplified classifiers - in real implementation would use trained ML models
        return {
            "attention": {"alpha_threshold": 0.3, "beta_threshold": 0.4},
            "meditation": {"theta_threshold": 0.4, "alpha_threshold": 0.5},
            "cognitive_load": {"beta_threshold": 0.6, "gamma_threshold": 0.3},
            "fatigue": {"delta_threshold": 0.4, "attention_threshold": 0.2}
        }
    
    def analyze_cognitive_state(self, features: Dict[str, Any]) -> BrainState:
        """Analyze cognitive state from neural features"""
        try:
            # Extract relevant features
            alpha_power = features.get("alpha_power", 0.0)
            beta_power = features.get("beta_power", 0.0)
            theta_power = features.get("theta_power", 0.0)
            delta_power = features.get("delta_power", 0.0)
            gamma_power = features.get("gamma_power", 0.0)
            
            # Calculate cognitive metrics
            attention_level = self._calculate_attention(alpha_power, beta_power)
            meditation_level = self._calculate_meditation(theta_power, alpha_power)
            cognitive_load = self._calculate_cognitive_load(beta_power, gamma_power)
            fatigue_level = self._calculate_fatigue(delta_power, attention_level)
            emotional_state = self._classify_emotional_state(features)
            
            # Calculate overall confidence
            confidence = self._calculate_confidence(features)
            
            brain_state = BrainState(
                state_id=hashlib.md5(f"{time.time()}".encode()).hexdigest(),
                attention_level=attention_level,
                meditation_level=meditation_level,
                cognitive_load=cognitive_load,
                emotional_state=emotional_state,
                fatigue_level=fatigue_level,
                confidence=confidence,
                timestamp=datetime.now().isoformat()
            )
            
            self.state_history.append(brain_state)
            return brain_state
            
        except Exception as e:
            logger.error(f"Error analyzing cognitive state: {e}")
            return BrainState("error", 0.0, 0.0, 0.0, "unknown", 0.0, 0.0, datetime.now().isoformat())
    
    def _calculate_attention(self, alpha_power: float, beta_power: float) -> float:
        """Calculate attention level"""
        # High beta and moderate alpha indicate focused attention
        attention_score = (beta_power * 0.7 + (1 - alpha_power) * 0.3)
        return max(0.0, min(1.0, attention_score))
    
    def _calculate_meditation(self, theta_power: float, alpha_power: float) -> float:
        """Calculate meditation level"""
        # High theta and alpha indicate meditative state
        meditation_score = (theta_power * 0.6 + alpha_power * 0.4)
        return max(0.0, min(1.0, meditation_score))
    
    def _calculate_cognitive_load(self, beta_power: float, gamma_power: float) -> float:
        """Calculate cognitive load"""
        # High beta and gamma indicate high cognitive load
        load_score = (beta_power * 0.6 + gamma_power * 0.4)
        return max(0.0, min(1.0, load_score))
    
    def _calculate_fatigue(self, delta_power: float, attention_level: float) -> float:
        """Calculate fatigue level"""
        # High delta and low attention indicate fatigue
        fatigue_score = (delta_power * 0.7 + (1 - attention_level) * 0.3)
        return max(0.0, min(1.0, fatigue_score))
    
    def _classify_emotional_state(self, features: Dict[str, Any]) -> str:
        """Classify emotional state"""
        # Simplified emotional state classification
        alpha_power = features.get("alpha_power", 0.0)
        beta_power = features.get("beta_power", 0.0)
        
        if beta_power > 0.6:
            return "stressed"
        elif alpha_power > 0.5:
            return "relaxed"
        elif beta_power > 0.4:
            return "focused"
        else:
            return "neutral"
    
    def _calculate_confidence(self, features: Dict[str, Any]) -> float:
        """Calculate confidence in state estimation"""
        # Base confidence on signal quality and feature consistency
        signal_quality = features.get("quality", 0.5)
        feature_variance = np.var(list(features.values())[:5])  # First 5 features
        
        confidence = signal_quality * (1 - min(feature_variance, 0.5))
        return max(0.0, min(1.0, confidence))

class ThoughtCommandDecoder:
    """Decode thought patterns into commands"""
    
    def __init__(self):
        self.command_patterns = self._initialize_command_patterns()
        self.decoder_models = {}
        self.command_history = deque(maxlen=50)
    
    def _initialize_command_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Initialize thought command patterns"""
        return {
            "move_cursor": {
                "pattern": {"beta_power": [0.4, 0.8], "gamma_power": [0.2, 0.6]},
                "confidence_threshold": 0.7
            },
            "click": {
                "pattern": {"beta_power": [0.6, 1.0], "alpha_power": [0.1, 0.4]},
                "confidence_threshold": 0.8
            },
            "scroll": {
                "pattern": {"theta_power": [0.3, 0.7], "alpha_power": [0.2, 0.6]},
                "confidence_threshold": 0.6
            },
            "type_text": {
                "pattern": {"beta_power": [0.5, 0.9], "gamma_power": [0.3, 0.7]},
                "confidence_threshold": 0.7
            },
            "voice_command": {
                "pattern": {"alpha_power": [0.4, 0.8], "beta_power": [0.3, 0.7]},
                "confidence_threshold": 0.6
            }
        }
    
    def decode_thought_command(self, brain_state: BrainState, features: Dict[str, Any]) -> Optional[ThoughtCommand]:
        """Decode thought patterns into commands"""
        try:
            best_match = None
            best_confidence = 0.0
            
            # Check each command pattern
            for command_name, pattern_info in self.command_patterns.items():
                confidence = self._match_pattern(features, pattern_info["pattern"])
                
                if confidence > pattern_info["confidence_threshold"] and confidence > best_confidence:
                    best_match = command_name
                    best_confidence = confidence
            
            if best_match:
                # Extract command parameters
                parameters = self._extract_command_parameters(best_match, features, brain_state)
                
                thought_command = ThoughtCommand(
                    command_id=hashlib.md5(f"{time.time()}_{best_match}".encode()).hexdigest(),
                    intent=best_match,
                    confidence=best_confidence,
                    parameters=parameters,
                    processing_time=time.time(),
                    neural_patterns=[best_match],
                    timestamp=datetime.now().isoformat()
                )
                
                self.command_history.append(thought_command)
                return thought_command
            
            return None
            
        except Exception as e:
            logger.error(f"Error decoding thought command: {e}")
            return None
    
    def _match_pattern(self, features: Dict[str, Any], pattern: Dict[str, List[float]]) -> float:
        """Match features against command pattern"""
        total_score = 0.0
        pattern_count = 0
        
        for feature_name, (min_val, max_val) in pattern.items():
            if feature_name in features:
                feature_value = features[feature_name]
                
                # Calculate how well the feature matches the pattern
                if min_val <= feature_value <= max_val:
                    # Perfect match
                    score = 1.0
                else:
                    # Calculate distance from range
                    if feature_value < min_val:
                        distance = min_val - feature_value
                    else:
                        distance = feature_value - max_val
                    
                    # Convert distance to score (exponential decay)
                    score = np.exp(-distance * 2)
                
                total_score += score
                pattern_count += 1
        
        return total_score / pattern_count if pattern_count > 0 else 0.0
    
    def _extract_command_parameters(self, command: str, features: Dict[str, Any], brain_state: BrainState) -> Dict[str, Any]:
        """Extract parameters for specific commands"""
        parameters = {}
        
        if command == "move_cursor":
            # Use attention level to determine movement speed
            parameters["speed"] = brain_state.attention_level
            parameters["direction"] = "forward"  # Simplified
            
        elif command == "click":
            # Use cognitive load to determine click type
            if brain_state.cognitive_load > 0.7:
                parameters["click_type"] = "double"
            else:
                parameters["click_type"] = "single"
                
        elif command == "scroll":
            # Use meditation level to determine scroll direction
            if brain_state.meditation_level > 0.5:
                parameters["direction"] = "up"
            else:
                parameters["direction"] = "down"
            parameters["speed"] = brain_state.attention_level
            
        elif command == "type_text":
            # Use emotional state to determine text mode
            parameters["mode"] = brain_state.emotional_state
            parameters["speed"] = brain_state.attention_level
            
        elif command == "voice_command":
            # Prepare for voice input
            parameters["listening_mode"] = True
            parameters["sensitivity"] = brain_state.attention_level
        
        return parameters

class NeuralProcessor:
    """Main neural processing unit"""
    
    def __init__(self, system_controller=None):
        self.system_controller = system_controller
        self.signal_processor = NeuralSignalProcessor()
        self.cognitive_monitor = CognitiveStateMonitor()
        self.command_decoder = ThoughtCommandDecoder()
        
        # Processing configuration
        self.config = {
            "sampling_rate": 256.0,
            "processing_enabled": True,
            "command_decoding": True,
            "cognitive_monitoring": True,
            "real_time_processing": True
        }
        
        # Performance metrics
        self.metrics = {
            "signals_processed": 0,
            "commands_decoded": 0,
            "average_processing_time": 0.0,
            "average_signal_quality": 0.0
        }
        
        logger.info("Neural Processor initialized")
    
    async def process_neural_input(self, neural_signal: NeuralSignal) -> Dict[str, Any]:
        """Process neural input and extract commands/states"""
        try:
            start_time = time.time()
            
            # Process neural signal
            signal_result = self.signal_processor.process_signal(neural_signal)
            
            if "error" in signal_result:
                return signal_result
            
            # Analyze cognitive state
            brain_state = self.cognitive_monitor.analyze_cognitive_state(signal_result["features"])
            
            # Decode thought commands
            thought_command = None
            if self.config["command_decoding"]:
                thought_command = self.command_decoder.decode_thought_command(
                    brain_state, signal_result["features"]
                )
            
            # Update metrics
            processing_time = time.time() - start_time
            self.metrics["signals_processed"] += 1
            self.metrics["average_processing_time"] = (
                (self.metrics["average_processing_time"] * (self.metrics["signals_processed"] - 1) + processing_time) /
                self.metrics["signals_processed"]
            )
            self.metrics["average_signal_quality"] = (
                (self.metrics["average_signal_quality"] * (self.metrics["signals_processed"] - 1) + signal_result["quality"]) /
                self.metrics["signals_processed"]
            )
            
            if thought_command:
                self.metrics["commands_decoded"] += 1
            
            return {
                "signal_processing": signal_result,
                "brain_state": asdict(brain_state),
                "thought_command": asdict(thought_command) if thought_command else None,
                "processing_time": processing_time,
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Error processing neural input: {e}")
            return {"success": False, "error": str(e)}

class BrainComputerInterface:
    """Main brain-computer interface system"""
    
    def __init__(self, system_controller=None):
        self.system_controller = system_controller
        self.neural_processor = NeuralProcessor(system_controller)
        
        # BCI configuration
        self.config = {
            "interface_type": "non_invasive",
            "signal_types": ["EEG", "EMG", "EOG"],
            "real_time_processing": True,
            "adaptive_learning": True,
            "accessibility_mode": True
        }
        
        # User profiles
        self.user_profiles: Dict[str, CognitiveProfile] = {}
        
        # BCI capabilities
        self.capabilities = {
            "thought_commands": True,
            "cognitive_monitoring": True,
            "accessibility_features": True,
            "real_time_feedback": True,
            "adaptive_learning": True
        }
        
        logger.info("Brain-Computer Interface initialized")
    
    async def process_brain_input(self, user_id: str, neural_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process brain input for specific user"""
        try:
            # Create neural signal object
            neural_signal = NeuralSignal(
                signal_id=hashlib.md5(f"{time.time()}_{user_id}".encode()).hexdigest(),
                channel=neural_data.get("channel", 0),
                data=neural_data.get("data", []),
                sampling_rate=neural_data.get("sampling_rate", 256.0),
                timestamp=datetime.now().isoformat(),
                signal_type=neural_data.get("signal_type", "EEG"),
                quality=neural_data.get("quality", 0.5),
                artifacts=neural_data.get("artifacts", [])
            )
            
            # Process neural signal
            result = await self.neural_processor.process_neural_input(neural_signal)
            
            # Update user profile if available
            if user_id in self.user_profiles:
                self._update_user_profile(user_id, result)
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing brain input: {e}")
            return {"success": False, "error": str(e)}
    
    def create_user_profile(self, user_id: str) -> CognitiveProfile:
        """Create cognitive profile for user"""
        profile = CognitiveProfile(
            user_id=user_id,
            baseline_patterns={},
            learned_commands={},
            adaptation_parameters={"learning_rate": 0.01, "adaptation_threshold": 0.1},
            training_sessions=0,
            last_calibration=datetime.now().isoformat()
        )
        
        self.user_profiles[user_id] = profile
        return profile
    
    def _update_user_profile(self, user_id: str, processing_result: Dict[str, Any]):
        """Update user cognitive profile based on processing results"""
        try:
            profile = self.user_profiles[user_id]
            
            # Update baseline patterns
            if "brain_state" in processing_result:
                brain_state = processing_result["brain_state"]
                # Simple running average update
                for key, value in brain_state.items():
                    if isinstance(value, (int, float)):
                        if key not in profile.baseline_patterns:
                            profile.baseline_patterns[key] = []
                        profile.baseline_patterns[key].append(value)
                        # Keep only recent values
                        if len(profile.baseline_patterns[key]) > 100:
                            profile.baseline_patterns[key] = profile.baseline_patterns[key][-100:]
            
            # Update learned commands
            if processing_result.get("thought_command"):
                command = processing_result["thought_command"]
                command_intent = command["intent"]
                
                if command_intent not in profile.learned_commands:
                    profile.learned_commands[command_intent] = {
                        "count": 0,
                        "average_confidence": 0.0,
                        "success_rate": 0.0
                    }
                
                cmd_data = profile.learned_commands[command_intent]
                cmd_data["count"] += 1
                cmd_data["average_confidence"] = (
                    (cmd_data["average_confidence"] * (cmd_data["count"] - 1) + command["confidence"]) /
                    cmd_data["count"]
                )
            
        except Exception as e:
            logger.error(f"Error updating user profile: {e}")
    
    def get_bci_capabilities(self) -> Dict[str, Any]:
        """Get BCI capabilities and status"""
        return {
            "capabilities": self.capabilities,
            "configuration": self.config,
            "neural_processor_metrics": self.neural_processor.metrics,
            "active_users": len(self.user_profiles),
            "supported_signals": self.config["signal_types"],
            "processing_status": "active" if self.config["real_time_processing"] else "inactive"
        }


# Alias for compatibility
BCIInterface = BrainComputerInterface
