"""
Advanced Biometric Authentication System

This module provides next-generation biometric authentication including multi-modal
biometrics, behavioral biometrics, liveness detection, biometric fusion, and
privacy-preserving authentication for enhanced security.
"""

import numpy as np
import logging
import threading
import time
import json
import hashlib
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
import cv2
import base64
from collections import deque, defaultdict

logger = logging.getLogger(__name__)

@dataclass
class BiometricTemplate:
    """Biometric template structure"""
    template_id: str
    user_id: str
    biometric_type: str  # 'fingerprint', 'face', 'iris', 'voice', 'behavioral'
    template_data: List[float]
    quality_score: float
    created_date: str
    last_used: str
    usage_count: int
    encrypted: bool

@dataclass
class BiometricSample:
    """Biometric sample for authentication"""
    sample_id: str
    biometric_type: str
    raw_data: Any
    features: List[float]
    quality_score: float
    timestamp: str
    device_info: Dict[str, Any]

@dataclass
class AuthenticationResult:
    """Authentication result structure"""
    result_id: str
    user_id: Optional[str]
    success: bool
    confidence: float
    biometric_types_used: List[str]
    processing_time: float
    liveness_score: float
    risk_score: float
    timestamp: str
    metadata: Dict[str, Any]

@dataclass
class BehavioralPattern:
    """Behavioral biometric pattern"""
    pattern_id: str
    user_id: str
    pattern_type: str  # 'typing', 'mouse', 'gait', 'voice_cadence'
    features: Dict[str, List[float]]
    confidence_threshold: float
    created_date: str
    last_updated: str

class FingerprintProcessor:
    """Fingerprint biometric processing"""
    
    def __init__(self):
        self.minutiae_extractor = MinutiaeExtractor()
        self.quality_assessor = QualityAssessor()
        
    def extract_features(self, fingerprint_image: np.ndarray) -> Dict[str, Any]:
        """Extract fingerprint features"""
        try:
            # Preprocess fingerprint image
            processed_image = self._preprocess_fingerprint(fingerprint_image)
            
            # Extract minutiae points
            minutiae = self.minutiae_extractor.extract_minutiae(processed_image)
            
            # Calculate quality score
            quality = self.quality_assessor.assess_fingerprint_quality(processed_image)
            
            # Extract ridge patterns
            ridge_features = self._extract_ridge_features(processed_image)
            
            return {
                "minutiae": minutiae,
                "ridge_features": ridge_features,
                "quality_score": quality,
                "image_size": fingerprint_image.shape,
                "processing_time": time.time()
            }
            
        except Exception as e:
            logger.error(f"Error extracting fingerprint features: {e}")
            return {"error": str(e)}
    
    def _preprocess_fingerprint(self, image: np.ndarray) -> np.ndarray:
        """Preprocess fingerprint image"""
        # Convert to grayscale if needed
        if len(image.shape) == 3:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Normalize image
        normalized = cv2.equalizeHist(image)
        
        # Apply Gaussian blur
        blurred = cv2.GaussianBlur(normalized, (5, 5), 0)
        
        return blurred
    
    def _extract_ridge_features(self, image: np.ndarray) -> List[float]:
        """Extract ridge pattern features"""
        # Simplified ridge feature extraction
        # In real implementation, would use advanced fingerprint algorithms
        
        # Calculate ridge frequency
        ridge_freq = np.mean(np.gradient(image))
        
        # Calculate ridge orientation
        grad_x = cv2.Sobel(image, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(image, cv2.CV_64F, 0, 1, ksize=3)
        ridge_orientation = np.mean(np.arctan2(grad_y, grad_x))
        
        # Calculate ridge density
        ridge_density = np.std(image) / np.mean(image)
        
        return [ridge_freq, ridge_orientation, ridge_density]

class MinutiaeExtractor:
    """Extract minutiae points from fingerprints"""
    
    def extract_minutiae(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """Extract minutiae points"""
        # Simplified minutiae extraction
        # In real implementation, would use specialized fingerprint libraries
        
        minutiae = []
        
        # Apply edge detection to find ridge endings and bifurcations
        edges = cv2.Canny(image, 50, 150)
        
        # Find contours
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for i, contour in enumerate(contours[:20]):  # Limit to 20 minutiae
            # Calculate centroid
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                
                # Estimate minutiae type and angle
                minutiae_type = "ending" if len(contour) < 10 else "bifurcation"
                angle = np.random.uniform(0, 2 * np.pi)  # Simplified
                
                minutiae.append({
                    "x": cx,
                    "y": cy,
                    "angle": angle,
                    "type": minutiae_type,
                    "quality": np.random.uniform(0.7, 1.0)
                })
        
        return minutiae

class FaceProcessor:
    """Face biometric processing"""
    
    def __init__(self):
        self.face_detector = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        self.landmark_detector = FaceLandmarkDetector()
        
    def extract_features(self, face_image: np.ndarray) -> Dict[str, Any]:
        """Extract facial features"""
        try:
            # Detect faces
            faces = self.face_detector.detectMultiScale(face_image, 1.1, 4)
            
            if len(faces) == 0:
                return {"error": "No face detected"}
            
            # Use the largest face
            face = max(faces, key=lambda x: x[2] * x[3])
            x, y, w, h = face
            
            # Extract face region
            face_roi = face_image[y:y+h, x:x+w]
            
            # Extract facial landmarks
            landmarks = self.landmark_detector.detect_landmarks(face_roi)
            
            # Extract geometric features
            geometric_features = self._extract_geometric_features(landmarks)
            
            # Extract texture features
            texture_features = self._extract_texture_features(face_roi)
            
            # Calculate quality score
            quality = self._calculate_face_quality(face_roi, landmarks)
            
            return {
                "geometric_features": geometric_features,
                "texture_features": texture_features,
                "landmarks": landmarks,
                "face_region": face.tolist(),
                "quality_score": quality,
                "processing_time": time.time()
            }
            
        except Exception as e:
            logger.error(f"Error extracting face features: {e}")
            return {"error": str(e)}
    
    def _extract_geometric_features(self, landmarks: List[Tuple[int, int]]) -> List[float]:
        """Extract geometric features from facial landmarks"""
        if len(landmarks) < 5:
            return [0.0] * 10
        
        features = []
        
        # Calculate distances between key points
        for i in range(min(5, len(landmarks))):
            for j in range(i+1, min(5, len(landmarks))):
                dist = np.sqrt((landmarks[i][0] - landmarks[j][0])**2 + 
                              (landmarks[i][1] - landmarks[j][1])**2)
                features.append(dist)
        
        # Pad to fixed length
        while len(features) < 10:
            features.append(0.0)
        
        return features[:10]
    
    def _extract_texture_features(self, face_roi: np.ndarray) -> List[float]:
        """Extract texture features from face region"""
        # Calculate LBP (Local Binary Pattern) features
        # Simplified implementation
        
        # Resize to standard size
        resized = cv2.resize(face_roi, (64, 64))
        
        # Calculate histogram
        hist = cv2.calcHist([resized], [0], None, [256], [0, 256])
        
        # Normalize and take first 20 bins
        normalized_hist = hist.flatten() / np.sum(hist)
        
        return normalized_hist[:20].tolist()
    
    def _calculate_face_quality(self, face_roi: np.ndarray, landmarks: List[Tuple[int, int]]) -> float:
        """Calculate face image quality"""
        quality = 1.0
        
        # Check image sharpness
        laplacian_var = cv2.Laplacian(face_roi, cv2.CV_64F).var()
        if laplacian_var < 100:  # Blurry image
            quality -= 0.3
        
        # Check brightness
        mean_brightness = np.mean(face_roi)
        if mean_brightness < 50 or mean_brightness > 200:
            quality -= 0.2
        
        # Check landmark detection quality
        if len(landmarks) < 5:
            quality -= 0.4
        
        return max(0.0, min(1.0, quality))

class FaceLandmarkDetector:
    """Facial landmark detection"""
    
    def detect_landmarks(self, face_image: np.ndarray) -> List[Tuple[int, int]]:
        """Detect facial landmarks"""
        # Simplified landmark detection
        # In real implementation, would use dlib or similar libraries
        
        h, w = face_image.shape[:2]
        
        # Estimate key facial landmarks
        landmarks = [
            (int(w * 0.3), int(h * 0.4)),   # Left eye
            (int(w * 0.7), int(h * 0.4)),   # Right eye
            (int(w * 0.5), int(h * 0.6)),   # Nose tip
            (int(w * 0.3), int(h * 0.8)),   # Left mouth corner
            (int(w * 0.7), int(h * 0.8)),   # Right mouth corner
        ]
        
        return landmarks

class VoiceProcessor:
    """Voice biometric processing"""
    
    def __init__(self):
        self.sample_rate = 16000
        self.frame_length = 1024
        
    def extract_features(self, audio_data: np.ndarray) -> Dict[str, Any]:
        """Extract voice biometric features"""
        try:
            # Extract MFCC features
            mfcc_features = self._extract_mfcc(audio_data)
            
            # Extract pitch features
            pitch_features = self._extract_pitch(audio_data)
            
            # Extract formant features
            formant_features = self._extract_formants(audio_data)
            
            # Calculate quality score
            quality = self._calculate_voice_quality(audio_data)
            
            return {
                "mfcc_features": mfcc_features,
                "pitch_features": pitch_features,
                "formant_features": formant_features,
                "quality_score": quality,
                "duration": len(audio_data) / self.sample_rate,
                "processing_time": time.time()
            }
            
        except Exception as e:
            logger.error(f"Error extracting voice features: {e}")
            return {"error": str(e)}
    
    def _extract_mfcc(self, audio_data: np.ndarray) -> List[float]:
        """Extract MFCC features"""
        # Simplified MFCC extraction
        # In real implementation, would use librosa or similar
        
        # Apply pre-emphasis
        pre_emphasized = np.append(audio_data[0], audio_data[1:] - 0.97 * audio_data[:-1])
        
        # Frame the signal
        frame_count = len(pre_emphasized) // self.frame_length
        frames = pre_emphasized[:frame_count * self.frame_length].reshape(frame_count, self.frame_length)
        
        # Calculate mean MFCC coefficients
        mfcc_coeffs = []
        for frame in frames:
            # Apply window
            windowed = frame * np.hanning(self.frame_length)
            
            # FFT
            fft_frame = np.fft.fft(windowed)
            power_spectrum = np.abs(fft_frame) ** 2
            
            # Mel filter bank (simplified)
            mel_coeffs = np.mean(power_spectrum[:13])
            mfcc_coeffs.append(mel_coeffs)
        
        return mfcc_coeffs[:13]  # Return first 13 coefficients
    
    def _extract_pitch(self, audio_data: np.ndarray) -> Dict[str, float]:
        """Extract pitch features"""
        # Simplified pitch extraction
        # Calculate fundamental frequency using autocorrelation
        
        # Autocorrelation
        autocorr = np.correlate(audio_data, audio_data, mode='full')
        autocorr = autocorr[len(autocorr)//2:]
        
        # Find peak (fundamental frequency)
        peak_idx = np.argmax(autocorr[1:]) + 1
        f0 = self.sample_rate / peak_idx if peak_idx > 0 else 0
        
        return {
            "fundamental_frequency": f0,
            "pitch_variance": np.var(autocorr[:100]),
            "pitch_range": np.ptp(autocorr[:100])
        }
    
    def _extract_formants(self, audio_data: np.ndarray) -> List[float]:
        """Extract formant frequencies"""
        # Simplified formant extraction
        # In real implementation, would use LPC analysis
        
        # Apply window
        windowed = audio_data * np.hanning(len(audio_data))
        
        # FFT
        fft_data = np.fft.fft(windowed)
        power_spectrum = np.abs(fft_data) ** 2
        
        # Find peaks (formants)
        from scipy.signal import find_peaks
        peaks, _ = find_peaks(power_spectrum[:len(power_spectrum)//2], height=np.max(power_spectrum) * 0.1)
        
        # Convert to frequencies
        formants = peaks * self.sample_rate / len(audio_data)
        
        # Return first 3 formants
        formants_list = formants[:3].tolist()
        while len(formants_list) < 3:
            formants_list.append(0.0)
        
        return formants_list
    
    def _calculate_voice_quality(self, audio_data: np.ndarray) -> float:
        """Calculate voice sample quality"""
        quality = 1.0
        
        # Check signal-to-noise ratio
        signal_power = np.mean(audio_data ** 2)
        if signal_power < 0.01:  # Very quiet
            quality -= 0.4
        
        # Check for clipping
        if np.max(np.abs(audio_data)) > 0.95:
            quality -= 0.3
        
        # Check duration
        duration = len(audio_data) / self.sample_rate
        if duration < 1.0:  # Too short
            quality -= 0.2
        
        return max(0.0, min(1.0, quality))

class QualityAssessor:
    """Biometric quality assessment"""
    
    def assess_fingerprint_quality(self, fingerprint_image: np.ndarray) -> float:
        """Assess fingerprint image quality"""
        quality = 1.0
        
        # Check image contrast
        contrast = np.std(fingerprint_image)
        if contrast < 30:
            quality -= 0.3
        
        # Check image sharpness
        laplacian_var = cv2.Laplacian(fingerprint_image, cv2.CV_64F).var()
        if laplacian_var < 100:
            quality -= 0.3
        
        # Check image size
        if fingerprint_image.shape[0] < 200 or fingerprint_image.shape[1] < 200:
            quality -= 0.2
        
        return max(0.0, min(1.0, quality))

class LivenessDetector:
    """Liveness detection for anti-spoofing"""
    
    def __init__(self):
        self.detection_methods = ["blink_detection", "texture_analysis", "motion_analysis"]
    
    def detect_liveness(self, biometric_type: str, sample_data: Any, 
                       additional_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Detect if biometric sample is from a live person"""
        try:
            if biometric_type == "face":
                return self._detect_face_liveness(sample_data, additional_data)
            elif biometric_type == "fingerprint":
                return self._detect_fingerprint_liveness(sample_data)
            elif biometric_type == "voice":
                return self._detect_voice_liveness(sample_data)
            else:
                return {"liveness_score": 0.5, "method": "unknown"}
                
        except Exception as e:
            logger.error(f"Error in liveness detection: {e}")
            return {"liveness_score": 0.0, "error": str(e)}
    
    def _detect_face_liveness(self, face_image: np.ndarray, 
                            additional_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Detect face liveness"""
        liveness_score = 0.5
        methods_used = []
        
        # Texture analysis
        texture_score = self._analyze_face_texture(face_image)
        liveness_score += texture_score * 0.4
        methods_used.append("texture_analysis")
        
        # Motion analysis (if video frames available)
        if additional_data and "previous_frames" in additional_data:
            motion_score = self._analyze_face_motion(face_image, additional_data["previous_frames"])
            liveness_score += motion_score * 0.3
            methods_used.append("motion_analysis")
        
        # Blink detection (if eye regions available)
        blink_score = self._detect_eye_blinks(face_image)
        liveness_score += blink_score * 0.3
        methods_used.append("blink_detection")
        
        return {
            "liveness_score": min(1.0, liveness_score),
            "methods_used": methods_used,
            "confidence": 0.8
        }
    
    def _analyze_face_texture(self, face_image: np.ndarray) -> float:
        """Analyze face texture for liveness"""
        # Calculate texture complexity
        gray = cv2.cvtColor(face_image, cv2.COLOR_BGR2GRAY) if len(face_image.shape) == 3 else face_image
        
        # Local Binary Pattern analysis
        texture_variance = np.var(gray)
        
        # Higher variance indicates more natural texture
        return min(1.0, texture_variance / 1000.0)
    
    def _analyze_face_motion(self, current_frame: np.ndarray, previous_frames: List[np.ndarray]) -> float:
        """Analyze face motion for liveness"""
        if not previous_frames:
            return 0.5
        
        # Calculate optical flow between frames
        prev_gray = cv2.cvtColor(previous_frames[-1], cv2.COLOR_BGR2GRAY) if len(previous_frames[-1].shape) == 3 else previous_frames[-1]
        curr_gray = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY) if len(current_frame.shape) == 3 else current_frame
        
        # Calculate motion magnitude
        flow = cv2.calcOpticalFlowPyrLK(prev_gray, curr_gray, None, None)
        motion_magnitude = np.mean(np.abs(flow[0])) if flow[0] is not None else 0
        
        # Natural motion indicates liveness
        return min(1.0, motion_magnitude / 10.0)
    
    def _detect_eye_blinks(self, face_image: np.ndarray) -> float:
        """Detect eye blinks for liveness"""
        # Simplified blink detection
        # In real implementation, would track eye aspect ratio over time
        
        # Estimate eye regions
        h, w = face_image.shape[:2]
        left_eye_region = face_image[int(h*0.3):int(h*0.5), int(w*0.2):int(w*0.4)]
        right_eye_region = face_image[int(h*0.3):int(h*0.5), int(w*0.6):int(w*0.8)]
        
        # Calculate eye openness (simplified)
        left_openness = np.mean(left_eye_region)
        right_openness = np.mean(right_eye_region)
        
        # Random score for demonstration
        return np.random.uniform(0.6, 1.0)
    
    def _detect_fingerprint_liveness(self, fingerprint_image: np.ndarray) -> Dict[str, Any]:
        """Detect fingerprint liveness"""
        # Analyze ridge flow and texture
        texture_score = np.std(fingerprint_image) / 255.0
        
        return {
            "liveness_score": min(1.0, texture_score * 2),
            "methods_used": ["texture_analysis"],
            "confidence": 0.7
        }
    
    def _detect_voice_liveness(self, audio_data: np.ndarray) -> Dict[str, Any]:
        """Detect voice liveness"""
        # Analyze voice naturalness
        # Check for natural variations and breathing patterns
        
        # Calculate signal variation
        variation_score = np.std(audio_data) / np.mean(np.abs(audio_data))
        
        return {
            "liveness_score": min(1.0, variation_score * 5),
            "methods_used": ["variation_analysis"],
            "confidence": 0.6
        }

class BehavioralBiometrics:
    """Behavioral biometric analysis"""

    def __init__(self):
        self.typing_patterns: Dict[str, List[float]] = {}
        self.mouse_patterns: Dict[str, List[float]] = {}
        self.voice_patterns: Dict[str, List[float]] = {}

    def analyze_typing_pattern(self, keystroke_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze typing behavioral pattern"""
        try:
            if len(keystroke_data) < 10:
                return {"error": "Insufficient keystroke data"}

            # Calculate dwell times (key press duration)
            dwell_times = []
            for keystroke in keystroke_data:
                if "press_time" in keystroke and "release_time" in keystroke:
                    dwell_time = keystroke["release_time"] - keystroke["press_time"]
                    dwell_times.append(dwell_time)

            # Calculate flight times (time between keystrokes)
            flight_times = []
            for i in range(len(keystroke_data) - 1):
                if "release_time" in keystroke_data[i] and "press_time" in keystroke_data[i+1]:
                    flight_time = keystroke_data[i+1]["press_time"] - keystroke_data[i]["release_time"]
                    flight_times.append(flight_time)

            # Calculate statistics
            features = {
                "avg_dwell_time": np.mean(dwell_times) if dwell_times else 0,
                "std_dwell_time": np.std(dwell_times) if dwell_times else 0,
                "avg_flight_time": np.mean(flight_times) if flight_times else 0,
                "std_flight_time": np.std(flight_times) if flight_times else 0,
                "typing_speed": len(keystroke_data) / (keystroke_data[-1]["release_time"] - keystroke_data[0]["press_time"]) if len(keystroke_data) > 1 else 0
            }

            return {
                "features": features,
                "pattern_type": "typing",
                "sample_count": len(keystroke_data),
                "quality_score": min(1.0, len(keystroke_data) / 50.0)
            }

        except Exception as e:
            logger.error(f"Error analyzing typing pattern: {e}")
            return {"error": str(e)}

    def analyze_mouse_pattern(self, mouse_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze mouse movement behavioral pattern"""
        try:
            if len(mouse_data) < 20:
                return {"error": "Insufficient mouse data"}

            # Extract coordinates
            x_coords = [point["x"] for point in mouse_data if "x" in point]
            y_coords = [point["y"] for point in mouse_data if "y" in point]
            timestamps = [point["timestamp"] for point in mouse_data if "timestamp" in point]

            # Calculate velocities
            velocities = []
            for i in range(len(x_coords) - 1):
                dx = x_coords[i+1] - x_coords[i]
                dy = y_coords[i+1] - y_coords[i]
                dt = timestamps[i+1] - timestamps[i] if len(timestamps) > i+1 else 0.01
                velocity = np.sqrt(dx**2 + dy**2) / dt if dt > 0 else 0
                velocities.append(velocity)

            # Calculate accelerations
            accelerations = []
            for i in range(len(velocities) - 1):
                dv = velocities[i+1] - velocities[i]
                dt = timestamps[i+1] - timestamps[i] if len(timestamps) > i+1 else 0.01
                acceleration = dv / dt if dt > 0 else 0
                accelerations.append(acceleration)

            features = {
                "avg_velocity": np.mean(velocities) if velocities else 0,
                "std_velocity": np.std(velocities) if velocities else 0,
                "avg_acceleration": np.mean(accelerations) if accelerations else 0,
                "std_acceleration": np.std(accelerations) if accelerations else 0,
                "path_length": sum(np.sqrt((x_coords[i+1] - x_coords[i])**2 + (y_coords[i+1] - y_coords[i])**2)
                              for i in range(len(x_coords) - 1)),
                "movement_efficiency": self._calculate_movement_efficiency(x_coords, y_coords)
            }

            return {
                "features": features,
                "pattern_type": "mouse",
                "sample_count": len(mouse_data),
                "quality_score": min(1.0, len(mouse_data) / 100.0)
            }

        except Exception as e:
            logger.error(f"Error analyzing mouse pattern: {e}")
            return {"error": str(e)}

    def _calculate_movement_efficiency(self, x_coords: List[float], y_coords: List[float]) -> float:
        """Calculate mouse movement efficiency"""
        if len(x_coords) < 2:
            return 0.0

        # Calculate direct distance
        direct_distance = np.sqrt((x_coords[-1] - x_coords[0])**2 + (y_coords[-1] - y_coords[0])**2)

        # Calculate actual path length
        path_length = sum(np.sqrt((x_coords[i+1] - x_coords[i])**2 + (y_coords[i+1] - y_coords[i])**2)
                         for i in range(len(x_coords) - 1))

        # Efficiency is ratio of direct distance to path length
        return direct_distance / path_length if path_length > 0 else 0.0

class BiometricFusion:
    """Multi-modal biometric fusion system"""

    def __init__(self):
        self.fusion_strategies = {
            "score_level": self._score_level_fusion,
            "feature_level": self._feature_level_fusion,
            "decision_level": self._decision_level_fusion,
            "adaptive": self._adaptive_fusion
        }
        self.weight_calculator = WeightCalculator()

    def fuse_biometrics(self, biometric_results: List[Dict[str, Any]],
                       strategy: str = "adaptive") -> Dict[str, Any]:
        """Fuse multiple biometric authentication results"""
        try:
            if not biometric_results:
                return {"error": "No biometric results to fuse"}

            if strategy not in self.fusion_strategies:
                strategy = "adaptive"

            fusion_func = self.fusion_strategies[strategy]
            fused_result = fusion_func(biometric_results)

            return {
                "fused_score": fused_result["score"],
                "confidence": fused_result["confidence"],
                "fusion_strategy": strategy,
                "biometric_types": [result.get("biometric_type", "unknown") for result in biometric_results],
                "individual_scores": [result.get("score", 0.0) for result in biometric_results],
                "processing_time": time.time()
            }

        except Exception as e:
            logger.error(f"Error in biometric fusion: {e}")
            return {"error": str(e)}

    def _score_level_fusion(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Score-level fusion using weighted average"""
        scores = [result.get("score", 0.0) for result in results]
        qualities = [result.get("quality_score", 0.5) for result in results]

        # Calculate weights based on quality scores
        weights = [q / sum(qualities) for q in qualities] if sum(qualities) > 0 else [1.0 / len(results)] * len(results)

        # Weighted average
        fused_score = sum(score * weight for score, weight in zip(scores, weights))
        confidence = np.mean(qualities)

        return {"score": fused_score, "confidence": confidence}

    def _feature_level_fusion(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Feature-level fusion by concatenating features"""
        all_features = []

        for result in results:
            features = result.get("features", [])
            if isinstance(features, list):
                all_features.extend(features)
            elif isinstance(features, dict):
                all_features.extend(features.values())

        # Simple scoring based on feature consistency
        if all_features:
            feature_variance = np.var(all_features)
            fused_score = 1.0 / (1.0 + feature_variance)  # Lower variance = higher score
        else:
            fused_score = 0.0

        confidence = len(all_features) / 100.0  # Confidence based on feature count

        return {"score": min(1.0, fused_score), "confidence": min(1.0, confidence)}

    def _decision_level_fusion(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Decision-level fusion using majority voting"""
        decisions = [result.get("score", 0.0) > 0.5 for result in results]
        positive_decisions = sum(decisions)

        # Majority vote
        fused_score = positive_decisions / len(decisions) if decisions else 0.0
        confidence = abs(positive_decisions - len(decisions) / 2) / (len(decisions) / 2) if decisions else 0.0

        return {"score": fused_score, "confidence": confidence}

    def _adaptive_fusion(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Adaptive fusion that selects best strategy based on context"""
        # Analyze result characteristics
        score_variance = np.var([result.get("score", 0.0) for result in results])
        quality_variance = np.var([result.get("quality_score", 0.5) for result in results])

        # Select fusion strategy based on characteristics
        if quality_variance > 0.2:
            # High quality variance - use score-level fusion with quality weighting
            return self._score_level_fusion(results)
        elif score_variance > 0.3:
            # High score variance - use decision-level fusion
            return self._decision_level_fusion(results)
        else:
            # Similar scores and qualities - use feature-level fusion
            return self._feature_level_fusion(results)

class WeightCalculator:
    """Calculate fusion weights for biometric modalities"""

    def calculate_weights(self, biometric_types: List[str], quality_scores: List[float]) -> List[float]:
        """Calculate fusion weights based on biometric types and quality"""
        # Base weights for different biometric types
        base_weights = {
            "fingerprint": 0.9,
            "face": 0.8,
            "iris": 0.95,
            "voice": 0.7,
            "behavioral": 0.6
        }

        weights = []
        for bio_type, quality in zip(biometric_types, quality_scores):
            base_weight = base_weights.get(bio_type, 0.5)
            adjusted_weight = base_weight * quality
            weights.append(adjusted_weight)

        # Normalize weights
        total_weight = sum(weights)
        if total_weight > 0:
            weights = [w / total_weight for w in weights]
        else:
            weights = [1.0 / len(weights)] * len(weights)

        return weights

class AdvancedBiometricAuth:
    """Main advanced biometric authentication system"""

    def __init__(self, system_controller=None):
        self.system_controller = system_controller

        # Initialize processors
        self.fingerprint_processor = FingerprintProcessor()
        self.face_processor = FaceProcessor()
        self.voice_processor = VoiceProcessor()
        self.behavioral_biometrics = BehavioralBiometrics()

        # Initialize security components
        self.liveness_detector = LivenessDetector()
        self.biometric_fusion = BiometricFusion()

        # Template storage (in production, would use secure database)
        self.biometric_templates: Dict[str, List[BiometricTemplate]] = defaultdict(list)

        # Authentication configuration
        self.config = {
            "multi_modal_required": True,
            "liveness_detection": True,
            "behavioral_analysis": True,
            "fusion_strategy": "adaptive",
            "security_level": "high",
            "template_encryption": True
        }

        # Performance metrics
        self.metrics = {
            "authentications_performed": 0,
            "successful_authentications": 0,
            "false_acceptance_rate": 0.0,
            "false_rejection_rate": 0.0,
            "average_processing_time": 0.0
        }

        logger.info("Advanced Biometric Authentication initialized")

    async def authenticate_user(self, biometric_samples: List[BiometricSample]) -> AuthenticationResult:
        """Perform multi-modal biometric authentication"""
        try:
            start_time = time.time()

            if not biometric_samples:
                return AuthenticationResult(
                    result_id="no_samples",
                    user_id=None,
                    success=False,
                    confidence=0.0,
                    biometric_types_used=[],
                    processing_time=0.0,
                    liveness_score=0.0,
                    risk_score=1.0,
                    timestamp=datetime.now().isoformat(),
                    metadata={"error": "No biometric samples provided"}
                )

            # Process each biometric sample
            processed_results = []
            liveness_scores = []

            for sample in biometric_samples:
                # Extract features
                features = await self._extract_biometric_features(sample)

                if "error" not in features:
                    # Perform liveness detection
                    liveness_result = self.liveness_detector.detect_liveness(
                        sample.biometric_type, sample.raw_data
                    )
                    liveness_scores.append(liveness_result.get("liveness_score", 0.0))

                    # Match against templates
                    match_result = self._match_biometric_template(sample.biometric_type, features)

                    processed_results.append({
                        "biometric_type": sample.biometric_type,
                        "features": features,
                        "match_result": match_result,
                        "liveness_score": liveness_result.get("liveness_score", 0.0),
                        "quality_score": sample.quality_score,
                        "score": match_result.get("confidence", 0.0)
                    })

            if not processed_results:
                return AuthenticationResult(
                    result_id="processing_failed",
                    user_id=None,
                    success=False,
                    confidence=0.0,
                    biometric_types_used=[],
                    processing_time=time.time() - start_time,
                    liveness_score=0.0,
                    risk_score=1.0,
                    timestamp=datetime.now().isoformat(),
                    metadata={"error": "Failed to process biometric samples"}
                )

            # Fuse biometric results
            fusion_result = self.biometric_fusion.fuse_biometrics(
                processed_results, self.config["fusion_strategy"]
            )

            # Determine authentication result
            success = fusion_result.get("fused_score", 0.0) > 0.7  # Threshold
            user_id = processed_results[0]["match_result"].get("user_id") if success else None

            # Calculate risk score
            risk_score = self._calculate_risk_score(processed_results, liveness_scores)

            # Update metrics
            processing_time = time.time() - start_time
            self._update_metrics(success, processing_time)

            return AuthenticationResult(
                result_id=hashlib.md5(f"{time.time()}_{user_id}".encode()).hexdigest(),
                user_id=user_id,
                success=success,
                confidence=fusion_result.get("fused_score", 0.0),
                biometric_types_used=[r["biometric_type"] for r in processed_results],
                processing_time=processing_time,
                liveness_score=np.mean(liveness_scores) if liveness_scores else 0.0,
                risk_score=risk_score,
                timestamp=datetime.now().isoformat(),
                metadata={
                    "fusion_strategy": fusion_result.get("fusion_strategy", "unknown"),
                    "individual_scores": fusion_result.get("individual_scores", [])
                }
            )

        except Exception as e:
            logger.error(f"Error in biometric authentication: {e}")
            return AuthenticationResult(
                result_id="error",
                user_id=None,
                success=False,
                confidence=0.0,
                biometric_types_used=[],
                processing_time=0.0,
                liveness_score=0.0,
                risk_score=1.0,
                timestamp=datetime.now().isoformat(),
                metadata={"error": str(e)}
            )

    async def _extract_biometric_features(self, sample: BiometricSample) -> Dict[str, Any]:
        """Extract features from biometric sample"""
        try:
            if sample.biometric_type == "fingerprint":
                return self.fingerprint_processor.extract_features(sample.raw_data)
            elif sample.biometric_type == "face":
                return self.face_processor.extract_features(sample.raw_data)
            elif sample.biometric_type == "voice":
                return self.voice_processor.extract_features(sample.raw_data)
            else:
                return {"error": f"Unsupported biometric type: {sample.biometric_type}"}

        except Exception as e:
            logger.error(f"Error extracting features for {sample.biometric_type}: {e}")
            return {"error": str(e)}

    def _match_biometric_template(self, biometric_type: str, features: Dict[str, Any]) -> Dict[str, Any]:
        """Match biometric features against stored templates"""
        try:
            best_match = {"user_id": None, "confidence": 0.0}

            # Get templates for this biometric type
            for user_id, templates in self.biometric_templates.items():
                user_templates = [t for t in templates if t.biometric_type == biometric_type]

                for template in user_templates:
                    # Calculate similarity score
                    similarity = self._calculate_similarity(features, template.template_data)

                    if similarity > best_match["confidence"]:
                        best_match = {"user_id": user_id, "confidence": similarity}

            return best_match

        except Exception as e:
            logger.error(f"Error matching biometric template: {e}")
            return {"user_id": None, "confidence": 0.0, "error": str(e)}

    def _calculate_similarity(self, features: Dict[str, Any], template_data: List[float]) -> float:
        """Calculate similarity between features and template"""
        try:
            # Extract feature vector
            if "geometric_features" in features:
                feature_vector = features["geometric_features"]
            elif "mfcc_features" in features:
                feature_vector = features["mfcc_features"]
            elif "minutiae" in features:
                # For fingerprint minutiae, use simplified matching
                feature_vector = [len(features["minutiae"])] + features.get("ridge_features", [])
            else:
                # Use first available numeric features
                feature_vector = []
                for value in features.values():
                    if isinstance(value, (int, float)):
                        feature_vector.append(value)
                    elif isinstance(value, list) and all(isinstance(x, (int, float)) for x in value):
                        feature_vector.extend(value[:10])  # Limit to 10 features

            if not feature_vector or not template_data:
                return 0.0

            # Normalize vectors to same length
            min_length = min(len(feature_vector), len(template_data))
            feature_vector = feature_vector[:min_length]
            template_data = template_data[:min_length]

            # Calculate cosine similarity
            dot_product = sum(a * b for a, b in zip(feature_vector, template_data))
            magnitude_a = sum(a * a for a in feature_vector) ** 0.5
            magnitude_b = sum(b * b for b in template_data) ** 0.5

            if magnitude_a == 0 or magnitude_b == 0:
                return 0.0

            similarity = dot_product / (magnitude_a * magnitude_b)
            return max(0.0, min(1.0, similarity))

        except Exception as e:
            logger.error(f"Error calculating similarity: {e}")
            return 0.0

    def _calculate_risk_score(self, processed_results: List[Dict[str, Any]],
                            liveness_scores: List[float]) -> float:
        """Calculate authentication risk score"""
        risk_score = 0.0

        # Risk from low liveness scores
        avg_liveness = np.mean(liveness_scores) if liveness_scores else 0.0
        risk_score += (1.0 - avg_liveness) * 0.4

        # Risk from low quality scores
        quality_scores = [r.get("quality_score", 0.0) for r in processed_results]
        avg_quality = np.mean(quality_scores) if quality_scores else 0.0
        risk_score += (1.0 - avg_quality) * 0.3

        # Risk from inconsistent results
        match_scores = [r.get("score", 0.0) for r in processed_results]
        score_variance = np.var(match_scores) if len(match_scores) > 1 else 0.0
        risk_score += score_variance * 0.3

        return max(0.0, min(1.0, risk_score))

    def _update_metrics(self, success: bool, processing_time: float):
        """Update authentication metrics"""
        self.metrics["authentications_performed"] += 1

        if success:
            self.metrics["successful_authentications"] += 1

        # Update average processing time
        total_auths = self.metrics["authentications_performed"]
        current_avg = self.metrics["average_processing_time"]
        self.metrics["average_processing_time"] = (
            (current_avg * (total_auths - 1) + processing_time) / total_auths
        )

        # Update success rate
        success_rate = self.metrics["successful_authentications"] / total_auths
        self.metrics["false_rejection_rate"] = 1.0 - success_rate  # Simplified

    def enroll_user_biometric(self, user_id: str, biometric_sample: BiometricSample) -> bool:
        """Enroll user biometric template"""
        try:
            # Extract features
            features = None
            if biometric_sample.biometric_type == "fingerprint":
                features = self.fingerprint_processor.extract_features(biometric_sample.raw_data)
            elif biometric_sample.biometric_type == "face":
                features = self.face_processor.extract_features(biometric_sample.raw_data)
            elif biometric_sample.biometric_type == "voice":
                features = self.voice_processor.extract_features(biometric_sample.raw_data)

            if not features or "error" in features:
                return False

            # Create template
            template_data = []
            if "geometric_features" in features:
                template_data = features["geometric_features"]
            elif "mfcc_features" in features:
                template_data = features["mfcc_features"]
            elif "minutiae" in features:
                template_data = [len(features["minutiae"])] + features.get("ridge_features", [])

            template = BiometricTemplate(
                template_id=hashlib.md5(f"{user_id}_{biometric_sample.biometric_type}_{time.time()}".encode()).hexdigest(),
                user_id=user_id,
                biometric_type=biometric_sample.biometric_type,
                template_data=template_data,
                quality_score=biometric_sample.quality_score,
                created_date=datetime.now().isoformat(),
                last_used=datetime.now().isoformat(),
                usage_count=0,
                encrypted=self.config["template_encryption"]
            )

            # Store template
            self.biometric_templates[user_id].append(template)

            logger.info(f"Enrolled {biometric_sample.biometric_type} template for user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error enrolling biometric template: {e}")
            return False

    def get_authentication_capabilities(self) -> Dict[str, Any]:
        """Get biometric authentication capabilities"""
        return {
            "supported_biometrics": ["fingerprint", "face", "voice", "behavioral"],
            "multi_modal_fusion": True,
            "liveness_detection": self.config["liveness_detection"],
            "behavioral_analysis": self.config["behavioral_analysis"],
            "security_level": self.config["security_level"],
            "template_encryption": self.config["template_encryption"],
            "enrolled_users": len(self.biometric_templates),
            "total_templates": sum(len(templates) for templates in self.biometric_templates.values()),
            "performance_metrics": self.metrics
        }


# Alias for compatibility
BiometricAuth = AdvancedBiometricAuth
