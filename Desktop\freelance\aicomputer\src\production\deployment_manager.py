"""
Deployment Manager - Phase 7 Component

Advanced deployment management for production environments.
"""

import asyncio
import time
import json
import os
import subprocess
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict
from datetime import datetime, timedelta
from enum import Enum
import redis
import yaml

from loguru import logger

from ..utils.config_manager import ConfigManager


class DeploymentStatus(Enum):
    """Deployment status options."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    ROLLED_BACK = "rolled_back"


class DeploymentStrategy(Enum):
    """Deployment strategy options."""
    BLUE_GREEN = "blue_green"
    ROLLING = "rolling"
    CANARY = "canary"
    RECREATE = "recreate"


@dataclass
class DeploymentConfig:
    """Deployment configuration."""
    deployment_id: str
    environment: str
    strategy: DeploymentStrategy
    version: str
    replicas: int
    health_check_url: str
    rollback_enabled: bool
    timeout_seconds: int
    metadata: Dict[str, Any]


@dataclass
class DeploymentResult:
    """Deployment result."""
    deployment_id: str
    status: DeploymentStatus
    start_time: float
    end_time: Optional[float]
    duration: Optional[float]
    success: bool
    error_message: Optional[str]
    logs: List[str]
    metrics: Dict[str, Any]


class DeploymentManager:
    """
    Advanced deployment management system.
    
    Features:
    - Multiple deployment strategies
    - Automated rollback capabilities
    - Health monitoring during deployment
    - Blue-green deployments
    - Canary releases
    - Rolling updates
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        
        # Deployment tracking
        self.active_deployments: Dict[str, DeploymentConfig] = {}
        self.deployment_history: List[DeploymentResult] = []
        
        # Redis for coordination
        redis_host = self.config.get("redis.host", "localhost")
        redis_port = self.config.get("redis.port", 6379)
        redis_db = self.config.get("redis.db", 0)
        
        try:
            self.redis_client = redis.Redis(
                host=redis_host,
                port=redis_port,
                db=redis_db,
                decode_responses=True
            )
            # Test connection
            self.redis_client.ping()
        except Exception as e:
            logger.warning(f"Redis connection failed, using in-memory storage: {e}")
            self.redis_client = None
        
        # Configuration
        self.default_timeout = self.config.get("deployment.default_timeout", 600)
        self.health_check_interval = self.config.get("deployment.health_check_interval", 30)
        self.max_concurrent_deployments = self.config.get("deployment.max_concurrent", 3)
        
        logger.info("Deployment Manager initialized")
    
    async def deploy(self, config: DeploymentConfig) -> DeploymentResult:
        """Execute a deployment."""
        try:
            logger.info(f"Starting deployment {config.deployment_id}")
            
            # Validate deployment
            validation_result = await self._validate_deployment(config)
            if not validation_result["valid"]:
                return DeploymentResult(
                    deployment_id=config.deployment_id,
                    status=DeploymentStatus.FAILED,
                    start_time=time.time(),
                    end_time=time.time(),
                    duration=0.0,
                    success=False,
                    error_message=f"Validation failed: {validation_result['errors']}",
                    logs=[],
                    metrics={}
                )
            
            # Check concurrent deployment limit
            if len(self.active_deployments) >= self.max_concurrent_deployments:
                return DeploymentResult(
                    deployment_id=config.deployment_id,
                    status=DeploymentStatus.FAILED,
                    start_time=time.time(),
                    end_time=time.time(),
                    duration=0.0,
                    success=False,
                    error_message="Maximum concurrent deployments reached",
                    logs=[],
                    metrics={}
                )
            
            # Start deployment
            start_time = time.time()
            self.active_deployments[config.deployment_id] = config
            
            # Execute deployment strategy
            result = await self._execute_deployment_strategy(config)
            
            # Update result timing
            end_time = time.time()
            result.end_time = end_time
            result.duration = end_time - start_time
            
            # Store result
            self.deployment_history.append(result)
            
            # Cleanup
            if config.deployment_id in self.active_deployments:
                del self.active_deployments[config.deployment_id]
            
            logger.info(f"Deployment {config.deployment_id} completed with status: {result.status}")
            return result
            
        except Exception as e:
            logger.error(f"Error during deployment {config.deployment_id}: {e}")
            return DeploymentResult(
                deployment_id=config.deployment_id,
                status=DeploymentStatus.FAILED,
                start_time=time.time(),
                end_time=time.time(),
                duration=0.0,
                success=False,
                error_message=str(e),
                logs=[],
                metrics={}
            )
    
    async def _validate_deployment(self, config: DeploymentConfig) -> Dict[str, Any]:
        """Validate deployment configuration."""
        try:
            errors = []
            
            # Check required fields
            if not config.deployment_id:
                errors.append("deployment_id is required")
            
            if not config.environment:
                errors.append("environment is required")
            
            if not config.version:
                errors.append("version is required")
            
            if config.replicas <= 0:
                errors.append("replicas must be greater than 0")
            
            # Check environment exists
            valid_environments = ["development", "staging", "production"]
            if config.environment not in valid_environments:
                errors.append(f"Invalid environment: {config.environment}")
            
            # Check for duplicate deployment
            if config.deployment_id in self.active_deployments:
                errors.append("Deployment with this ID is already in progress")
            
            return {
                "valid": len(errors) == 0,
                "errors": errors
            }
            
        except Exception as e:
            logger.error(f"Error validating deployment: {e}")
            return {"valid": False, "errors": [str(e)]}
    
    async def _execute_deployment_strategy(self, config: DeploymentConfig) -> DeploymentResult:
        """Execute deployment based on strategy."""
        try:
            logs = []
            metrics = {}
            
            if config.strategy == DeploymentStrategy.BLUE_GREEN:
                result = await self._blue_green_deployment(config, logs, metrics)
            elif config.strategy == DeploymentStrategy.ROLLING:
                result = await self._rolling_deployment(config, logs, metrics)
            elif config.strategy == DeploymentStrategy.CANARY:
                result = await self._canary_deployment(config, logs, metrics)
            elif config.strategy == DeploymentStrategy.RECREATE:
                result = await self._recreate_deployment(config, logs, metrics)
            else:
                raise ValueError(f"Unsupported deployment strategy: {config.strategy}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error executing deployment strategy: {e}")
            return DeploymentResult(
                deployment_id=config.deployment_id,
                status=DeploymentStatus.FAILED,
                start_time=time.time(),
                end_time=None,
                duration=None,
                success=False,
                error_message=str(e),
                logs=[],
                metrics={}
            )
    
    async def _blue_green_deployment(self, config: DeploymentConfig, 
                                   logs: List[str], metrics: Dict[str, Any]) -> DeploymentResult:
        """Execute blue-green deployment."""
        try:
            logs.append("Starting blue-green deployment")
            
            # Create green environment
            logs.append("Creating green environment")
            await asyncio.sleep(2)  # Simulate deployment time
            
            # Deploy to green
            logs.append(f"Deploying version {config.version} to green environment")
            await asyncio.sleep(3)
            
            # Health check green
            logs.append("Performing health checks on green environment")
            health_ok = await self._perform_health_check(config.health_check_url)
            
            if not health_ok:
                logs.append("Health check failed, rolling back")
                return DeploymentResult(
                    deployment_id=config.deployment_id,
                    status=DeploymentStatus.FAILED,
                    start_time=time.time(),
                    end_time=None,
                    duration=None,
                    success=False,
                    error_message="Health check failed",
                    logs=logs,
                    metrics=metrics
                )
            
            # Switch traffic to green
            logs.append("Switching traffic to green environment")
            await asyncio.sleep(1)
            
            # Cleanup blue environment
            logs.append("Cleaning up blue environment")
            await asyncio.sleep(1)
            
            metrics["deployment_time"] = 7.0
            metrics["health_check_passed"] = True
            
            return DeploymentResult(
                deployment_id=config.deployment_id,
                status=DeploymentStatus.COMPLETED,
                start_time=time.time(),
                end_time=None,
                duration=None,
                success=True,
                error_message=None,
                logs=logs,
                metrics=metrics
            )
            
        except Exception as e:
            logs.append(f"Error in blue-green deployment: {e}")
            return DeploymentResult(
                deployment_id=config.deployment_id,
                status=DeploymentStatus.FAILED,
                start_time=time.time(),
                end_time=None,
                duration=None,
                success=False,
                error_message=str(e),
                logs=logs,
                metrics=metrics
            )
    
    async def _rolling_deployment(self, config: DeploymentConfig, 
                                logs: List[str], metrics: Dict[str, Any]) -> DeploymentResult:
        """Execute rolling deployment."""
        try:
            logs.append("Starting rolling deployment")
            
            # Update instances one by one
            for i in range(config.replicas):
                logs.append(f"Updating instance {i+1}/{config.replicas}")
                await asyncio.sleep(1)
                
                # Health check after each update
                health_ok = await self._perform_health_check(config.health_check_url)
                if not health_ok:
                    logs.append(f"Health check failed for instance {i+1}")
                    return DeploymentResult(
                        deployment_id=config.deployment_id,
                        status=DeploymentStatus.FAILED,
                        start_time=time.time(),
                        end_time=None,
                        duration=None,
                        success=False,
                        error_message=f"Health check failed for instance {i+1}",
                        logs=logs,
                        metrics=metrics
                    )
            
            logs.append("Rolling deployment completed successfully")
            metrics["deployment_time"] = config.replicas * 1.0
            metrics["instances_updated"] = config.replicas
            
            return DeploymentResult(
                deployment_id=config.deployment_id,
                status=DeploymentStatus.COMPLETED,
                start_time=time.time(),
                end_time=None,
                duration=None,
                success=True,
                error_message=None,
                logs=logs,
                metrics=metrics
            )
            
        except Exception as e:
            logs.append(f"Error in rolling deployment: {e}")
            return DeploymentResult(
                deployment_id=config.deployment_id,
                status=DeploymentStatus.FAILED,
                start_time=time.time(),
                end_time=None,
                duration=None,
                success=False,
                error_message=str(e),
                logs=logs,
                metrics=metrics
            )
    
    async def _canary_deployment(self, config: DeploymentConfig, 
                               logs: List[str], metrics: Dict[str, Any]) -> DeploymentResult:
        """Execute canary deployment."""
        try:
            logs.append("Starting canary deployment")
            
            # Deploy to small subset (10%)
            canary_replicas = max(1, config.replicas // 10)
            logs.append(f"Deploying to {canary_replicas} canary instances")
            await asyncio.sleep(2)
            
            # Monitor canary for a period
            logs.append("Monitoring canary deployment")
            await asyncio.sleep(3)
            
            # Check canary health and metrics
            health_ok = await self._perform_health_check(config.health_check_url)
            if not health_ok:
                logs.append("Canary health check failed, aborting deployment")
                return DeploymentResult(
                    deployment_id=config.deployment_id,
                    status=DeploymentStatus.FAILED,
                    start_time=time.time(),
                    end_time=None,
                    duration=None,
                    success=False,
                    error_message="Canary health check failed",
                    logs=logs,
                    metrics=metrics
                )
            
            # Deploy to remaining instances
            remaining_replicas = config.replicas - canary_replicas
            logs.append(f"Deploying to remaining {remaining_replicas} instances")
            await asyncio.sleep(remaining_replicas * 0.5)
            
            logs.append("Canary deployment completed successfully")
            metrics["deployment_time"] = 5.0 + remaining_replicas * 0.5
            metrics["canary_replicas"] = canary_replicas
            
            return DeploymentResult(
                deployment_id=config.deployment_id,
                status=DeploymentStatus.COMPLETED,
                start_time=time.time(),
                end_time=None,
                duration=None,
                success=True,
                error_message=None,
                logs=logs,
                metrics=metrics
            )
            
        except Exception as e:
            logs.append(f"Error in canary deployment: {e}")
            return DeploymentResult(
                deployment_id=config.deployment_id,
                status=DeploymentStatus.FAILED,
                start_time=time.time(),
                end_time=None,
                duration=None,
                success=False,
                error_message=str(e),
                logs=logs,
                metrics=metrics
            )
    
    async def _recreate_deployment(self, config: DeploymentConfig, 
                                 logs: List[str], metrics: Dict[str, Any]) -> DeploymentResult:
        """Execute recreate deployment."""
        try:
            logs.append("Starting recreate deployment")
            
            # Stop all instances
            logs.append("Stopping all instances")
            await asyncio.sleep(2)
            
            # Deploy new version
            logs.append(f"Deploying new version {config.version}")
            await asyncio.sleep(3)
            
            # Start all instances
            logs.append("Starting all instances")
            await asyncio.sleep(2)
            
            # Health check
            health_ok = await self._perform_health_check(config.health_check_url)
            if not health_ok:
                logs.append("Health check failed after recreate deployment")
                return DeploymentResult(
                    deployment_id=config.deployment_id,
                    status=DeploymentStatus.FAILED,
                    start_time=time.time(),
                    end_time=None,
                    duration=None,
                    success=False,
                    error_message="Health check failed",
                    logs=logs,
                    metrics=metrics
                )
            
            logs.append("Recreate deployment completed successfully")
            metrics["deployment_time"] = 7.0
            metrics["downtime"] = 2.0
            
            return DeploymentResult(
                deployment_id=config.deployment_id,
                status=DeploymentStatus.COMPLETED,
                start_time=time.time(),
                end_time=None,
                duration=None,
                success=True,
                error_message=None,
                logs=logs,
                metrics=metrics
            )
            
        except Exception as e:
            logs.append(f"Error in recreate deployment: {e}")
            return DeploymentResult(
                deployment_id=config.deployment_id,
                status=DeploymentStatus.FAILED,
                start_time=time.time(),
                end_time=None,
                duration=None,
                success=False,
                error_message=str(e),
                logs=logs,
                metrics=metrics
            )
    
    async def _perform_health_check(self, health_check_url: str) -> bool:
        """Perform health check on deployment."""
        try:
            # Simulate health check
            await asyncio.sleep(1)
            
            # In a real implementation, this would make HTTP requests
            # For now, simulate 90% success rate
            import random
            return random.random() > 0.1
            
        except Exception as e:
            logger.error(f"Error performing health check: {e}")
            return False
    
    async def rollback(self, deployment_id: str) -> DeploymentResult:
        """Rollback a deployment."""
        try:
            logger.info(f"Rolling back deployment {deployment_id}")
            
            # Find deployment in history
            deployment = None
            for d in self.deployment_history:
                if d.deployment_id == deployment_id:
                    deployment = d
                    break
            
            if not deployment:
                raise ValueError(f"Deployment {deployment_id} not found")
            
            # Simulate rollback
            logs = [
                "Starting rollback",
                "Reverting to previous version",
                "Updating instances",
                "Rollback completed"
            ]
            
            result = DeploymentResult(
                deployment_id=f"{deployment_id}_rollback",
                status=DeploymentStatus.COMPLETED,
                start_time=time.time(),
                end_time=time.time() + 5,
                duration=5.0,
                success=True,
                error_message=None,
                logs=logs,
                metrics={"rollback_time": 5.0}
            )
            
            self.deployment_history.append(result)
            return result
            
        except Exception as e:
            logger.error(f"Error rolling back deployment: {e}")
            return DeploymentResult(
                deployment_id=f"{deployment_id}_rollback",
                status=DeploymentStatus.FAILED,
                start_time=time.time(),
                end_time=time.time(),
                duration=0.0,
                success=False,
                error_message=str(e),
                logs=[],
                metrics={}
            )
    
    async def get_deployment_status(self, deployment_id: str) -> Optional[DeploymentResult]:
        """Get status of a deployment."""
        for deployment in self.deployment_history:
            if deployment.deployment_id == deployment_id:
                return deployment
        return None
    
    async def list_deployments(self, environment: str = None) -> List[DeploymentResult]:
        """List deployments, optionally filtered by environment."""
        deployments = self.deployment_history.copy()
        
        if environment:
            # In a real implementation, we'd filter by environment
            # For now, return all deployments
            pass
        
        return deployments
    
    async def get_deployment_metrics(self) -> Dict[str, Any]:
        """Get deployment metrics and statistics."""
        try:
            total_deployments = len(self.deployment_history)
            successful_deployments = sum(1 for d in self.deployment_history if d.success)
            failed_deployments = total_deployments - successful_deployments
            
            success_rate = (successful_deployments / total_deployments * 100) if total_deployments > 0 else 0
            
            # Calculate average deployment time
            completed_deployments = [d for d in self.deployment_history if d.duration is not None]
            avg_deployment_time = (
                sum(d.duration for d in completed_deployments) / len(completed_deployments)
                if completed_deployments else 0
            )
            
            return {
                "total_deployments": total_deployments,
                "successful_deployments": successful_deployments,
                "failed_deployments": failed_deployments,
                "success_rate": success_rate,
                "average_deployment_time": avg_deployment_time,
                "active_deployments": len(self.active_deployments),
                "deployment_strategies_used": {
                    "blue_green": sum(1 for d in self.deployment_history if "green" in str(d.logs)),
                    "rolling": sum(1 for d in self.deployment_history if "rolling" in str(d.logs)),
                    "canary": sum(1 for d in self.deployment_history if "canary" in str(d.logs)),
                    "recreate": sum(1 for d in self.deployment_history if "recreate" in str(d.logs))
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting deployment metrics: {e}")
            return {}


# Factory functions
def create_deployment_config(deployment_id: str, environment: str, version: str) -> DeploymentConfig:
    """Create a new deployment configuration."""
    return DeploymentConfig(
        deployment_id=deployment_id,
        environment=environment,
        strategy=DeploymentStrategy.ROLLING,
        version=version,
        replicas=3,
        health_check_url="/health",
        rollback_enabled=True,
        timeout_seconds=600,
        metadata={}
    )


# Alias for compatibility
DeploymentManager = DeploymentManager
