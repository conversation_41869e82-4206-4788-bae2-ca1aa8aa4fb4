"""
Monitoring System - Phase 7 Component

Advanced monitoring and alerting for production systems.
"""

import asyncio
import time
import json
import psutil
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from datetime import datetime, timedelta
from enum import Enum
import redis
import threading

from loguru import logger

from ..utils.config_manager import ConfigManager


class AlertSeverity(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class MetricType(Enum):
    """Types of metrics."""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    SUMMARY = "summary"


@dataclass
class Alert:
    """System alert."""
    alert_id: str
    severity: AlertSeverity
    title: str
    description: str
    metric_name: str
    current_value: float
    threshold_value: float
    timestamp: float
    resolved: bool
    resolved_at: Optional[float]
    metadata: Dict[str, Any]


@dataclass
class Metric:
    """System metric."""
    metric_id: str
    name: str
    metric_type: MetricType
    value: float
    unit: str
    labels: Dict[str, str]
    timestamp: float
    metadata: Dict[str, Any]


class MonitoringSystem:
    """
    Advanced monitoring and alerting system.
    
    Features:
    - Real-time system monitoring
    - Custom metric collection
    - Intelligent alerting
    - Performance dashboards
    - Health checks
    - SLA monitoring
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        
        # Monitoring data
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.alerts: List[Alert] = []
        self.alert_rules: Dict[str, Dict[str, Any]] = {}
        
        # Redis for coordination
        redis_host = self.config.get("redis.host", "localhost")
        redis_port = self.config.get("redis.port", 6379)
        redis_db = self.config.get("redis.db", 2)
        
        try:
            self.redis_client = redis.Redis(
                host=redis_host,
                port=redis_port,
                db=redis_db,
                decode_responses=True
            )
            self.redis_client.ping()
        except Exception as e:
            logger.warning(f"Redis connection failed, using in-memory storage: {e}")
            self.redis_client = None
        
        # Configuration
        self.monitoring_enabled = self.config.get("monitoring.enabled", True)
        self.collection_interval = self.config.get("monitoring.collection_interval", 30)
        self.alert_cooldown = self.config.get("monitoring.alert_cooldown", 300)
        
        # Monitoring state
        self.monitoring_active = False
        self.monitor_thread: Optional[threading.Thread] = None
        
        # Initialize default alert rules
        self._initialize_default_alert_rules()
        
        logger.info("Monitoring System initialized")
    
    def _initialize_default_alert_rules(self):
        """Initialize default alert rules."""
        try:
            self.alert_rules = {
                "cpu_usage": {
                    "threshold": 80.0,
                    "severity": AlertSeverity.WARNING,
                    "comparison": "greater_than",
                    "description": "High CPU usage detected"
                },
                "memory_usage": {
                    "threshold": 85.0,
                    "severity": AlertSeverity.WARNING,
                    "comparison": "greater_than",
                    "description": "High memory usage detected"
                },
                "disk_usage": {
                    "threshold": 90.0,
                    "severity": AlertSeverity.ERROR,
                    "comparison": "greater_than",
                    "description": "High disk usage detected"
                },
                "process_memory": {
                    "threshold": 1000.0,  # MB
                    "severity": AlertSeverity.WARNING,
                    "comparison": "greater_than",
                    "description": "High process memory usage"
                }
            }
            
        except Exception as e:
            logger.error(f"Error initializing alert rules: {e}")
    
    async def start_monitoring(self):
        """Start monitoring system."""
        try:
            if not self.monitoring_enabled:
                logger.info("Monitoring is disabled")
                return
            
            self.monitoring_active = True
            
            # Start monitoring thread
            self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitor_thread.start()
            
            logger.info("Monitoring system started")
            
        except Exception as e:
            logger.error(f"Error starting monitoring: {e}")
    
    async def stop_monitoring(self):
        """Stop monitoring system."""
        self.monitoring_active = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5.0)
        
        logger.info("Monitoring system stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop."""
        while self.monitoring_active:
            try:
                # Collect system metrics
                self._collect_system_metrics()
                
                # Check alert rules
                self._check_alert_rules()
                
                # Cleanup old data
                self._cleanup_old_data()
                
                time.sleep(self.collection_interval)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(self.collection_interval)
    
    def _collect_system_metrics(self):
        """Collect system metrics."""
        try:
            current_time = time.time()
            
            # CPU metrics
            cpu_usage = psutil.cpu_percent(interval=None)
            self._record_metric("cpu_usage", cpu_usage, MetricType.GAUGE, "percent")
            
            # Memory metrics
            memory = psutil.virtual_memory()
            self._record_metric("memory_usage", memory.percent, MetricType.GAUGE, "percent")
            self._record_metric("memory_available", memory.available / (1024**3), MetricType.GAUGE, "GB")
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            disk_usage_percent = (disk.used / disk.total) * 100
            self._record_metric("disk_usage", disk_usage_percent, MetricType.GAUGE, "percent")
            self._record_metric("disk_free", disk.free / (1024**3), MetricType.GAUGE, "GB")
            
            # Network metrics
            network = psutil.net_io_counters()
            self._record_metric("network_bytes_sent", network.bytes_sent, MetricType.COUNTER, "bytes")
            self._record_metric("network_bytes_recv", network.bytes_recv, MetricType.COUNTER, "bytes")
            
            # Process metrics
            process = psutil.Process()
            self._record_metric("process_cpu", process.cpu_percent(), MetricType.GAUGE, "percent")
            self._record_metric("process_memory", process.memory_info().rss / (1024**2), MetricType.GAUGE, "MB")
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
    
    def _record_metric(self, name: str, value: float, metric_type: MetricType, unit: str, labels: Dict[str, str] = None):
        """Record a metric."""
        try:
            if labels is None:
                labels = {}
            
            metric = Metric(
                metric_id=f"metric_{int(time.time() * 1000)}_{hash(name) % 10000}",
                name=name,
                metric_type=metric_type,
                value=value,
                unit=unit,
                labels=labels,
                timestamp=time.time(),
                metadata={}
            )
            
            self.metrics[name].append(metric)
            
            # Store in Redis if available
            if self.redis_client:
                try:
                    self.redis_client.lpush(f"metrics:{name}", json.dumps(asdict(metric)))
                    self.redis_client.ltrim(f"metrics:{name}", 0, 999)  # Keep last 1000
                except Exception as e:
                    logger.debug(f"Error storing metric in Redis: {e}")
            
        except Exception as e:
            logger.error(f"Error recording metric {name}: {e}")
    
    def _check_alert_rules(self):
        """Check alert rules against current metrics."""
        try:
            current_time = time.time()
            
            for metric_name, rule in self.alert_rules.items():
                if metric_name not in self.metrics or not self.metrics[metric_name]:
                    continue
                
                # Get latest metric value
                latest_metric = self.metrics[metric_name][-1]
                current_value = latest_metric.value
                threshold = rule["threshold"]
                comparison = rule["comparison"]
                
                # Check if alert condition is met
                alert_triggered = False
                if comparison == "greater_than" and current_value > threshold:
                    alert_triggered = True
                elif comparison == "less_than" and current_value < threshold:
                    alert_triggered = True
                elif comparison == "equals" and current_value == threshold:
                    alert_triggered = True
                
                if alert_triggered:
                    # Check if we already have an active alert for this metric
                    active_alert = None
                    for alert in self.alerts:
                        if (alert.metric_name == metric_name and 
                            not alert.resolved and 
                            (current_time - alert.timestamp) < self.alert_cooldown):
                            active_alert = alert
                            break
                    
                    if not active_alert:
                        # Create new alert
                        self._create_alert(
                            severity=rule["severity"],
                            title=f"Alert: {metric_name}",
                            description=rule["description"],
                            metric_name=metric_name,
                            current_value=current_value,
                            threshold_value=threshold
                        )
                
        except Exception as e:
            logger.error(f"Error checking alert rules: {e}")
    
    def _create_alert(self, severity: AlertSeverity, title: str, description: str,
                     metric_name: str, current_value: float, threshold_value: float):
        """Create a new alert."""
        try:
            alert = Alert(
                alert_id=f"alert_{int(time.time() * 1000)}_{hash(metric_name) % 10000}",
                severity=severity,
                title=title,
                description=description,
                metric_name=metric_name,
                current_value=current_value,
                threshold_value=threshold_value,
                timestamp=time.time(),
                resolved=False,
                resolved_at=None,
                metadata={}
            )
            
            self.alerts.append(alert)
            
            # Store in Redis if available
            if self.redis_client:
                try:
                    self.redis_client.lpush("alerts", json.dumps(asdict(alert)))
                except Exception as e:
                    logger.debug(f"Error storing alert in Redis: {e}")
            
            logger.warning(f"Alert created: {title} - {description} (Value: {current_value}, Threshold: {threshold_value})")
            
        except Exception as e:
            logger.error(f"Error creating alert: {e}")
    
    def _cleanup_old_data(self):
        """Cleanup old metrics and alerts."""
        try:
            current_time = time.time()
            retention_period = 24 * 3600  # 24 hours
            
            # Cleanup old alerts (keep resolved alerts for 24 hours)
            self.alerts = [
                alert for alert in self.alerts
                if not alert.resolved or (current_time - (alert.resolved_at or alert.timestamp)) < retention_period
            ]
            
        except Exception as e:
            logger.error(f"Error cleaning up old data: {e}")
    
    async def record_custom_metric(self, name: str, value: float, metric_type: MetricType = MetricType.GAUGE,
                                 unit: str = "count", labels: Dict[str, str] = None):
        """Record a custom metric."""
        try:
            self._record_metric(name, value, metric_type, unit, labels)
            
        except Exception as e:
            logger.error(f"Error recording custom metric {name}: {e}")
    
    async def create_alert_rule(self, metric_name: str, threshold: float, 
                              severity: AlertSeverity = AlertSeverity.WARNING,
                              comparison: str = "greater_than", description: str = ""):
        """Create a new alert rule."""
        try:
            self.alert_rules[metric_name] = {
                "threshold": threshold,
                "severity": severity,
                "comparison": comparison,
                "description": description or f"Alert for {metric_name}"
            }
            
            logger.info(f"Alert rule created for {metric_name}: {threshold} ({comparison})")
            
        except Exception as e:
            logger.error(f"Error creating alert rule: {e}")
    
    async def resolve_alert(self, alert_id: str) -> bool:
        """Resolve an alert."""
        try:
            for alert in self.alerts:
                if alert.alert_id == alert_id and not alert.resolved:
                    alert.resolved = True
                    alert.resolved_at = time.time()
                    
                    logger.info(f"Alert resolved: {alert.title}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error resolving alert {alert_id}: {e}")
            return False
    
    async def get_metrics(self, metric_name: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get metrics."""
        try:
            if metric_name:
                if metric_name in self.metrics:
                    metrics = list(self.metrics[metric_name])[-limit:]
                    return [asdict(metric) for metric in metrics]
                else:
                    return []
            else:
                # Return all metrics
                all_metrics = []
                for name, metric_list in self.metrics.items():
                    recent_metrics = list(metric_list)[-limit:]
                    all_metrics.extend([asdict(metric) for metric in recent_metrics])
                
                # Sort by timestamp
                all_metrics.sort(key=lambda x: x["timestamp"], reverse=True)
                return all_metrics[:limit]
            
        except Exception as e:
            logger.error(f"Error getting metrics: {e}")
            return []
    
    async def get_alerts(self, severity: AlertSeverity = None, resolved: bool = None) -> List[Dict[str, Any]]:
        """Get alerts."""
        try:
            filtered_alerts = self.alerts
            
            if severity:
                filtered_alerts = [alert for alert in filtered_alerts if alert.severity == severity]
            
            if resolved is not None:
                filtered_alerts = [alert for alert in filtered_alerts if alert.resolved == resolved]
            
            # Sort by timestamp (newest first)
            filtered_alerts.sort(key=lambda x: x.timestamp, reverse=True)
            
            return [asdict(alert) for alert in filtered_alerts]
            
        except Exception as e:
            logger.error(f"Error getting alerts: {e}")
            return []
    
    async def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health status."""
        try:
            # Count active alerts by severity
            active_alerts = [alert for alert in self.alerts if not alert.resolved]
            alert_counts = {
                "critical": sum(1 for alert in active_alerts if alert.severity == AlertSeverity.CRITICAL),
                "error": sum(1 for alert in active_alerts if alert.severity == AlertSeverity.ERROR),
                "warning": sum(1 for alert in active_alerts if alert.severity == AlertSeverity.WARNING),
                "info": sum(1 for alert in active_alerts if alert.severity == AlertSeverity.INFO)
            }
            
            # Determine overall health status
            if alert_counts["critical"] > 0:
                health_status = "critical"
            elif alert_counts["error"] > 0:
                health_status = "error"
            elif alert_counts["warning"] > 0:
                health_status = "warning"
            else:
                health_status = "healthy"
            
            # Get latest metrics
            latest_metrics = {}
            for name, metric_list in self.metrics.items():
                if metric_list:
                    latest_metrics[name] = metric_list[-1].value
            
            return {
                "health_status": health_status,
                "active_alerts": len(active_alerts),
                "alert_counts": alert_counts,
                "latest_metrics": latest_metrics,
                "monitoring_active": self.monitoring_active,
                "last_update": time.time()
            }
            
        except Exception as e:
            logger.error(f"Error getting system health: {e}")
            return {"health_status": "unknown", "error": str(e)}
    
    async def get_monitoring_status(self) -> Dict[str, Any]:
        """Get monitoring system status."""
        try:
            return {
                "monitoring_enabled": self.monitoring_enabled,
                "monitoring_active": self.monitoring_active,
                "collection_interval": self.collection_interval,
                "total_metrics": sum(len(metrics) for metrics in self.metrics.values()),
                "total_alerts": len(self.alerts),
                "active_alerts": len([alert for alert in self.alerts if not alert.resolved]),
                "alert_rules": len(self.alert_rules),
                "redis_connected": self.redis_client is not None
            }
            
        except Exception as e:
            logger.error(f"Error getting monitoring status: {e}")
            return {}


# Factory functions
def create_alert(severity: AlertSeverity, title: str, description: str) -> Alert:
    """Create a new alert."""
    return Alert(
        alert_id=f"alert_{int(time.time() * 1000)}",
        severity=severity,
        title=title,
        description=description,
        metric_name="unknown",
        current_value=0.0,
        threshold_value=0.0,
        timestamp=time.time(),
        resolved=False,
        resolved_at=None,
        metadata={}
    )


# Alias for compatibility
MonitoringSystem = MonitoringSystem
