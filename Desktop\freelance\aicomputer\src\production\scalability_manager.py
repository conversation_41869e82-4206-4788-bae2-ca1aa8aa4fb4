"""
Scalability Manager - Phase 7 Component

Advanced scalability management for production systems.
"""

import asyncio
import time
import json
import psutil
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from datetime import datetime, timedelta
from enum import Enum
import redis
import threading

from loguru import logger

from ..utils.config_manager import ConfigManager


class ScalingDirection(Enum):
    """Scaling direction options."""
    UP = "up"
    DOWN = "down"
    OUT = "out"
    IN = "in"


class ScalingTrigger(Enum):
    """Scaling trigger types."""
    CPU_USAGE = "cpu_usage"
    MEMORY_USAGE = "memory_usage"
    REQUEST_RATE = "request_rate"
    RESPONSE_TIME = "response_time"
    QUEUE_LENGTH = "queue_length"
    CUSTOM_METRIC = "custom_metric"


@dataclass
class ScalingRule:
    """Scaling rule definition."""
    rule_id: str
    name: str
    trigger: ScalingTrigger
    threshold_up: float
    threshold_down: float
    scaling_direction: ScalingDirection
    cooldown_period: int
    min_instances: int
    max_instances: int
    enabled: bool
    created_at: float


@dataclass
class ScalingEvent:
    """Scaling event record."""
    event_id: str
    rule_id: str
    trigger_metric: str
    trigger_value: float
    threshold_value: float
    scaling_direction: ScalingDirection
    instances_before: int
    instances_after: int
    timestamp: float
    success: bool
    error_message: Optional[str]


class ScalabilityManager:
    """
    Advanced scalability management system.
    
    Features:
    - Auto-scaling based on metrics
    - Horizontal and vertical scaling
    - Load balancing optimization
    - Resource allocation
    - Performance monitoring
    - Predictive scaling
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        
        # Scalability data
        self.scaling_rules: Dict[str, ScalingRule] = {}
        self.scaling_events: List[ScalingEvent] = []
        self.current_instances: int = 1
        self.metrics_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # Redis for coordination
        redis_host = self.config.get("redis.host", "localhost")
        redis_port = self.config.get("redis.port", 6379)
        redis_db = self.config.get("redis.db", 4)
        
        try:
            self.redis_client = redis.Redis(
                host=redis_host,
                port=redis_port,
                db=redis_db,
                decode_responses=True
            )
            self.redis_client.ping()
        except Exception as e:
            logger.warning(f"Redis connection failed, using in-memory storage: {e}")
            self.redis_client = None
        
        # Configuration
        self.scaling_enabled = self.config.get("scaling.enabled", True)
        self.monitoring_interval = self.config.get("scaling.monitoring_interval", 60)
        self.default_cooldown = self.config.get("scaling.default_cooldown", 300)
        self.min_instances = self.config.get("scaling.min_instances", 1)
        self.max_instances = self.config.get("scaling.max_instances", 10)
        
        # Monitoring state
        self.monitoring_active = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.last_scaling_time: Dict[str, float] = {}
        
        # Initialize default scaling rules
        self._initialize_default_rules()
        
        logger.info("Scalability Manager initialized")
    
    def _initialize_default_rules(self):
        """Initialize default scaling rules."""
        try:
            # CPU-based scaling rule
            cpu_rule = ScalingRule(
                rule_id="cpu_scaling",
                name="CPU-based Auto Scaling",
                trigger=ScalingTrigger.CPU_USAGE,
                threshold_up=80.0,
                threshold_down=30.0,
                scaling_direction=ScalingDirection.OUT,
                cooldown_period=self.default_cooldown,
                min_instances=self.min_instances,
                max_instances=self.max_instances,
                enabled=True,
                created_at=time.time()
            )
            self.scaling_rules[cpu_rule.rule_id] = cpu_rule
            
            # Memory-based scaling rule
            memory_rule = ScalingRule(
                rule_id="memory_scaling",
                name="Memory-based Auto Scaling",
                trigger=ScalingTrigger.MEMORY_USAGE,
                threshold_up=85.0,
                threshold_down=40.0,
                scaling_direction=ScalingDirection.OUT,
                cooldown_period=self.default_cooldown,
                min_instances=self.min_instances,
                max_instances=self.max_instances,
                enabled=True,
                created_at=time.time()
            )
            self.scaling_rules[memory_rule.rule_id] = memory_rule
            
        except Exception as e:
            logger.error(f"Error initializing scaling rules: {e}")
    
    async def start_monitoring(self):
        """Start scaling monitoring."""
        try:
            if not self.scaling_enabled:
                logger.info("Scaling is disabled")
                return
            
            self.monitoring_active = True
            
            # Start monitoring thread
            self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitor_thread.start()
            
            logger.info("Scaling monitoring started")
            
        except Exception as e:
            logger.error(f"Error starting scaling monitoring: {e}")
    
    async def stop_monitoring(self):
        """Stop scaling monitoring."""
        self.monitoring_active = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5.0)
        
        logger.info("Scaling monitoring stopped")
    
    def _monitoring_loop(self):
        """Scaling monitoring loop."""
        while self.monitoring_active:
            try:
                # Collect current metrics
                self._collect_scaling_metrics()
                
                # Evaluate scaling rules
                self._evaluate_scaling_rules()
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"Error in scaling monitoring loop: {e}")
                time.sleep(self.monitoring_interval)
    
    def _collect_scaling_metrics(self):
        """Collect metrics for scaling decisions."""
        try:
            current_time = time.time()
            
            # CPU metrics
            cpu_usage = psutil.cpu_percent(interval=1)
            self.metrics_history["cpu_usage"].append((current_time, cpu_usage))
            
            # Memory metrics
            memory = psutil.virtual_memory()
            self.metrics_history["memory_usage"].append((current_time, memory.percent))
            
            # Simulated request rate (in production, this would come from load balancer)
            import random
            request_rate = random.uniform(50, 200)  # Requests per second
            self.metrics_history["request_rate"].append((current_time, request_rate))
            
            # Simulated response time
            response_time = random.uniform(100, 500)  # Milliseconds
            self.metrics_history["response_time"].append((current_time, response_time))
            
        except Exception as e:
            logger.error(f"Error collecting scaling metrics: {e}")
    
    def _evaluate_scaling_rules(self):
        """Evaluate scaling rules against current metrics."""
        try:
            current_time = time.time()
            
            for rule in self.scaling_rules.values():
                if not rule.enabled:
                    continue
                
                # Check cooldown period
                last_scaling = self.last_scaling_time.get(rule.rule_id, 0)
                if current_time - last_scaling < rule.cooldown_period:
                    continue
                
                # Get current metric value
                metric_name = rule.trigger.value
                if metric_name not in self.metrics_history or not self.metrics_history[metric_name]:
                    continue
                
                # Get average of last 3 readings
                recent_values = list(self.metrics_history[metric_name])[-3:]
                if len(recent_values) < 3:
                    continue
                
                avg_value = sum(value for _, value in recent_values) / len(recent_values)
                
                # Check scaling conditions
                should_scale_up = avg_value > rule.threshold_up and self.current_instances < rule.max_instances
                should_scale_down = avg_value < rule.threshold_down and self.current_instances > rule.min_instances
                
                if should_scale_up:
                    asyncio.run(self._execute_scaling(rule, ScalingDirection.OUT, avg_value, rule.threshold_up))
                elif should_scale_down:
                    asyncio.run(self._execute_scaling(rule, ScalingDirection.IN, avg_value, rule.threshold_down))
                
        except Exception as e:
            logger.error(f"Error evaluating scaling rules: {e}")
    
    async def _execute_scaling(self, rule: ScalingRule, direction: ScalingDirection, 
                             current_value: float, threshold_value: float):
        """Execute scaling action."""
        try:
            instances_before = self.current_instances
            
            # Calculate new instance count
            if direction == ScalingDirection.OUT:
                # Scale out (add instances)
                new_instances = min(instances_before + 1, rule.max_instances)
            elif direction == ScalingDirection.IN:
                # Scale in (remove instances)
                new_instances = max(instances_before - 1, rule.min_instances)
            else:
                # Vertical scaling not implemented in this example
                new_instances = instances_before
            
            if new_instances == instances_before:
                return  # No scaling needed
            
            # Simulate scaling operation
            success = await self._perform_scaling_operation(instances_before, new_instances)
            
            if success:
                self.current_instances = new_instances
                self.last_scaling_time[rule.rule_id] = time.time()
            
            # Record scaling event
            event = ScalingEvent(
                event_id=f"scale_evt_{int(time.time() * 1000)}",
                rule_id=rule.rule_id,
                trigger_metric=rule.trigger.value,
                trigger_value=current_value,
                threshold_value=threshold_value,
                scaling_direction=direction,
                instances_before=instances_before,
                instances_after=new_instances if success else instances_before,
                timestamp=time.time(),
                success=success,
                error_message=None if success else "Scaling operation failed"
            )
            
            self.scaling_events.append(event)
            
            # Store in Redis if available
            if self.redis_client:
                try:
                    self.redis_client.lpush("scaling_events", json.dumps(asdict(event)))
                    self.redis_client.ltrim("scaling_events", 0, 999)  # Keep last 1000
                except Exception as e:
                    logger.debug(f"Error storing scaling event in Redis: {e}")
            
            action = "scaled out" if direction == ScalingDirection.OUT else "scaled in"
            logger.info(f"Scaling event: {action} from {instances_before} to {new_instances} instances "
                       f"(trigger: {rule.trigger.value}={current_value:.1f}, threshold={threshold_value})")
            
        except Exception as e:
            logger.error(f"Error executing scaling: {e}")
    
    async def _perform_scaling_operation(self, current_instances: int, target_instances: int) -> bool:
        """Perform the actual scaling operation."""
        try:
            # Simulate scaling operation
            logger.info(f"Performing scaling operation: {current_instances} -> {target_instances} instances")
            
            # Simulate deployment time
            await asyncio.sleep(2)
            
            # Simulate 95% success rate
            import random
            return random.random() > 0.05
            
        except Exception as e:
            logger.error(f"Error performing scaling operation: {e}")
            return False
    
    async def create_scaling_rule(self, name: str, trigger: ScalingTrigger,
                                threshold_up: float, threshold_down: float,
                                scaling_direction: ScalingDirection = ScalingDirection.OUT,
                                min_instances: int = None, max_instances: int = None) -> str:
        """Create a new scaling rule."""
        try:
            rule_id = f"rule_{int(time.time() * 1000)}"
            
            rule = ScalingRule(
                rule_id=rule_id,
                name=name,
                trigger=trigger,
                threshold_up=threshold_up,
                threshold_down=threshold_down,
                scaling_direction=scaling_direction,
                cooldown_period=self.default_cooldown,
                min_instances=min_instances or self.min_instances,
                max_instances=max_instances or self.max_instances,
                enabled=True,
                created_at=time.time()
            )
            
            self.scaling_rules[rule_id] = rule
            
            logger.info(f"Scaling rule created: {name}")
            return rule_id
            
        except Exception as e:
            logger.error(f"Error creating scaling rule: {e}")
            return ""
    
    async def update_scaling_rule(self, rule_id: str, **kwargs) -> bool:
        """Update an existing scaling rule."""
        try:
            if rule_id not in self.scaling_rules:
                return False
            
            rule = self.scaling_rules[rule_id]
            
            # Update allowed fields
            if "threshold_up" in kwargs:
                rule.threshold_up = kwargs["threshold_up"]
            if "threshold_down" in kwargs:
                rule.threshold_down = kwargs["threshold_down"]
            if "enabled" in kwargs:
                rule.enabled = kwargs["enabled"]
            if "min_instances" in kwargs:
                rule.min_instances = kwargs["min_instances"]
            if "max_instances" in kwargs:
                rule.max_instances = kwargs["max_instances"]
            
            logger.info(f"Scaling rule updated: {rule_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating scaling rule: {e}")
            return False
    
    async def manual_scale(self, target_instances: int, reason: str = "Manual scaling") -> bool:
        """Manually scale to target instance count."""
        try:
            if target_instances < self.min_instances or target_instances > self.max_instances:
                logger.warning(f"Target instances {target_instances} outside allowed range "
                             f"({self.min_instances}-{self.max_instances})")
                return False
            
            instances_before = self.current_instances
            
            if target_instances == instances_before:
                logger.info("No scaling needed - already at target instance count")
                return True
            
            # Perform scaling
            success = await self._perform_scaling_operation(instances_before, target_instances)
            
            if success:
                self.current_instances = target_instances
            
            # Record manual scaling event
            direction = ScalingDirection.OUT if target_instances > instances_before else ScalingDirection.IN
            
            event = ScalingEvent(
                event_id=f"manual_scale_{int(time.time() * 1000)}",
                rule_id="manual",
                trigger_metric="manual",
                trigger_value=0.0,
                threshold_value=0.0,
                scaling_direction=direction,
                instances_before=instances_before,
                instances_after=target_instances if success else instances_before,
                timestamp=time.time(),
                success=success,
                error_message=None if success else "Manual scaling failed"
            )
            
            self.scaling_events.append(event)
            
            logger.info(f"Manual scaling: {instances_before} -> {target_instances} instances. "
                       f"Reason: {reason}. Success: {success}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error in manual scaling: {e}")
            return False
    
    async def get_scaling_metrics(self) -> Dict[str, Any]:
        """Get current scaling metrics."""
        try:
            current_metrics = {}
            
            for metric_name, history in self.metrics_history.items():
                if history:
                    recent_values = [value for _, value in list(history)[-10:]]
                    current_metrics[metric_name] = {
                        "current": recent_values[-1] if recent_values else 0,
                        "average_10": sum(recent_values) / len(recent_values) if recent_values else 0,
                        "min_10": min(recent_values) if recent_values else 0,
                        "max_10": max(recent_values) if recent_values else 0
                    }
            
            return current_metrics
            
        except Exception as e:
            logger.error(f"Error getting scaling metrics: {e}")
            return {}
    
    async def get_scaling_status(self) -> Dict[str, Any]:
        """Get current scaling status."""
        try:
            # Count scaling events by type
            recent_events = [
                event for event in self.scaling_events
                if time.time() - event.timestamp < 86400  # Last 24 hours
            ]
            
            scale_out_events = sum(1 for e in recent_events if e.scaling_direction == ScalingDirection.OUT)
            scale_in_events = sum(1 for e in recent_events if e.scaling_direction == ScalingDirection.IN)
            
            return {
                "scaling_enabled": self.scaling_enabled,
                "monitoring_active": self.monitoring_active,
                "current_instances": self.current_instances,
                "min_instances": self.min_instances,
                "max_instances": self.max_instances,
                "active_rules": len([r for r in self.scaling_rules.values() if r.enabled]),
                "total_rules": len(self.scaling_rules),
                "total_scaling_events": len(self.scaling_events),
                "recent_events_24h": len(recent_events),
                "scale_out_events_24h": scale_out_events,
                "scale_in_events_24h": scale_in_events,
                "last_scaling_time": max(self.last_scaling_time.values()) if self.last_scaling_time else None
            }
            
        except Exception as e:
            logger.error(f"Error getting scaling status: {e}")
            return {}
    
    async def get_scaling_recommendations(self) -> List[Dict[str, Any]]:
        """Get scaling recommendations based on current metrics."""
        try:
            recommendations = []
            
            # Analyze recent metrics
            current_metrics = await self.get_scaling_metrics()
            
            for metric_name, data in current_metrics.items():
                current_value = data["current"]
                average_value = data["average_10"]
                
                # CPU recommendations
                if metric_name == "cpu_usage":
                    if average_value > 85:
                        recommendations.append({
                            "type": "scale_out",
                            "reason": f"High CPU usage: {average_value:.1f}%",
                            "priority": "high",
                            "suggested_instances": min(self.current_instances + 2, self.max_instances)
                        })
                    elif average_value < 20 and self.current_instances > self.min_instances:
                        recommendations.append({
                            "type": "scale_in",
                            "reason": f"Low CPU usage: {average_value:.1f}%",
                            "priority": "medium",
                            "suggested_instances": max(self.current_instances - 1, self.min_instances)
                        })
                
                # Memory recommendations
                elif metric_name == "memory_usage":
                    if average_value > 90:
                        recommendations.append({
                            "type": "scale_out",
                            "reason": f"High memory usage: {average_value:.1f}%",
                            "priority": "critical",
                            "suggested_instances": min(self.current_instances + 1, self.max_instances)
                        })
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error getting scaling recommendations: {e}")
            return []
    
    async def get_scaling_events(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent scaling events."""
        try:
            # Sort by timestamp (newest first)
            sorted_events = sorted(self.scaling_events, key=lambda x: x.timestamp, reverse=True)
            
            return [asdict(event) for event in sorted_events[:limit]]
            
        except Exception as e:
            logger.error(f"Error getting scaling events: {e}")
            return []


# Factory functions
def create_scaling_rule(name: str, trigger: ScalingTrigger, threshold_up: float, threshold_down: float) -> ScalingRule:
    """Create a new scaling rule."""
    return ScalingRule(
        rule_id=f"rule_{int(time.time() * 1000)}",
        name=name,
        trigger=trigger,
        threshold_up=threshold_up,
        threshold_down=threshold_down,
        scaling_direction=ScalingDirection.OUT,
        cooldown_period=300,
        min_instances=1,
        max_instances=10,
        enabled=True,
        created_at=time.time()
    )


# Alias for compatibility
ScalabilityManager = ScalabilityManager
