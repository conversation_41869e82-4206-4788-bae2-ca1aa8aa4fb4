"""
Security Hardening - Phase 7 Component

Advanced security hardening for production systems.
"""

import asyncio
import time
import json
import hashlib
import secrets
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict
from datetime import datetime, timed<PERSON>ta
from enum import Enum
import redis
import os

from loguru import logger

from ..utils.config_manager import ConfigManager


class SecurityLevel(Enum):
    """Security levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ThreatType(Enum):
    """Types of security threats."""
    UNAUTHORIZED_ACCESS = "unauthorized_access"
    DATA_BREACH = "data_breach"
    MALWARE = "malware"
    DOS_ATTACK = "dos_attack"
    PRIVILEGE_ESCALATION = "privilege_escalation"
    INJECTION_ATTACK = "injection_attack"


@dataclass
class SecurityEvent:
    """Security event record."""
    event_id: str
    event_type: str
    threat_type: ThreatType
    severity: SecurityLevel
    source_ip: str
    user_id: Optional[str]
    description: str
    timestamp: float
    resolved: bool
    metadata: Dict[str, Any]


@dataclass
class SecurityPolicy:
    """Security policy definition."""
    policy_id: str
    name: str
    description: str
    rules: List[Dict[str, Any]]
    enabled: bool
    created_at: float
    updated_at: float


class SecurityHardening:
    """
    Advanced security hardening system.
    
    Features:
    - Real-time threat detection
    - Security policy enforcement
    - Access control management
    - Encryption management
    - Audit logging
    - Vulnerability scanning
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        
        # Security data
        self.security_events: List[SecurityEvent] = []
        self.security_policies: Dict[str, SecurityPolicy] = {}
        self.failed_login_attempts: Dict[str, List[float]] = defaultdict(list)
        self.blocked_ips: Dict[str, float] = {}
        
        # Redis for coordination
        redis_host = self.config.get("redis.host", "localhost")
        redis_port = self.config.get("redis.port", 6379)
        redis_db = self.config.get("redis.db", 3)
        
        try:
            self.redis_client = redis.Redis(
                host=redis_host,
                port=redis_port,
                db=redis_db,
                decode_responses=True
            )
            self.redis_client.ping()
        except Exception as e:
            logger.warning(f"Redis connection failed, using in-memory storage: {e}")
            self.redis_client = None
        
        # Configuration
        self.security_enabled = self.config.get("security.enabled", True)
        self.max_login_attempts = self.config.get("security.max_login_attempts", 5)
        self.lockout_duration = self.config.get("security.lockout_duration", 900)  # 15 minutes
        self.session_timeout = self.config.get("security.session_timeout", 3600)  # 1 hour
        
        # Initialize default policies
        self._initialize_default_policies()
        
        logger.info("Security Hardening initialized")
    
    def _initialize_default_policies(self):
        """Initialize default security policies."""
        try:
            # Password policy
            password_policy = SecurityPolicy(
                policy_id="password_policy",
                name="Password Policy",
                description="Enforce strong password requirements",
                rules=[
                    {"type": "min_length", "value": 8},
                    {"type": "require_uppercase", "value": True},
                    {"type": "require_lowercase", "value": True},
                    {"type": "require_numbers", "value": True},
                    {"type": "require_special_chars", "value": True}
                ],
                enabled=True,
                created_at=time.time(),
                updated_at=time.time()
            )
            self.security_policies[password_policy.policy_id] = password_policy
            
            # Access control policy
            access_policy = SecurityPolicy(
                policy_id="access_control",
                name="Access Control Policy",
                description="Control system access and permissions",
                rules=[
                    {"type": "require_authentication", "value": True},
                    {"type": "session_timeout", "value": self.session_timeout},
                    {"type": "max_concurrent_sessions", "value": 3}
                ],
                enabled=True,
                created_at=time.time(),
                updated_at=time.time()
            )
            self.security_policies[access_policy.policy_id] = access_policy
            
            # Data protection policy
            data_policy = SecurityPolicy(
                policy_id="data_protection",
                name="Data Protection Policy",
                description="Protect sensitive data",
                rules=[
                    {"type": "encrypt_at_rest", "value": True},
                    {"type": "encrypt_in_transit", "value": True},
                    {"type": "data_classification", "value": True}
                ],
                enabled=True,
                created_at=time.time(),
                updated_at=time.time()
            )
            self.security_policies[data_policy.policy_id] = data_policy
            
        except Exception as e:
            logger.error(f"Error initializing security policies: {e}")
    
    async def validate_login_attempt(self, user_id: str, source_ip: str, success: bool) -> Dict[str, Any]:
        """Validate and track login attempts."""
        try:
            current_time = time.time()
            
            # Check if IP is blocked
            if source_ip in self.blocked_ips:
                if current_time < self.blocked_ips[source_ip]:
                    return {
                        "allowed": False,
                        "reason": "IP blocked due to suspicious activity",
                        "blocked_until": self.blocked_ips[source_ip]
                    }
                else:
                    # Unblock IP
                    del self.blocked_ips[source_ip]
            
            if success:
                # Clear failed attempts on successful login
                if user_id in self.failed_login_attempts:
                    del self.failed_login_attempts[user_id]
                
                # Log successful login
                await self._log_security_event(
                    event_type="login_success",
                    threat_type=ThreatType.UNAUTHORIZED_ACCESS,
                    severity=SecurityLevel.LOW,
                    source_ip=source_ip,
                    user_id=user_id,
                    description=f"Successful login for user {user_id}"
                )
                
                return {"allowed": True, "reason": "Login successful"}
            
            else:
                # Track failed attempt
                self.failed_login_attempts[user_id].append(current_time)
                
                # Clean old attempts (older than 1 hour)
                self.failed_login_attempts[user_id] = [
                    attempt for attempt in self.failed_login_attempts[user_id]
                    if current_time - attempt < 3600
                ]
                
                failed_count = len(self.failed_login_attempts[user_id])
                
                # Check if max attempts exceeded
                if failed_count >= self.max_login_attempts:
                    # Block IP
                    self.blocked_ips[source_ip] = current_time + self.lockout_duration
                    
                    # Log security event
                    await self._log_security_event(
                        event_type="login_failure_threshold",
                        threat_type=ThreatType.UNAUTHORIZED_ACCESS,
                        severity=SecurityLevel.HIGH,
                        source_ip=source_ip,
                        user_id=user_id,
                        description=f"Max login attempts exceeded for user {user_id} from IP {source_ip}"
                    )
                    
                    return {
                        "allowed": False,
                        "reason": f"Max login attempts exceeded. IP blocked for {self.lockout_duration} seconds",
                        "failed_attempts": failed_count
                    }
                
                # Log failed attempt
                await self._log_security_event(
                    event_type="login_failure",
                    threat_type=ThreatType.UNAUTHORIZED_ACCESS,
                    severity=SecurityLevel.MEDIUM,
                    source_ip=source_ip,
                    user_id=user_id,
                    description=f"Failed login attempt for user {user_id}"
                )
                
                return {
                    "allowed": True,
                    "reason": f"Login failed. {self.max_login_attempts - failed_count} attempts remaining",
                    "failed_attempts": failed_count
                }
            
        except Exception as e:
            logger.error(f"Error validating login attempt: {e}")
            return {"allowed": False, "reason": "Security validation error"}
    
    async def _log_security_event(self, event_type: str, threat_type: ThreatType,
                                 severity: SecurityLevel, source_ip: str,
                                 user_id: Optional[str], description: str):
        """Log a security event."""
        try:
            event = SecurityEvent(
                event_id=f"sec_evt_{int(time.time() * 1000)}_{secrets.token_hex(4)}",
                event_type=event_type,
                threat_type=threat_type,
                severity=severity,
                source_ip=source_ip,
                user_id=user_id,
                description=description,
                timestamp=time.time(),
                resolved=False,
                metadata={}
            )
            
            self.security_events.append(event)
            
            # Store in Redis if available
            if self.redis_client:
                try:
                    self.redis_client.lpush("security_events", json.dumps(asdict(event)))
                    self.redis_client.ltrim("security_events", 0, 9999)  # Keep last 10000
                except Exception as e:
                    logger.debug(f"Error storing security event in Redis: {e}")
            
            logger.warning(f"Security event: {event_type} - {description}")
            
        except Exception as e:
            logger.error(f"Error logging security event: {e}")
    
    async def validate_password(self, password: str) -> Dict[str, Any]:
        """Validate password against security policy."""
        try:
            password_policy = self.security_policies.get("password_policy")
            if not password_policy or not password_policy.enabled:
                return {"valid": True, "issues": []}
            
            issues = []
            
            for rule in password_policy.rules:
                rule_type = rule["type"]
                rule_value = rule["value"]
                
                if rule_type == "min_length" and len(password) < rule_value:
                    issues.append(f"Password must be at least {rule_value} characters long")
                
                elif rule_type == "require_uppercase" and rule_value:
                    if not any(c.isupper() for c in password):
                        issues.append("Password must contain at least one uppercase letter")
                
                elif rule_type == "require_lowercase" and rule_value:
                    if not any(c.islower() for c in password):
                        issues.append("Password must contain at least one lowercase letter")
                
                elif rule_type == "require_numbers" and rule_value:
                    if not any(c.isdigit() for c in password):
                        issues.append("Password must contain at least one number")
                
                elif rule_type == "require_special_chars" and rule_value:
                    special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
                    if not any(c in special_chars for c in password):
                        issues.append("Password must contain at least one special character")
            
            return {
                "valid": len(issues) == 0,
                "issues": issues,
                "strength_score": self._calculate_password_strength(password)
            }
            
        except Exception as e:
            logger.error(f"Error validating password: {e}")
            return {"valid": False, "issues": ["Password validation error"]}
    
    def _calculate_password_strength(self, password: str) -> float:
        """Calculate password strength score (0-1)."""
        try:
            score = 0.0
            
            # Length score
            if len(password) >= 8:
                score += 0.2
            if len(password) >= 12:
                score += 0.1
            if len(password) >= 16:
                score += 0.1
            
            # Character variety
            if any(c.isupper() for c in password):
                score += 0.15
            if any(c.islower() for c in password):
                score += 0.15
            if any(c.isdigit() for c in password):
                score += 0.15
            
            special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
            if any(c in special_chars for c in password):
                score += 0.15
            
            return min(score, 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating password strength: {e}")
            return 0.0
    
    async def encrypt_data(self, data: str, key: Optional[str] = None) -> Dict[str, Any]:
        """Encrypt sensitive data."""
        try:
            # Simple encryption simulation (in production, use proper encryption)
            if key is None:
                key = self.config.get("security.encryption_key", "default_key")
            
            # Create a simple hash-based encryption simulation
            data_bytes = data.encode('utf-8')
            key_bytes = key.encode('utf-8')
            
            # Simple XOR encryption (NOT for production use)
            encrypted_bytes = bytearray()
            for i, byte in enumerate(data_bytes):
                encrypted_bytes.append(byte ^ key_bytes[i % len(key_bytes)])
            
            encrypted_data = encrypted_bytes.hex()
            
            return {
                "success": True,
                "encrypted_data": encrypted_data,
                "algorithm": "simple_xor",
                "key_id": hashlib.sha256(key_bytes).hexdigest()[:8]
            }
            
        except Exception as e:
            logger.error(f"Error encrypting data: {e}")
            return {"success": False, "error": str(e)}
    
    async def decrypt_data(self, encrypted_data: str, key: Optional[str] = None) -> Dict[str, Any]:
        """Decrypt sensitive data."""
        try:
            if key is None:
                key = self.config.get("security.encryption_key", "default_key")
            
            # Reverse the simple XOR encryption
            encrypted_bytes = bytes.fromhex(encrypted_data)
            key_bytes = key.encode('utf-8')
            
            decrypted_bytes = bytearray()
            for i, byte in enumerate(encrypted_bytes):
                decrypted_bytes.append(byte ^ key_bytes[i % len(key_bytes)])
            
            decrypted_data = decrypted_bytes.decode('utf-8')
            
            return {
                "success": True,
                "decrypted_data": decrypted_data
            }
            
        except Exception as e:
            logger.error(f"Error decrypting data: {e}")
            return {"success": False, "error": str(e)}
    
    async def scan_for_vulnerabilities(self) -> Dict[str, Any]:
        """Scan system for security vulnerabilities."""
        try:
            vulnerabilities = []
            
            # Check password policies
            if "password_policy" not in self.security_policies:
                vulnerabilities.append({
                    "type": "missing_policy",
                    "severity": "medium",
                    "description": "Password policy not configured"
                })
            
            # Check for weak encryption
            encryption_key = self.config.get("security.encryption_key", "default_key")
            if encryption_key == "default_key":
                vulnerabilities.append({
                    "type": "weak_encryption",
                    "severity": "high",
                    "description": "Default encryption key in use"
                })
            
            # Check session timeout
            if self.session_timeout > 7200:  # 2 hours
                vulnerabilities.append({
                    "type": "long_session_timeout",
                    "severity": "low",
                    "description": "Session timeout is longer than recommended"
                })
            
            # Check for recent security events
            recent_events = [
                event for event in self.security_events
                if time.time() - event.timestamp < 3600  # Last hour
            ]
            
            high_severity_events = [
                event for event in recent_events
                if event.severity in [SecurityLevel.HIGH, SecurityLevel.CRITICAL]
            ]
            
            if len(high_severity_events) > 5:
                vulnerabilities.append({
                    "type": "high_security_activity",
                    "severity": "high",
                    "description": f"{len(high_severity_events)} high-severity security events in the last hour"
                })
            
            return {
                "scan_timestamp": time.time(),
                "vulnerabilities_found": len(vulnerabilities),
                "vulnerabilities": vulnerabilities,
                "overall_risk": self._calculate_overall_risk(vulnerabilities)
            }
            
        except Exception as e:
            logger.error(f"Error scanning for vulnerabilities: {e}")
            return {"error": str(e)}
    
    def _calculate_overall_risk(self, vulnerabilities: List[Dict[str, Any]]) -> str:
        """Calculate overall security risk level."""
        try:
            if not vulnerabilities:
                return "low"
            
            severity_counts = {"low": 0, "medium": 0, "high": 0, "critical": 0}
            for vuln in vulnerabilities:
                severity = vuln.get("severity", "low")
                severity_counts[severity] += 1
            
            if severity_counts["critical"] > 0:
                return "critical"
            elif severity_counts["high"] > 2:
                return "high"
            elif severity_counts["high"] > 0 or severity_counts["medium"] > 3:
                return "medium"
            else:
                return "low"
                
        except Exception as e:
            logger.error(f"Error calculating overall risk: {e}")
            return "unknown"
    
    async def get_security_status(self) -> Dict[str, Any]:
        """Get overall security status."""
        try:
            # Count events by severity
            recent_events = [
                event for event in self.security_events
                if time.time() - event.timestamp < 86400  # Last 24 hours
            ]
            
            event_counts = {
                "critical": sum(1 for e in recent_events if e.severity == SecurityLevel.CRITICAL),
                "high": sum(1 for e in recent_events if e.severity == SecurityLevel.HIGH),
                "medium": sum(1 for e in recent_events if e.severity == SecurityLevel.MEDIUM),
                "low": sum(1 for e in recent_events if e.severity == SecurityLevel.LOW)
            }
            
            return {
                "security_enabled": self.security_enabled,
                "active_policies": len([p for p in self.security_policies.values() if p.enabled]),
                "total_security_events": len(self.security_events),
                "recent_events_24h": len(recent_events),
                "event_counts_24h": event_counts,
                "blocked_ips": len(self.blocked_ips),
                "failed_login_tracking": len(self.failed_login_attempts),
                "last_vulnerability_scan": "not_performed"
            }
            
        except Exception as e:
            logger.error(f"Error getting security status: {e}")
            return {}
    
    async def create_security_policy(self, name: str, description: str, 
                                   rules: List[Dict[str, Any]]) -> str:
        """Create a new security policy."""
        try:
            policy_id = f"policy_{int(time.time() * 1000)}"
            
            policy = SecurityPolicy(
                policy_id=policy_id,
                name=name,
                description=description,
                rules=rules,
                enabled=True,
                created_at=time.time(),
                updated_at=time.time()
            )
            
            self.security_policies[policy_id] = policy
            
            logger.info(f"Security policy created: {name}")
            return policy_id
            
        except Exception as e:
            logger.error(f"Error creating security policy: {e}")
            return ""
    
    async def get_security_events(self, severity: SecurityLevel = None, 
                                limit: int = 100) -> List[Dict[str, Any]]:
        """Get security events."""
        try:
            events = self.security_events
            
            if severity:
                events = [event for event in events if event.severity == severity]
            
            # Sort by timestamp (newest first)
            events.sort(key=lambda x: x.timestamp, reverse=True)
            
            return [asdict(event) for event in events[:limit]]
            
        except Exception as e:
            logger.error(f"Error getting security events: {e}")
            return []


# Factory functions
def create_security_event(event_type: str, threat_type: ThreatType, severity: SecurityLevel) -> SecurityEvent:
    """Create a new security event."""
    return SecurityEvent(
        event_id=f"sec_evt_{int(time.time() * 1000)}",
        event_type=event_type,
        threat_type=threat_type,
        severity=severity,
        source_ip="unknown",
        user_id=None,
        description="Security event",
        timestamp=time.time(),
        resolved=False,
        metadata={}
    )


# Alias for compatibility
SecurityHardening = SecurityHardening
