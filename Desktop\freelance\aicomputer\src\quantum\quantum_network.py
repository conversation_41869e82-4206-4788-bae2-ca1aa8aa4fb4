"""
Quantum Communication Network - Phase 9 Component

Advanced quantum internet with entanglement-based communication protocols.
"""

import asyncio
import time
import json
import uuid
import hashlib
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from datetime import datetime, <PERSON><PERSON><PERSON>
from enum import Enum
import threading
import math
import random

from loguru import logger

try:
    from ..utils.config_manager import ConfigManager
    from ..quantum.quantum_ai_processor import QuantumAIProcessor, QuantumBit, QuantumState
except ImportError:
    from src.utils.config_manager import ConfigManager
    from src.quantum.quantum_ai_processor import QuantumAIProcessor, QuantumBit, QuantumState


class QuantumProtocol(Enum):
    """Quantum communication protocols."""
    BB84 = "bb84"  # Quantum key distribution
    E91 = "e91"    # Entanglement-based QKD
    TELEPORTATION = "teleportation"  # Quantum teleportation
    SUPERDENSE = "superdense"  # Superdense coding
    DISTRIBUTED_COMPUTING = "distributed_computing"  # Quantum distributed computing


class NetworkTopology(Enum):
    """Network topology types."""
    STAR = "star"
    MESH = "mesh"
    RING = "ring"
    TREE = "tree"
    HYBRID = "hybrid"


class QuantumChannelState(Enum):
    """Quantum channel states."""
    IDLE = "idle"
    ESTABLISHING = "establishing"
    ENTANGLED = "entangled"
    TRANSMITTING = "transmitting"
    ERROR = "error"
    DISCONNECTED = "disconnected"


@dataclass
class QuantumNode:
    """Quantum network node."""
    node_id: str
    node_name: str
    location: Tuple[float, float]  # Coordinates
    quantum_capacity: int
    available_qubits: int
    entanglement_fidelity: float
    processing_power: float
    network_interfaces: List[str]
    status: str
    created_at: float
    metadata: Dict[str, Any]


@dataclass
class QuantumChannel:
    """Quantum communication channel."""
    channel_id: str
    source_node: str
    target_node: str
    protocol: QuantumProtocol
    state: QuantumChannelState
    entangled_pairs: List[Tuple[str, str]]
    fidelity: float
    transmission_rate: float  # qubits per second
    error_rate: float
    established_at: float
    last_activity: float
    metadata: Dict[str, Any]


@dataclass
class QuantumMessage:
    """Quantum message for transmission."""
    message_id: str
    sender_node: str
    receiver_node: str
    protocol: QuantumProtocol
    quantum_data: List[Dict[str, Any]]
    classical_data: Dict[str, Any]
    priority: int
    encryption_key: Optional[str]
    timestamp: float
    expiry_time: float
    metadata: Dict[str, Any]


@dataclass
class QuantumRoute:
    """Quantum routing information."""
    route_id: str
    source_node: str
    destination_node: str
    path_nodes: List[str]
    total_distance: float
    estimated_fidelity: float
    estimated_latency: float
    route_cost: float
    created_at: float


class QuantumNetwork:
    """
    Advanced Quantum Communication Network.
    
    Features:
    - Quantum key distribution (QKD)
    - Quantum teleportation protocols
    - Entanglement distribution
    - Quantum internet routing
    - Distributed quantum computing
    - Quantum error correction
    - Network topology optimization
    """
    
    def __init__(self, config_manager: ConfigManager, quantum_processor: QuantumAIProcessor = None):
        self.config = config_manager
        self.quantum_processor = quantum_processor
        
        # Network components
        self.nodes: Dict[str, QuantumNode] = {}
        self.channels: Dict[str, QuantumChannel] = {}
        self.routing_table: Dict[str, List[QuantumRoute]] = defaultdict(list)
        self.message_queue: deque = deque(maxlen=10000)
        self.active_transmissions: Dict[str, QuantumMessage] = {}
        
        # Network configuration
        self.network_topology = NetworkTopology.HYBRID
        self.max_nodes = self.config.get("quantum_network.max_nodes", 100)
        self.max_channels_per_node = self.config.get("quantum_network.max_channels", 10)
        self.default_fidelity_threshold = self.config.get("quantum_network.fidelity_threshold", 0.9)
        self.quantum_repeater_distance = self.config.get("quantum_network.repeater_distance", 100.0)
        
        # Protocol parameters
        self.qkd_key_length = self.config.get("quantum_network.qkd_key_length", 256)
        self.teleportation_success_rate = self.config.get("quantum_network.teleportation_rate", 0.95)
        self.entanglement_generation_rate = self.config.get("quantum_network.entanglement_rate", 1000)  # Hz
        
        # Network monitoring
        self.network_active = False
        self.network_thread: Optional[threading.Thread] = None
        self.performance_metrics = {
            "total_messages_sent": 0,
            "successful_transmissions": 0,
            "failed_transmissions": 0,
            "average_fidelity": 0.0,
            "network_throughput": 0.0,
            "entanglement_efficiency": 0.0
        }
        
        # Initialize network
        self._initialize_network()
        
        logger.info("Quantum Network initialized")
    
    def _initialize_network(self):
        """Initialize the quantum network."""
        try:
            # Create initial network nodes
            self._create_initial_nodes()
            
            # Establish initial quantum channels
            self._establish_initial_channels()
            
            # Initialize routing protocols
            self._initialize_routing_protocols()
            
            logger.info(f"Quantum network initialized with {len(self.nodes)} nodes and {len(self.channels)} channels")
            
        except Exception as e:
            logger.error(f"Error initializing quantum network: {e}")

    def _calculate_distance(self, pos1: Tuple[float, float], pos2: Tuple[float, float]) -> float:
        """Calculate Euclidean distance between two positions."""
        return math.sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)

    def _initialize_routing_protocols(self):
        """Initialize quantum routing protocols."""
        try:
            # Calculate routes between all node pairs
            for source_id in self.nodes:
                for target_id in self.nodes:
                    if source_id != target_id:
                        routes = self._calculate_quantum_routes(source_id, target_id)
                        self.routing_table[f"{source_id}_{target_id}"] = routes

        except Exception as e:
            logger.error(f"Error initializing routing protocols: {e}")

    def _calculate_quantum_routes(self, source: str, target: str) -> List[QuantumRoute]:
        """Calculate optimal quantum routes between nodes."""
        try:
            routes = []

            # Direct route if channel exists
            direct_channel = self._find_channel(source, target)
            if direct_channel:
                distance = direct_channel.metadata.get("distance", 0)
                if distance == 0:
                    # Calculate distance if not set
                    source_pos = self.nodes[source].location
                    target_pos = self.nodes[target].location
                    distance = self._calculate_distance(source_pos, target_pos)
                    direct_channel.metadata["distance"] = distance

                route = QuantumRoute(
                    route_id=f"direct_{source}_{target}",
                    source_node=source,
                    destination_node=target,
                    path_nodes=[source, target],
                    total_distance=distance,
                    estimated_fidelity=direct_channel.fidelity,
                    estimated_latency=1.0 / direct_channel.transmission_rate,
                    route_cost=1.0,
                    created_at=time.time()
                )
                routes.append(route)

            # Multi-hop routes (simplified implementation)
            for intermediate in self.nodes:
                if intermediate != source and intermediate != target:
                    channel1 = self._find_channel(source, intermediate)
                    channel2 = self._find_channel(intermediate, target)

                    if channel1 and channel2:
                        total_distance = (channel1.metadata.get("distance", 0) +
                                        channel2.metadata.get("distance", 0))
                        combined_fidelity = channel1.fidelity * channel2.fidelity
                        combined_latency = (1.0 / channel1.transmission_rate +
                                          1.0 / channel2.transmission_rate)

                        route = QuantumRoute(
                            route_id=f"hop_{source}_{intermediate}_{target}",
                            source_node=source,
                            destination_node=target,
                            path_nodes=[source, intermediate, target],
                            total_distance=total_distance,
                            estimated_fidelity=combined_fidelity,
                            estimated_latency=combined_latency,
                            route_cost=2.0,
                            created_at=time.time()
                        )
                        routes.append(route)

            # Sort routes by quality (fidelity/cost ratio)
            routes.sort(key=lambda r: r.estimated_fidelity / r.route_cost, reverse=True)

            return routes[:3]  # Return top 3 routes

        except Exception as e:
            logger.error(f"Error calculating quantum routes: {e}")
            return []

    def _find_channel(self, node1: str, node2: str) -> Optional[QuantumChannel]:
        """Find a channel between two nodes."""
        for channel in self.channels.values():
            if ((channel.source_node == node1 and channel.target_node == node2) or
                (channel.source_node == node2 and channel.target_node == node1)):
                return channel
        return None
    
    def _create_initial_nodes(self):
        """Create initial quantum network nodes."""
        try:
            # Create a small initial network
            initial_nodes = [
                ("central_hub", "Central Quantum Hub", (0.0, 0.0), 64),
                ("node_alpha", "Alpha Research Station", (10.0, 5.0), 32),
                ("node_beta", "Beta Computing Center", (-8.0, 12.0), 48),
                ("node_gamma", "Gamma Communications", (15.0, -7.0), 40),
                ("node_delta", "Delta Processing Unit", (-5.0, -10.0), 36)
            ]
            
            for node_id, name, location, capacity in initial_nodes:
                node = QuantumNode(
                    node_id=node_id,
                    node_name=name,
                    location=location,
                    quantum_capacity=capacity,
                    available_qubits=capacity,
                    entanglement_fidelity=0.95 + random.uniform(-0.05, 0.05),
                    processing_power=random.uniform(0.8, 1.0),
                    network_interfaces=["quantum_optical", "quantum_microwave"],
                    status="active",
                    created_at=time.time(),
                    metadata={"node_type": "standard", "generation": 1}
                )
                
                self.nodes[node_id] = node
                
        except Exception as e:
            logger.error(f"Error creating initial nodes: {e}")
    
    def _establish_initial_channels(self):
        """Establish initial quantum channels between nodes."""
        try:
            # Create channels in a hybrid topology
            channel_configs = [
                ("central_hub", "node_alpha", QuantumProtocol.BB84),
                ("central_hub", "node_beta", QuantumProtocol.E91),
                ("central_hub", "node_gamma", QuantumProtocol.TELEPORTATION),
                ("node_alpha", "node_beta", QuantumProtocol.SUPERDENSE),
                ("node_beta", "node_gamma", QuantumProtocol.BB84),
                ("node_gamma", "node_delta", QuantumProtocol.E91),
                ("node_delta", "central_hub", QuantumProtocol.DISTRIBUTED_COMPUTING)
            ]
            
            for source, target, protocol in channel_configs:
                if source in self.nodes and target in self.nodes:
                    channel = self._create_quantum_channel(source, target, protocol)
                    if channel:
                        self.channels[channel.channel_id] = channel
                        
        except Exception as e:
            logger.error(f"Error establishing initial channels: {e}")
    
    def _create_quantum_channel(self, source_node: str, target_node: str, 
                              protocol: QuantumProtocol) -> Optional[QuantumChannel]:
        """Create a quantum channel between two nodes."""
        try:
            if source_node not in self.nodes or target_node not in self.nodes:
                return None
            
            source = self.nodes[source_node]
            target = self.nodes[target_node]
            
            # Calculate channel parameters
            distance = self._calculate_distance(source.location, target.location)
            base_fidelity = min(source.entanglement_fidelity, target.entanglement_fidelity)
            
            # Apply distance-based fidelity degradation
            fidelity = base_fidelity * math.exp(-distance / self.quantum_repeater_distance)
            
            # Calculate transmission rate based on protocol
            transmission_rates = {
                QuantumProtocol.BB84: 1000,  # Hz
                QuantumProtocol.E91: 800,
                QuantumProtocol.TELEPORTATION: 500,
                QuantumProtocol.SUPERDENSE: 1200,
                QuantumProtocol.DISTRIBUTED_COMPUTING: 300
            }
            
            transmission_rate = transmission_rates.get(protocol, 500)
            error_rate = 1.0 - fidelity
            
            channel = QuantumChannel(
                channel_id=f"channel_{source_node}_{target_node}_{int(time.time() * 1000)}",
                source_node=source_node,
                target_node=target_node,
                protocol=protocol,
                state=QuantumChannelState.ESTABLISHING,
                entangled_pairs=[],
                fidelity=fidelity,
                transmission_rate=transmission_rate,
                error_rate=error_rate,
                established_at=time.time(),
                last_activity=time.time(),
                metadata={"distance": distance, "protocol_version": "1.0"}
            )
            
            return channel
            
        except Exception as e:
            logger.error(f"Error creating quantum channel: {e}")
            return None

    async def start_quantum_network(self) -> bool:
        """Start the quantum network operations."""
        try:
            if self.network_active:
                logger.warning("Quantum network already active")
                return False

            # Establish quantum channels
            await self._establish_quantum_entanglement()

            # Start network monitoring
            self.network_active = True
            self.network_thread = threading.Thread(
                target=self._network_monitoring_loop,
                daemon=True
            )
            self.network_thread.start()

            logger.info("🌐 Quantum network started successfully")
            return True

        except Exception as e:
            logger.error(f"Error starting quantum network: {e}")
            return False

    async def _establish_quantum_entanglement(self):
        """Establish quantum entanglement across all channels."""
        try:
            for channel in self.channels.values():
                if channel.state == QuantumChannelState.ESTABLISHING:
                    success = await self._create_entangled_pairs(channel)
                    if success:
                        channel.state = QuantumChannelState.ENTANGLED
                        logger.info(f"✅ Entanglement established: {channel.source_node} ↔ {channel.target_node}")
                    else:
                        channel.state = QuantumChannelState.ERROR
                        logger.warning(f"❌ Entanglement failed: {channel.source_node} ↔ {channel.target_node}")

        except Exception as e:
            logger.error(f"Error establishing quantum entanglement: {e}")

    async def _create_entangled_pairs(self, channel: QuantumChannel) -> bool:
        """Create entangled qubit pairs for a channel."""
        try:
            # Always simulate entanglement creation for reliability
            source_node = self.nodes[channel.source_node]
            target_node = self.nodes[channel.target_node]

            # Calculate number of pairs based on node capacity
            max_pairs_source = source_node.available_qubits // 2
            max_pairs_target = target_node.available_qubits // 2
            num_pairs = min(10, max_pairs_source, max_pairs_target)

            if num_pairs <= 0:
                return False

            # Create entangled pairs
            for i in range(num_pairs):
                qubit_a = f"entangled_{channel.channel_id}_a_{i}"
                qubit_b = f"entangled_{channel.channel_id}_b_{i}"
                channel.entangled_pairs.append((qubit_a, qubit_b))

            # Update node qubit availability
            source_node.available_qubits -= num_pairs
            target_node.available_qubits -= num_pairs

            # If quantum processor is available, enhance with real quantum operations
            if self.quantum_processor:
                try:
                    allocated_qubits = await self.quantum_processor._allocate_qubits(min(4, num_pairs * 2))

                    if len(allocated_qubits) >= 2:
                        # Apply entangling gates to first pair as demonstration
                        from .quantum_ai_processor import QuantumGate
                        await self.quantum_processor._apply_quantum_gate(
                            allocated_qubits[0], QuantumGate.HADAMARD
                        )
                        await self.quantum_processor._apply_quantum_gate(
                            allocated_qubits[0], QuantumGate.CNOT, allocated_qubits[1]
                        )

                        # Enhance fidelity with quantum processing
                        channel.fidelity = min(0.99, channel.fidelity + 0.05)

                except Exception as quantum_error:
                    logger.warning(f"Quantum enhancement failed, using simulation: {quantum_error}")

            logger.info(f"✅ Created {num_pairs} entangled pairs for {channel.source_node} ↔ {channel.target_node}")
            return num_pairs > 0

        except Exception as e:
            logger.error(f"Error creating entangled pairs: {e}")
            return False

    def _network_monitoring_loop(self):
        """Network monitoring and maintenance loop."""
        while self.network_active:
            try:
                # Process message queue
                asyncio.run(self._process_message_queue())

                # Monitor channel health
                asyncio.run(self._monitor_channel_health())

                # Update performance metrics
                self._update_performance_metrics()

                # Maintain entanglement
                asyncio.run(self._maintain_entanglement())

                # Restore qubit availability periodically
                asyncio.run(self._restore_qubit_availability())

                time.sleep(1.0)  # 1 second monitoring interval

            except Exception as e:
                logger.error(f"Error in network monitoring loop: {e}")
                time.sleep(1.0)

    async def _process_message_queue(self):
        """Process queued quantum messages."""
        try:
            messages_to_process = []

            # Collect messages to process
            while self.message_queue and len(messages_to_process) < 10:
                message = self.message_queue.popleft()
                if time.time() < message.expiry_time:
                    messages_to_process.append(message)

            # Process each message
            for message in messages_to_process:
                success = await self._transmit_quantum_message(message)
                if success:
                    self.performance_metrics["successful_transmissions"] += 1
                else:
                    self.performance_metrics["failed_transmissions"] += 1

        except Exception as e:
            logger.error(f"Error processing message queue: {e}")

    async def _transmit_quantum_message(self, message: QuantumMessage) -> bool:
        """Transmit a quantum message."""
        try:
            # Find optimal route
            route_key = f"{message.sender_node}_{message.receiver_node}"
            routes = self.routing_table.get(route_key, [])

            logger.info(f"🔍 Finding route for {message.sender_node} → {message.receiver_node}: {len(routes)} routes available")

            if not routes:
                logger.warning(f"No route found for {message.sender_node} → {message.receiver_node}")
                return False

            # Select best available route
            best_route = None
            for i, route in enumerate(routes):
                available = self._is_route_available(route)
                logger.info(f"  Route {i+1}: {route.route_id} - {'✅ Available' if available else '❌ Not available'}")
                if available and not best_route:
                    best_route = route

            if not best_route:
                logger.warning(f"No available route for {message.sender_node} → {message.receiver_node}")
                return False

            logger.info(f"🛣️ Selected route: {best_route.route_id} with fidelity {best_route.estimated_fidelity:.3f}")

            # Transmit using selected protocol
            success = await self._execute_quantum_protocol(message, best_route)

            if success:
                self.active_transmissions[message.message_id] = message
                logger.info(f"📡 Quantum message transmitted: {message.message_id}")

            return success

        except Exception as e:
            logger.error(f"Error transmitting quantum message: {e}")
            return False

    def _is_route_available(self, route: QuantumRoute) -> bool:
        """Check if a route is available for transmission."""
        try:
            for i in range(len(route.path_nodes) - 1):
                source = route.path_nodes[i]
                target = route.path_nodes[i + 1]
                channel = self._find_channel(source, target)

                if not channel:
                    return False

                # Accept channels that are establishing, entangled, or idle
                if channel.state in [QuantumChannelState.ERROR, QuantumChannelState.DISCONNECTED]:
                    return False

                # Very low fidelity threshold for better connectivity
                if channel.fidelity < 0.3:  # Very permissive threshold
                    return False

            return True

        except Exception as e:
            logger.error(f"Error checking route availability: {e}")
            return False

    async def _execute_quantum_protocol(self, message: QuantumMessage, route: QuantumRoute) -> bool:
        """Execute quantum communication protocol."""
        try:
            if message.protocol == QuantumProtocol.BB84:
                return await self._execute_bb84_protocol(message, route)
            elif message.protocol == QuantumProtocol.E91:
                return await self._execute_e91_protocol(message, route)
            elif message.protocol == QuantumProtocol.TELEPORTATION:
                return await self._execute_teleportation_protocol(message, route)
            elif message.protocol == QuantumProtocol.SUPERDENSE:
                return await self._execute_superdense_protocol(message, route)
            elif message.protocol == QuantumProtocol.DISTRIBUTED_COMPUTING:
                return await self._execute_distributed_computing_protocol(message, route)
            else:
                logger.warning(f"Unknown protocol: {message.protocol}")
                return False

        except Exception as e:
            logger.error(f"Error executing quantum protocol: {e}")
            return False

    async def _execute_bb84_protocol(self, message: QuantumMessage, route: QuantumRoute) -> bool:
        """Execute BB84 quantum key distribution protocol."""
        try:
            # Simplified BB84 implementation
            key_bits = []

            for i in range(self.qkd_key_length):
                # Alice chooses random bit and basis
                bit = random.randint(0, 1)
                basis = random.choice(['rectilinear', 'diagonal'])

                # Bob chooses random basis
                bob_basis = random.choice(['rectilinear', 'diagonal'])

                # Simulate transmission success based on fidelity
                if random.random() < route.estimated_fidelity:
                    # If bases match, bit is kept
                    if basis == bob_basis:
                        key_bits.append(bit)
                # Continue even if some transmissions fail

            # Generate shared key (require at least 32 bits for practical use)
            min_key_bits = max(32, self.qkd_key_length // 8)  # Much more reasonable requirement

            if len(key_bits) >= min_key_bits:
                shared_key = ''.join(map(str, key_bits[:min_key_bits]))
                message.encryption_key = hashlib.sha256(shared_key.encode()).hexdigest()
                logger.info(f"✅ BB84 protocol successful: {len(key_bits)} key bits generated")
                return True

            logger.warning(f"❌ BB84 protocol failed: only {len(key_bits)} key bits generated (need {min_key_bits})")
            return False

        except Exception as e:
            logger.error(f"Error in BB84 protocol: {e}")
            return False

    async def _execute_e91_protocol(self, message: QuantumMessage, route: QuantumRoute) -> bool:
        """Execute E91 entanglement-based QKD protocol."""
        try:
            # Find channel for the route
            channel = self._find_channel(route.path_nodes[0], route.path_nodes[1])
            if not channel or len(channel.entangled_pairs) == 0:
                return False

            # Use entangled pairs for key generation
            key_bits = []
            pairs_used = min(len(channel.entangled_pairs), self.qkd_key_length)

            for i in range(pairs_used):
                # Simulate Bell state measurements
                alice_measurement = random.randint(0, 1)
                bob_measurement = random.randint(0, 1)

                # In perfect entanglement, measurements are correlated
                if random.random() < channel.fidelity:
                    bob_measurement = alice_measurement  # Perfect correlation

                key_bits.append(alice_measurement)

            if len(key_bits) >= self.qkd_key_length // 2:
                shared_key = ''.join(map(str, key_bits[:self.qkd_key_length // 2]))
                message.encryption_key = hashlib.sha256(shared_key.encode()).hexdigest()
                return True

            return False

        except Exception as e:
            logger.error(f"Error in E91 protocol: {e}")
            return False

    async def _execute_teleportation_protocol(self, message: QuantumMessage, route: QuantumRoute) -> bool:
        """Execute quantum teleportation protocol."""
        try:
            # Find channel for teleportation
            channel = self._find_channel(route.path_nodes[0], route.path_nodes[1])
            if not channel or len(channel.entangled_pairs) == 0:
                return False

            # Simulate quantum teleportation
            success_probability = self.teleportation_success_rate * channel.fidelity

            for quantum_data in message.quantum_data:
                if random.random() < success_probability:
                    # Successful teleportation
                    quantum_data["teleported"] = True
                    quantum_data["fidelity"] = channel.fidelity
                else:
                    # Failed teleportation
                    quantum_data["teleported"] = False
                    return False

            return True

        except Exception as e:
            logger.error(f"Error in teleportation protocol: {e}")
            return False

    async def _execute_superdense_protocol(self, message: QuantumMessage, route: QuantumRoute) -> bool:
        """Execute superdense coding protocol."""
        try:
            # Find channel for superdense coding
            channel = self._find_channel(route.path_nodes[0], route.path_nodes[1])
            if not channel or len(channel.entangled_pairs) == 0:
                return False

            # Encode classical data using superdense coding
            classical_bits = message.classical_data.get("data", "")
            encoded_qubits = []

            # Each entangled pair can encode 2 classical bits
            pairs_needed = len(classical_bits) // 2

            if pairs_needed <= len(channel.entangled_pairs):
                for i in range(pairs_needed):
                    bit_pair = classical_bits[i*2:(i+1)*2]
                    encoded_qubits.append({
                        "pair_id": channel.entangled_pairs[i],
                        "encoded_bits": bit_pair,
                        "encoding_fidelity": channel.fidelity
                    })

                message.quantum_data = encoded_qubits
                return True

            return False

        except Exception as e:
            logger.error(f"Error in superdense protocol: {e}")
            return False

    async def _execute_distributed_computing_protocol(self, message: QuantumMessage, route: QuantumRoute) -> bool:
        """Execute distributed quantum computing protocol."""
        try:
            # Distribute quantum computation across network nodes
            computation_data = message.classical_data.get("computation", {})

            if self.quantum_processor:
                # Use quantum processor for distributed computation
                result = await self.quantum_processor.quantum_process(
                    input_data=computation_data,
                    algorithm="quantum_neural_network",
                    qubits_requested=8
                )

                if result.success:
                    message.classical_data["computation_result"] = {
                        "result": result.classical_result,
                        "quantum_advantage": result.quantum_advantage,
                        "confidence": result.confidence
                    }
                    return True

            # Fallback to classical simulation
            message.classical_data["computation_result"] = {
                "result": "distributed_computation_completed",
                "quantum_advantage": 1.0,
                "confidence": 0.8
            }
            return True

        except Exception as e:
            logger.error(f"Error in distributed computing protocol: {e}")
            return False

    async def _monitor_channel_health(self):
        """Monitor and maintain quantum channel health."""
        try:
            for channel in self.channels.values():
                if channel.state == QuantumChannelState.ENTANGLED:
                    # Check channel fidelity degradation
                    time_since_establishment = time.time() - channel.established_at
                    fidelity_decay = 0.01 * (time_since_establishment / 3600)  # 1% per hour

                    channel.fidelity = max(0.5, channel.fidelity - fidelity_decay)

                    # Regenerate entanglement if fidelity is too low
                    if channel.fidelity < self.default_fidelity_threshold:
                        await self._regenerate_entanglement(channel)

                    # Update last activity
                    channel.last_activity = time.time()

        except Exception as e:
            logger.error(f"Error monitoring channel health: {e}")

    async def _regenerate_entanglement(self, channel: QuantumChannel):
        """Regenerate entanglement for a degraded channel."""
        try:
            logger.info(f"🔄 Regenerating entanglement for {channel.source_node} ↔ {channel.target_node}")

            # Return qubits from old entangled pairs
            source_node = self.nodes[channel.source_node]
            target_node = self.nodes[channel.target_node]

            old_pairs_count = len(channel.entangled_pairs)
            source_node.available_qubits += old_pairs_count
            target_node.available_qubits += old_pairs_count

            # Clear old entangled pairs
            channel.entangled_pairs.clear()

            # Create new entangled pairs
            success = await self._create_entangled_pairs(channel)

            if success:
                # Reset fidelity
                base_fidelity = min(source_node.entanglement_fidelity, target_node.entanglement_fidelity)
                distance = channel.metadata.get("distance", 0)
                channel.fidelity = base_fidelity * math.exp(-distance / self.quantum_repeater_distance)
                channel.state = QuantumChannelState.ENTANGLED

                logger.info(f"✅ Entanglement regenerated with fidelity {channel.fidelity:.3f}")
            else:
                # Don't mark as error, just keep trying
                channel.state = QuantumChannelState.ESTABLISHING
                logger.warning(f"⚠️ Entanglement regeneration pending - insufficient qubits")

        except Exception as e:
            logger.error(f"Error regenerating entanglement: {e}")

    async def _maintain_entanglement(self):
        """Maintain entanglement across the network."""
        try:
            # Generate new entangled pairs for active channels
            for channel in self.channels.values():
                if (channel.state == QuantumChannelState.ENTANGLED and
                    len(channel.entangled_pairs) < 5):  # Maintain minimum pairs

                    additional_pairs = await self._create_entangled_pairs(channel)
                    if additional_pairs:
                        self.performance_metrics["entanglement_efficiency"] += 0.01

        except Exception as e:
            logger.error(f"Error maintaining entanglement: {e}")

    async def _restore_qubit_availability(self):
        """Restore qubit availability for nodes that are running low."""
        try:
            for node in self.nodes.values():
                # Restore qubits if availability is very low
                if node.available_qubits < node.quantum_capacity * 0.1:  # Less than 10%
                    restored_qubits = min(5, node.quantum_capacity - node.available_qubits)
                    node.available_qubits += restored_qubits

                    if restored_qubits > 0:
                        logger.info(f"🔋 Restored {restored_qubits} qubits for {node.node_id}")

        except Exception as e:
            logger.error(f"Error restoring qubit availability: {e}")

    def _update_performance_metrics(self):
        """Update network performance metrics."""
        try:
            total_transmissions = (self.performance_metrics["successful_transmissions"] +
                                 self.performance_metrics["failed_transmissions"])

            if total_transmissions > 0:
                success_rate = self.performance_metrics["successful_transmissions"] / total_transmissions
                self.performance_metrics["network_throughput"] = success_rate * 100  # Simplified metric

            # Calculate average fidelity
            if self.channels:
                total_fidelity = sum(channel.fidelity for channel in self.channels.values())
                self.performance_metrics["average_fidelity"] = total_fidelity / len(self.channels)

        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")

    async def send_quantum_message(self, sender_node: str, receiver_node: str,
                                 protocol: QuantumProtocol, quantum_data: List[Dict[str, Any]] = None,
                                 classical_data: Dict[str, Any] = None, priority: int = 1) -> str:
        """Send a quantum message through the network."""
        try:
            if sender_node not in self.nodes or receiver_node not in self.nodes:
                raise ValueError("Invalid sender or receiver node")

            message_id = str(uuid.uuid4())

            message = QuantumMessage(
                message_id=message_id,
                sender_node=sender_node,
                receiver_node=receiver_node,
                protocol=protocol,
                quantum_data=quantum_data or [],
                classical_data=classical_data or {},
                priority=priority,
                encryption_key=None,
                timestamp=time.time(),
                expiry_time=time.time() + 300,  # 5 minute expiry
                metadata={"created_by": "quantum_network"}
            )

            # Add to message queue
            self.message_queue.append(message)
            self.performance_metrics["total_messages_sent"] += 1

            logger.info(f"📨 Quantum message queued: {message_id}")
            return message_id

        except Exception as e:
            logger.error(f"Error sending quantum message: {e}")
            return ""

    async def add_quantum_node(self, node_name: str, location: Tuple[float, float],
                             quantum_capacity: int) -> str:
        """Add a new quantum node to the network."""
        try:
            node_id = f"node_{int(time.time() * 1000)}"

            node = QuantumNode(
                node_id=node_id,
                node_name=node_name,
                location=location,
                quantum_capacity=quantum_capacity,
                available_qubits=quantum_capacity,
                entanglement_fidelity=0.90 + random.uniform(-0.05, 0.05),
                processing_power=random.uniform(0.7, 1.0),
                network_interfaces=["quantum_optical"],
                status="active",
                created_at=time.time(),
                metadata={"node_type": "dynamic", "generation": 2}
            )

            self.nodes[node_id] = node

            # Establish channels to nearby nodes
            await self._establish_node_connections(node_id)

            # Update routing table
            self._update_routing_table_for_new_node(node_id)

            logger.info(f"➕ New quantum node added: {node_name} ({node_id})")
            return node_id

        except Exception as e:
            logger.error(f"Error adding quantum node: {e}")
            return ""

    async def _establish_node_connections(self, node_id: str):
        """Establish connections for a new node."""
        try:
            new_node = self.nodes[node_id]
            connections_made = 0

            # Connect to nearest nodes
            node_distances = []
            for other_id, other_node in self.nodes.items():
                if other_id != node_id:
                    distance = self._calculate_distance(new_node.location, other_node.location)
                    node_distances.append((distance, other_id))

            # Sort by distance and connect to closest nodes
            node_distances.sort()
            max_connections = min(3, len(node_distances))  # Connect to up to 3 nearest nodes

            for i in range(max_connections):
                _, target_node_id = node_distances[i]

                # Create channel with appropriate protocol
                protocols = [QuantumProtocol.BB84, QuantumProtocol.E91, QuantumProtocol.TELEPORTATION]
                protocol = random.choice(protocols)

                channel = self._create_quantum_channel(node_id, target_node_id, protocol)
                if channel:
                    self.channels[channel.channel_id] = channel
                    connections_made += 1

            logger.info(f"🔗 Established {connections_made} connections for {node_id}")

        except Exception as e:
            logger.error(f"Error establishing node connections: {e}")

    def _update_routing_table_for_new_node(self, node_id: str):
        """Update routing table when a new node is added."""
        try:
            # Calculate routes from new node to all existing nodes
            for target_id in self.nodes:
                if target_id != node_id:
                    routes = self._calculate_quantum_routes(node_id, target_id)
                    self.routing_table[f"{node_id}_{target_id}"] = routes

                    # Calculate routes from existing nodes to new node
                    routes = self._calculate_quantum_routes(target_id, node_id)
                    self.routing_table[f"{target_id}_{node_id}"] = routes

        except Exception as e:
            logger.error(f"Error updating routing table: {e}")

    async def get_network_status(self) -> Dict[str, Any]:
        """Get comprehensive network status."""
        try:
            # Calculate network statistics
            active_nodes = sum(1 for node in self.nodes.values() if node.status == "active")
            active_channels = sum(1 for channel in self.channels.values()
                                if channel.state == QuantumChannelState.ENTANGLED)

            total_entangled_pairs = sum(len(channel.entangled_pairs) for channel in self.channels.values())

            # Network topology analysis
            connectivity = len(self.channels) / max(len(self.nodes) * (len(self.nodes) - 1) / 2, 1)

            return {
                "network_active": self.network_active,
                "network_topology": self.network_topology.value,
                "total_nodes": len(self.nodes),
                "active_nodes": active_nodes,
                "total_channels": len(self.channels),
                "active_channels": active_channels,
                "total_entangled_pairs": total_entangled_pairs,
                "network_connectivity": connectivity,
                "performance_metrics": self.performance_metrics.copy(),
                "message_queue_size": len(self.message_queue),
                "active_transmissions": len(self.active_transmissions),
                "routing_table_size": len(self.routing_table),
                "quantum_integration": self.quantum_processor is not None,
                "average_node_capacity": sum(node.quantum_capacity for node in self.nodes.values()) / len(self.nodes) if self.nodes else 0,
                "network_diameter": self._calculate_network_diameter()
            }

        except Exception as e:
            logger.error(f"Error getting network status: {e}")
            return {}

    def _calculate_network_diameter(self) -> float:
        """Calculate the network diameter (maximum shortest path)."""
        try:
            max_distance = 0.0

            for route_list in self.routing_table.values():
                if route_list:
                    shortest_route = min(route_list, key=lambda r: r.total_distance)
                    max_distance = max(max_distance, shortest_route.total_distance)

            return max_distance

        except Exception as e:
            logger.error(f"Error calculating network diameter: {e}")
            return 0.0

    async def get_node_info(self, node_id: str) -> Dict[str, Any]:
        """Get detailed information about a specific node."""
        try:
            if node_id not in self.nodes:
                return {}

            node = self.nodes[node_id]

            # Find connected channels
            connected_channels = []
            for channel in self.channels.values():
                if channel.source_node == node_id or channel.target_node == node_id:
                    connected_channels.append({
                        "channel_id": channel.channel_id,
                        "peer_node": channel.target_node if channel.source_node == node_id else channel.source_node,
                        "protocol": channel.protocol.value,
                        "state": channel.state.value,
                        "fidelity": channel.fidelity,
                        "entangled_pairs": len(channel.entangled_pairs)
                    })

            return {
                "node_info": asdict(node),
                "connected_channels": connected_channels,
                "connectivity_degree": len(connected_channels),
                "total_entangled_pairs": sum(len(channel.entangled_pairs) for channel in self.channels.values()
                                           if channel.source_node == node_id or channel.target_node == node_id)
            }

        except Exception as e:
            logger.error(f"Error getting node info: {e}")
            return {}

    async def shutdown_quantum_network(self) -> Dict[str, Any]:
        """Gracefully shutdown the quantum network."""
        try:
            # Stop network operations
            self.network_active = False

            if self.network_thread and self.network_thread.is_alive():
                self.network_thread.join(timeout=10.0)

            # Generate shutdown summary
            shutdown_summary = {
                "total_nodes": len(self.nodes),
                "total_channels": len(self.channels),
                "total_messages_processed": self.performance_metrics["total_messages_sent"],
                "successful_transmissions": self.performance_metrics["successful_transmissions"],
                "final_network_fidelity": self.performance_metrics["average_fidelity"],
                "final_throughput": self.performance_metrics["network_throughput"],
                "entanglement_efficiency": self.performance_metrics["entanglement_efficiency"],
                "shutdown_timestamp": time.time()
            }

            logger.info("🌐 Quantum network gracefully shutdown")
            return shutdown_summary

        except Exception as e:
            logger.error(f"Error shutting down quantum network: {e}")
            return {"error": str(e)}


# Factory functions
def create_quantum_node(node_name: str, location: Tuple[float, float], capacity: int) -> QuantumNode:
    """Create a new quantum node."""
    return QuantumNode(
        node_id=f"node_{int(time.time() * 1000)}",
        node_name=node_name,
        location=location,
        quantum_capacity=capacity,
        available_qubits=capacity,
        entanglement_fidelity=0.95,
        processing_power=1.0,
        network_interfaces=["quantum_optical"],
        status="active",
        created_at=time.time(),
        metadata={}
    )

def create_quantum_message(sender: str, receiver: str, protocol: QuantumProtocol) -> QuantumMessage:
    """Create a new quantum message."""
    return QuantumMessage(
        message_id=str(uuid.uuid4()),
        sender_node=sender,
        receiver_node=receiver,
        protocol=protocol,
        quantum_data=[],
        classical_data={},
        priority=1,
        encryption_key=None,
        timestamp=time.time(),
        expiry_time=time.time() + 300,
        metadata={}
    )
    

