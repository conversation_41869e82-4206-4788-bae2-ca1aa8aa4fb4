"""
System Tray Interface for Voice AI Controller

Provides a system tray icon with basic controls and status information.
"""

import tkinter as tk
from tkinter import messagebox
import threading
import time
from typing import Optional, Callable
from pathlib import Path
import sys

try:
    import pystray
    from pystray import MenuItem as item
    from PIL import Image, ImageDraw
    PYSTRAY_AVAILABLE = True
except ImportError:
    PYSTRAY_AVAILABLE = False
    # Create dummy classes for when PIL is not available
    class Image:
        class Image:
            pass
    class ImageDraw:
        @staticmethod
        def Draw(image):
            return None

from loguru import logger
from ..utils.config_manager import ConfigManager


class SystemTrayInterface:
    """
    System tray interface for the Voice AI Controller.
    
    Features:
    - System tray icon with status indication
    - Context menu with basic controls
    - Status notifications
    - Settings access
    - Quick start/stop functionality
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        self.icon: Optional[pystray.Icon] = None
        self.is_running = False
        
        # Callbacks
        self.on_start_requested: Optional[Callable] = None
        self.on_stop_requested: Optional[Callable] = None
        self.on_settings_requested: Optional[Callable] = None
        self.on_exit_requested: Optional[Callable] = None
        
        # Status
        self.voice_listening = False
        self.last_command = "None"
        self.commands_processed = 0
        
        if not PYSTRAY_AVAILABLE:
            logger.warning("pystray not available, system tray disabled")
        
        logger.info("System Tray Interface initialized")
    
    def create_icon_image(self, color: str = "green") -> Image.Image:
        """Create system tray icon image."""

        if not PYSTRAY_AVAILABLE:
            return None

        # Create a simple icon
        width = 64
        height = 64
        image = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(image)
        
        # Draw microphone icon
        if color == "green":  # Active/listening
            fill_color = (0, 255, 0, 255)
        elif color == "red":  # Error/stopped
            fill_color = (255, 0, 0, 255)
        elif color == "yellow":  # Processing
            fill_color = (255, 255, 0, 255)
        else:  # Default/idle
            fill_color = (128, 128, 128, 255)
        
        # Draw microphone shape
        # Body
        draw.ellipse([20, 15, 44, 35], fill=fill_color)
        # Stand
        draw.rectangle([30, 35, 34, 50], fill=fill_color)
        # Base
        draw.rectangle([25, 50, 39, 55], fill=fill_color)
        
        return image
    
    def start(self):
        """Start the system tray interface."""
        
        if not PYSTRAY_AVAILABLE:
            logger.warning("Cannot start system tray - pystray not available")
            return
        
        if self.is_running:
            return
        
        try:
            # Create menu
            menu = pystray.Menu(
                item('Voice AI Controller', self._show_status, default=True),
                pystray.Menu.SEPARATOR,
                item('Start Listening', self._start_listening, enabled=lambda item: not self.voice_listening),
                item('Stop Listening', self._stop_listening, enabled=lambda item: self.voice_listening),
                pystray.Menu.SEPARATOR,
                item('Settings', self._show_settings),
                item('Test Microphone', self._test_microphone),
                item('View Logs', self._view_logs),
                pystray.Menu.SEPARATOR,
                item('About', self._show_about),
                item('Exit', self._exit_application)
            )
            
            # Create icon
            self.icon = pystray.Icon(
                "voice_ai_controller",
                self.create_icon_image("gray"),
                "Voice AI Controller",
                menu
            )
            
            # Start in separate thread
            self.is_running = True
            threading.Thread(target=self._run_icon, daemon=True).start()
            
            logger.info("System tray started")
            
        except Exception as e:
            logger.error(f"Failed to start system tray: {e}")
    
    def stop(self):
        """Stop the system tray interface."""
        
        if not self.is_running:
            return
        
        self.is_running = False
        
        if self.icon:
            self.icon.stop()
        
        logger.info("System tray stopped")
    
    def _run_icon(self):
        """Run the system tray icon."""
        try:
            self.icon.run()
        except Exception as e:
            logger.error(f"System tray error: {e}")
    
    def update_status(self, listening: bool = None, last_command: str = None, commands_count: int = None):
        """Update system tray status."""
        
        if listening is not None:
            self.voice_listening = listening
        
        if last_command is not None:
            self.last_command = last_command
        
        if commands_count is not None:
            self.commands_processed = commands_count
        
        # Update icon color based on status
        if self.icon:
            if self.voice_listening:
                self.icon.icon = self.create_icon_image("green")
            else:
                self.icon.icon = self.create_icon_image("gray")
    
    def show_notification(self, title: str, message: str, timeout: int = 3):
        """Show system notification."""
        
        if self.icon:
            try:
                self.icon.notify(message, title)
            except Exception as e:
                logger.error(f"Failed to show notification: {e}")
    
    def _show_status(self, icon, item):
        """Show status window."""
        
        status_text = f"""Voice AI Controller Status

Listening: {'Yes' if self.voice_listening else 'No'}
Last Command: {self.last_command}
Commands Processed: {self.commands_processed}

Configuration:
- Wake Word: {self.config.get('voice.wake_word.word', 'computer')}
- Language: {self.config.get('voice.recognition.language', 'en-US')}
- Safe Mode: {self.config.get('app.safe_mode', True)}
"""
        
        messagebox.showinfo("Voice AI Controller Status", status_text)
    
    def _start_listening(self, icon, item):
        """Start voice listening."""
        if self.on_start_requested:
            self.on_start_requested()
        self.show_notification("Voice AI", "Voice recognition started")
    
    def _stop_listening(self, icon, item):
        """Stop voice listening."""
        if self.on_stop_requested:
            self.on_stop_requested()
        self.show_notification("Voice AI", "Voice recognition stopped")
    
    def _show_settings(self, icon, item):
        """Show settings window."""
        if self.on_settings_requested:
            self.on_settings_requested()
        else:
            messagebox.showinfo("Settings", "Settings window not implemented yet.\nEdit config/settings.yaml manually.")
    
    def _test_microphone(self, icon, item):
        """Test microphone functionality."""
        
        def test_mic():
            try:
                import speech_recognition as sr
                r = sr.Recognizer()
                with sr.Microphone() as source:
                    r.adjust_for_ambient_noise(source, duration=1)
                messagebox.showinfo("Microphone Test", "Microphone is working correctly!")
            except Exception as e:
                messagebox.showerror("Microphone Test", f"Microphone test failed:\n{str(e)}")
        
        threading.Thread(target=test_mic, daemon=True).start()
    
    def _view_logs(self, icon, item):
        """Open log file."""
        
        log_file = Path("logs/voice_ai.log")
        if log_file.exists():
            try:
                import os
                os.startfile(str(log_file))
            except Exception as e:
                messagebox.showerror("View Logs", f"Failed to open log file:\n{str(e)}")
        else:
            messagebox.showwarning("View Logs", "Log file not found")
    
    def _show_about(self, icon, item):
        """Show about dialog."""
        
        about_text = """Ultimate Voice-Controlled AI Computer System
Version: 1.0.0-alpha
Phase: 1 - Foundation

A comprehensive voice-controlled AI system that enables 
natural language interaction with your computer.

Features:
• Advanced voice recognition
• Natural language processing
• File system control
• Application management
• Safety mechanisms

Visit: https://github.com/voiceai/voice-ai-controller
"""
        
        messagebox.showinfo("About Voice AI Controller", about_text)
    
    def _exit_application(self, icon, item):
        """Exit the application."""
        
        result = messagebox.askyesno(
            "Exit Voice AI Controller", 
            "Are you sure you want to exit Voice AI Controller?"
        )
        
        if result:
            if self.on_exit_requested:
                self.on_exit_requested()
            self.stop()


class SimpleStatusWindow:
    """
    Simple status window as fallback when system tray is not available.
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        self.root = None
        self.status_label = None
        self.is_running = False
        
        # Callbacks
        self.on_start_requested: Optional[Callable] = None
        self.on_stop_requested: Optional[Callable] = None
        self.on_exit_requested: Optional[Callable] = None
        
        # Status
        self.voice_listening = False
        self.last_command = "None"
        self.commands_processed = 0
    
    def start(self):
        """Start the status window."""
        
        if self.is_running:
            return
        
        self.is_running = True
        
        # Create window in separate thread
        threading.Thread(target=self._create_window, daemon=True).start()
        
        logger.info("Status window started")
    
    def stop(self):
        """Stop the status window."""
        
        if not self.is_running:
            return
        
        self.is_running = False
        
        if self.root:
            self.root.quit()
        
        logger.info("Status window stopped")
    
    def _create_window(self):
        """Create the status window."""
        
        self.root = tk.Tk()
        self.root.title("Voice AI Controller")
        self.root.geometry("400x300")
        self.root.resizable(False, False)
        
        # Status frame
        status_frame = tk.Frame(self.root)
        status_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_label = tk.Label(status_frame, text="Voice AI Controller", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 10))
        
        # Status
        self.status_label = tk.Label(status_frame, text="Status: Initializing...", font=("Arial", 10))
        self.status_label.pack(pady=5)
        
        # Buttons
        button_frame = tk.Frame(status_frame)
        button_frame.pack(pady=10)
        
        start_btn = tk.Button(button_frame, text="Start Listening", command=self._start_listening)
        start_btn.pack(side=tk.LEFT, padx=5)
        
        stop_btn = tk.Button(button_frame, text="Stop Listening", command=self._stop_listening)
        stop_btn.pack(side=tk.LEFT, padx=5)
        
        exit_btn = tk.Button(button_frame, text="Exit", command=self._exit_application)
        exit_btn.pack(side=tk.LEFT, padx=5)
        
        # Update status periodically
        self._update_display()
        
        self.root.mainloop()
    
    def _update_display(self):
        """Update the status display."""
        
        if not self.is_running or not self.root:
            return
        
        status_text = f"""Status: {'Listening' if self.voice_listening else 'Idle'}
Last Command: {self.last_command}
Commands Processed: {self.commands_processed}
Wake Word: {self.config.get('voice.wake_word.word', 'computer')}"""
        
        if self.status_label:
            self.status_label.config(text=status_text)
        
        # Schedule next update
        self.root.after(1000, self._update_display)
    
    def update_status(self, listening: bool = None, last_command: str = None, commands_count: int = None):
        """Update status information."""
        
        if listening is not None:
            self.voice_listening = listening
        
        if last_command is not None:
            self.last_command = last_command
        
        if commands_count is not None:
            self.commands_processed = commands_count
    
    def _start_listening(self):
        """Start voice listening."""
        if self.on_start_requested:
            self.on_start_requested()
    
    def _stop_listening(self):
        """Stop voice listening."""
        if self.on_stop_requested:
            self.on_stop_requested()
    
    def _exit_application(self):
        """Exit the application."""
        
        result = messagebox.askyesno(
            "Exit Voice AI Controller", 
            "Are you sure you want to exit Voice AI Controller?"
        )
        
        if result:
            if self.on_exit_requested:
                self.on_exit_requested()
            self.stop()


def create_ui_interface(config_manager: ConfigManager):
    """
    Create appropriate UI interface based on available libraries.
    
    Returns:
        UI interface instance (SystemTrayInterface or SimpleStatusWindow)
    """
    
    if PYSTRAY_AVAILABLE and config_manager.get("ui.system_tray", True):
        return SystemTrayInterface(config_manager)
    else:
        return SimpleStatusWindow(config_manager)
