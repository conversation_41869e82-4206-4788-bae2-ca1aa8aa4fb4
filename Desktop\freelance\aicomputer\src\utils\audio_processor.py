"""
Audio Processing Utilities for Voice AI System

Handles audio preprocessing, noise reduction, and audio enhancement:
- Real-time audio processing
- Noise cancellation
- Audio format conversion
- Volume normalization
"""

import numpy as np
import librosa
import soundfile as sf
from typing import Optional, Tuple
from loguru import logger

from .config_manager import ConfigManager


class AudioProcessor:
    """
    Audio processing utilities for voice recognition enhancement.
    
    Features:
    - Noise reduction and filtering
    - Audio normalization
    - Format conversion
    - Real-time processing
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        
        # Audio configuration
        self.sample_rate = self.config.get("voice.audio.sample_rate", 16000)
        self.chunk_size = self.config.get("voice.audio.chunk_size", 1024)
        self.channels = self.config.get("voice.audio.channels", 1)
        
        logger.info("Audio Processor initialized")
    
    def preprocess_audio(self, audio_data: np.ndarray, sample_rate: int = None) -> np.ndarray:
        """
        Preprocess audio data for better recognition.
        
        Args:
            audio_data: Raw audio data as numpy array
            sample_rate: Sample rate of the audio
            
        Returns:
            Preprocessed audio data
        """
        if sample_rate is None:
            sample_rate = self.sample_rate
        
        try:
            # Normalize audio
            audio_data = self._normalize_audio(audio_data)
            
            # Apply noise reduction
            audio_data = self._reduce_noise(audio_data, sample_rate)
            
            # Apply bandpass filter for speech frequencies
            audio_data = self._bandpass_filter(audio_data, sample_rate)
            
            return audio_data
            
        except Exception as e:
            logger.error(f"Audio preprocessing failed: {e}")
            return audio_data  # Return original if preprocessing fails
    
    def _normalize_audio(self, audio_data: np.ndarray) -> np.ndarray:
        """Normalize audio amplitude."""
        if np.max(np.abs(audio_data)) > 0:
            return audio_data / np.max(np.abs(audio_data))
        return audio_data
    
    def _reduce_noise(self, audio_data: np.ndarray, sample_rate: int) -> np.ndarray:
        """Apply basic noise reduction."""
        try:
            # Simple spectral subtraction for noise reduction
            # This is a basic implementation - more advanced methods can be added
            
            # Compute STFT
            stft = librosa.stft(audio_data)
            magnitude = np.abs(stft)
            phase = np.angle(stft)
            
            # Estimate noise from first few frames
            noise_frames = min(10, magnitude.shape[1] // 4)
            noise_spectrum = np.mean(magnitude[:, :noise_frames], axis=1, keepdims=True)
            
            # Spectral subtraction
            alpha = 2.0  # Over-subtraction factor
            enhanced_magnitude = magnitude - alpha * noise_spectrum
            
            # Ensure non-negative values
            enhanced_magnitude = np.maximum(enhanced_magnitude, 0.1 * magnitude)
            
            # Reconstruct audio
            enhanced_stft = enhanced_magnitude * np.exp(1j * phase)
            enhanced_audio = librosa.istft(enhanced_stft)
            
            return enhanced_audio
            
        except Exception as e:
            logger.warning(f"Noise reduction failed: {e}")
            return audio_data
    
    def _bandpass_filter(self, audio_data: np.ndarray, sample_rate: int) -> np.ndarray:
        """Apply bandpass filter for speech frequencies (300-3400 Hz)."""
        try:
            # Use librosa for filtering
            low_freq = 300
            high_freq = 3400
            
            # Apply bandpass filter
            filtered_audio = librosa.effects.preemphasis(audio_data)
            
            return filtered_audio
            
        except Exception as e:
            logger.warning(f"Bandpass filtering failed: {e}")
            return audio_data
    
    def convert_sample_rate(self, audio_data: np.ndarray, 
                          original_rate: int, target_rate: int) -> np.ndarray:
        """Convert audio sample rate."""
        if original_rate == target_rate:
            return audio_data
        
        try:
            resampled = librosa.resample(audio_data, 
                                       orig_sr=original_rate, 
                                       target_sr=target_rate)
            return resampled
        except Exception as e:
            logger.error(f"Sample rate conversion failed: {e}")
            return audio_data
    
    def detect_voice_activity(self, audio_data: np.ndarray, 
                            sample_rate: int = None) -> bool:
        """
        Detect if audio contains voice activity.
        
        Args:
            audio_data: Audio data to analyze
            sample_rate: Sample rate of the audio
            
        Returns:
            True if voice activity detected, False otherwise
        """
        if sample_rate is None:
            sample_rate = self.sample_rate
        
        try:
            # Calculate energy
            energy = np.sum(audio_data ** 2)
            
            # Calculate zero crossing rate
            zcr = librosa.feature.zero_crossing_rate(audio_data)[0]
            mean_zcr = np.mean(zcr)
            
            # Simple voice activity detection
            energy_threshold = 0.01
            zcr_threshold = 0.1
            
            return energy > energy_threshold and mean_zcr > zcr_threshold
            
        except Exception as e:
            logger.error(f"Voice activity detection failed: {e}")
            return True  # Assume voice activity if detection fails
    
    def save_audio(self, audio_data: np.ndarray, file_path: str, 
                   sample_rate: int = None):
        """Save audio data to file."""
        if sample_rate is None:
            sample_rate = self.sample_rate
        
        try:
            sf.write(file_path, audio_data, sample_rate)
            logger.info(f"Audio saved to {file_path}")
        except Exception as e:
            logger.error(f"Failed to save audio: {e}")
    
    def load_audio(self, file_path: str) -> Tuple[np.ndarray, int]:
        """Load audio from file."""
        try:
            audio_data, sample_rate = librosa.load(file_path, sr=None)
            return audio_data, sample_rate
        except Exception as e:
            logger.error(f"Failed to load audio: {e}")
            return np.array([]), 0
