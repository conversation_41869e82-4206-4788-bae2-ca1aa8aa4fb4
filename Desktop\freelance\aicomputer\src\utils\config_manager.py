"""
Configuration Manager for Voice AI System

Handles loading and managing configuration from multiple sources:
- YAML configuration files
- Environment variables
- Command line arguments
- Runtime settings
"""

import os
import yaml
from typing import Any, Dict, Optional
from pathlib import Path
from dotenv import load_dotenv
from loguru import logger


class ConfigManager:
    """
    Centralized configuration management system.
    
    Features:
    - YAML configuration file loading
    - Environment variable override
    - Nested configuration access with dot notation
    - Configuration validation
    - Runtime configuration updates
    """
    
    def __init__(self, config_file: Optional[str] = None, env_file: Optional[str] = None):
        self.config_data: Dict[str, Any] = {}
        self.config_file = config_file or "config/settings.yaml"
        self.env_file = env_file or ".env"
        
        # Load configuration
        self._load_environment()
        self._load_config_file()
        self._apply_environment_overrides()
        
        logger.info("Configuration Manager initialized")
    
    def _load_environment(self):
        """Load environment variables from .env file."""
        env_path = Path(self.env_file)
        if env_path.exists():
            load_dotenv(env_path)
            logger.info(f"Environment variables loaded from {env_path}")
        else:
            logger.warning(f"Environment file not found: {env_path}")
    
    def _load_config_file(self):
        """Load configuration from YAML file."""
        config_path = Path(self.config_file)
        
        if not config_path.exists():
            logger.warning(f"Configuration file not found: {config_path}")
            self._create_default_config()
            return
        
        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                self.config_data = yaml.safe_load(file) or {}
            logger.info(f"Configuration loaded from {config_path}")
            
        except yaml.YAMLError as e:
            logger.error(f"Error parsing YAML configuration: {e}")
            self.config_data = {}
        except Exception as e:
            logger.error(f"Error loading configuration file: {e}")
            self.config_data = {}
    
    def _create_default_config(self):
        """Create default configuration if file doesn't exist."""
        self.config_data = {
            "app": {
                "name": "Voice AI Controller",
                "version": "1.0.0-alpha",
                "debug": False
            },
            "voice": {
                "recognition": {
                    "engine": "whisper",
                    "language": "en-US",
                    "timeout": 5
                },
                "wake_word": {
                    "enabled": True,
                    "word": "computer",
                    "sensitivity": 0.7
                }
            },
            "ai": {
                "primary_model": "gpt-4-turbo-preview",
                "max_tokens": 4096,
                "temperature": 0.3
            }
        }
        logger.info("Default configuration created")
    
    def _apply_environment_overrides(self):
        """Apply environment variable overrides to configuration."""
        
        # Map environment variables to config paths
        env_mappings = {
            "OPENAI_API_KEY": "openai.api_key",
            "OPENAI_MODEL": "ai.primary_model",
            "VOICE_RECOGNITION_LANGUAGE": "voice.recognition.language",
            "WAKE_WORD": "voice.wake_word.word",
            "LOG_LEVEL": "logging.level",
            "DEBUG_MODE": "app.debug"
        }
        
        for env_var, config_path in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                self.set(config_path, env_value)
                logger.debug(f"Environment override: {config_path} = {env_value}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value using dot notation.
        
        Args:
            key: Configuration key in dot notation (e.g., 'voice.recognition.engine')
            default: Default value if key not found
            
        Returns:
            Configuration value or default
        """
        keys = key.split('.')
        value = self.config_data
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """
        Set configuration value using dot notation.
        
        Args:
            key: Configuration key in dot notation
            value: Value to set
        """
        keys = key.split('.')
        config = self.config_data
        
        # Navigate to the parent dictionary
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # Set the value
        config[keys[-1]] = value
    
    def has(self, key: str) -> bool:
        """
        Check if configuration key exists.
        
        Args:
            key: Configuration key in dot notation
            
        Returns:
            True if key exists, False otherwise
        """
        return self.get(key) is not None
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        Get entire configuration section.
        
        Args:
            section: Section name
            
        Returns:
            Dictionary containing section configuration
        """
        return self.get(section, {})
    
    def update_section(self, section: str, updates: Dict[str, Any]):
        """
        Update configuration section with new values.
        
        Args:
            section: Section name
            updates: Dictionary of updates to apply
        """
        current = self.get_section(section)
        current.update(updates)
        self.set(section, current)
    
    def save_config(self, file_path: Optional[str] = None):
        """
        Save current configuration to file.
        
        Args:
            file_path: Optional path to save to (defaults to current config file)
        """
        save_path = Path(file_path or self.config_file)
        
        try:
            # Ensure directory exists
            save_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(save_path, 'w', encoding='utf-8') as file:
                yaml.dump(self.config_data, file, default_flow_style=False, indent=2)
            
            logger.info(f"Configuration saved to {save_path}")
            
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
    
    def validate_config(self) -> bool:
        """
        Validate configuration for required settings.
        
        Returns:
            True if configuration is valid, False otherwise
        """
        required_keys = [
            "app.name",
            "voice.recognition.engine",
            "ai.primary_model"
        ]
        
        missing_keys = []
        for key in required_keys:
            if not self.has(key):
                missing_keys.append(key)
        
        if missing_keys:
            logger.error(f"Missing required configuration keys: {missing_keys}")
            return False
        
        # Validate OpenAI API key if using OpenAI models
        if "gpt" in self.get("ai.primary_model", "").lower():
            if not self.get("openai.api_key"):
                logger.error("OpenAI API key required for GPT models")
                return False
        
        logger.info("Configuration validation passed")
        return True
    
    def get_all(self) -> Dict[str, Any]:
        """
        Get all configuration data.
        
        Returns:
            Complete configuration dictionary
        """
        return self.config_data.copy()
    
    def reload(self):
        """Reload configuration from file and environment."""
        self.config_data = {}
        self._load_environment()
        self._load_config_file()
        self._apply_environment_overrides()
        logger.info("Configuration reloaded")
    
    def __str__(self) -> str:
        """String representation of configuration."""
        return f"ConfigManager(file={self.config_file}, sections={list(self.config_data.keys())})"
