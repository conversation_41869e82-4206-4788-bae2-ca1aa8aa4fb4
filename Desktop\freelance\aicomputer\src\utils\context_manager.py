"""
Context Manager for Voice AI System

Manages conversation context, memory, and session state:
- Conversation history tracking
- Context-aware command interpretation
- Session management
- Memory persistence
"""

import json
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from pathlib import Path
from loguru import logger

from .config_manager import Config<PERSON>anager


@dataclass
class ContextEntry:
    """Single context entry in conversation history."""
    timestamp: float
    type: str  # 'user_input', 'ai_response', 'system_event'
    content: Any
    session_id: str
    metadata: Dict[str, Any] = None


class ContextManager:
    """
    Manages conversation context and memory for the voice AI system.
    
    Features:
    - Conversation history tracking
    - Session management
    - Context-aware responses
    - Memory persistence
    - Context cleanup and optimization
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        self.context_history: List[ContextEntry] = []
        self.current_session_id = self._generate_session_id()
        self.session_start_time = time.time()
        
        # Configuration
        self.max_history_size = self.config.get("ai.context.max_history", 100)
        self.session_timeout = self.config.get("ai.context.session_timeout", 3600)
        self.enable_memory = self.config.get("ai.context.enable_memory", True)
        
        # Load persistent context if enabled
        if self.enable_memory:
            self._load_persistent_context()
        
        logger.info(f"Context Manager initialized (session: {self.current_session_id})")
    
    def _generate_session_id(self) -> str:
        """Generate unique session identifier."""
        return f"session_{int(time.time())}_{hash(str(time.time())) % 10000}"
    
    def add_user_input(self, text: str, metadata: Optional[Dict] = None):
        """
        Add user input to context history.
        
        Args:
            text: User's voice input text
            metadata: Additional metadata about the input
        """
        entry = ContextEntry(
            timestamp=time.time(),
            type="user_input",
            content=text,
            session_id=self.current_session_id,
            metadata=metadata or {}
        )
        
        self._add_context_entry(entry)
        logger.debug(f"Added user input to context: {text[:50]}...")
    
    def add_ai_response(self, response: Any, metadata: Optional[Dict] = None):
        """
        Add AI response to context history.
        
        Args:
            response: AI response (command, text, etc.)
            metadata: Additional metadata about the response
        """
        entry = ContextEntry(
            timestamp=time.time(),
            type="ai_response",
            content=response,
            session_id=self.current_session_id,
            metadata=metadata or {}
        )
        
        self._add_context_entry(entry)
        logger.debug("Added AI response to context")
    
    def add_system_event(self, event: str, data: Any = None, metadata: Optional[Dict] = None):
        """
        Add system event to context history.
        
        Args:
            event: Event type/name
            data: Event data
            metadata: Additional metadata
        """
        entry = ContextEntry(
            timestamp=time.time(),
            type="system_event",
            content={"event": event, "data": data},
            session_id=self.current_session_id,
            metadata=metadata or {}
        )
        
        self._add_context_entry(entry)
        logger.debug(f"Added system event to context: {event}")
    
    def _add_context_entry(self, entry: ContextEntry):
        """Add entry to context history with size management."""
        self.context_history.append(entry)
        
        # Manage history size
        if len(self.context_history) > self.max_history_size:
            # Remove oldest entries, but keep some from current session
            current_session_entries = [
                e for e in self.context_history 
                if e.session_id == self.current_session_id
            ]
            
            # Keep recent entries and current session
            keep_count = max(self.max_history_size // 2, len(current_session_entries))
            self.context_history = self.context_history[-keep_count:]
    
    def get_recent_context(self, count: int = 10) -> List[ContextEntry]:
        """
        Get recent context entries.
        
        Args:
            count: Number of recent entries to return
            
        Returns:
            List of recent context entries
        """
        return self.context_history[-count:] if self.context_history else []
    
    def get_session_context(self, session_id: Optional[str] = None) -> List[ContextEntry]:
        """
        Get context entries for specific session.
        
        Args:
            session_id: Session ID (defaults to current session)
            
        Returns:
            List of context entries for the session
        """
        target_session = session_id or self.current_session_id
        return [e for e in self.context_history if e.session_id == target_session]
    
    def get_recent_commands(self, count: int = 5) -> List[str]:
        """
        Get recent user commands.
        
        Args:
            count: Number of recent commands to return
            
        Returns:
            List of recent command texts
        """
        user_inputs = [
            e.content for e in self.context_history 
            if e.type == "user_input"
        ]
        return user_inputs[-count:] if user_inputs else []
    
    def get_conversation_summary(self, session_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get summary of conversation for specified session.
        
        Args:
            session_id: Session ID (defaults to current session)
            
        Returns:
            Dictionary containing conversation summary
        """
        session_entries = self.get_session_context(session_id)
        
        if not session_entries:
            return {"session_id": session_id, "entries": 0, "summary": "No conversation data"}
        
        user_inputs = [e for e in session_entries if e.type == "user_input"]
        ai_responses = [e for e in session_entries if e.type == "ai_response"]
        system_events = [e for e in session_entries if e.type == "system_event"]
        
        start_time = min(e.timestamp for e in session_entries)
        end_time = max(e.timestamp for e in session_entries)
        duration = end_time - start_time
        
        return {
            "session_id": session_id or self.current_session_id,
            "start_time": datetime.fromtimestamp(start_time).isoformat(),
            "duration_seconds": duration,
            "total_entries": len(session_entries),
            "user_inputs": len(user_inputs),
            "ai_responses": len(ai_responses),
            "system_events": len(system_events),
            "recent_commands": [e.content for e in user_inputs[-5:]]
        }
    
    def start_new_session(self) -> str:
        """
        Start a new conversation session.
        
        Returns:
            New session ID
        """
        # Save current session if memory is enabled
        if self.enable_memory:
            self._save_session_context()
        
        # Start new session
        old_session = self.current_session_id
        self.current_session_id = self._generate_session_id()
        self.session_start_time = time.time()
        
        self.add_system_event("session_started", {"previous_session": old_session})
        
        logger.info(f"New session started: {self.current_session_id}")
        return self.current_session_id
    
    def is_session_expired(self) -> bool:
        """
        Check if current session has expired.
        
        Returns:
            True if session is expired, False otherwise
        """
        if not self.session_timeout:
            return False
        
        session_age = time.time() - self.session_start_time
        return session_age > self.session_timeout
    
    def cleanup_old_context(self, max_age_hours: int = 24):
        """
        Remove old context entries.
        
        Args:
            max_age_hours: Maximum age of entries to keep (in hours)
        """
        cutoff_time = time.time() - (max_age_hours * 3600)
        
        old_count = len(self.context_history)
        self.context_history = [
            e for e in self.context_history 
            if e.timestamp > cutoff_time
        ]
        
        removed_count = old_count - len(self.context_history)
        if removed_count > 0:
            logger.info(f"Cleaned up {removed_count} old context entries")
    
    def get_context_for_ai(self, include_system_events: bool = False) -> str:
        """
        Get formatted context string for AI processing.
        
        Args:
            include_system_events: Whether to include system events
            
        Returns:
            Formatted context string
        """
        recent_entries = self.get_recent_context(10)
        
        context_lines = []
        for entry in recent_entries:
            if entry.type == "user_input":
                context_lines.append(f"User: {entry.content}")
            elif entry.type == "ai_response" and isinstance(entry.content, dict):
                if "action" in entry.content:
                    context_lines.append(f"AI executed: {entry.content.get('action', 'unknown')}")
            elif entry.type == "system_event" and include_system_events:
                event_data = entry.content
                if isinstance(event_data, dict):
                    context_lines.append(f"System: {event_data.get('event', 'unknown event')}")
        
        return "\n".join(context_lines[-5:])  # Last 5 interactions
    
    def _save_session_context(self):
        """Save current session context to persistent storage."""
        if not self.enable_memory:
            return
        
        try:
            context_dir = Path("data/context")
            context_dir.mkdir(parents=True, exist_ok=True)
            
            session_file = context_dir / f"{self.current_session_id}.json"
            session_data = {
                "session_id": self.current_session_id,
                "start_time": self.session_start_time,
                "entries": [asdict(e) for e in self.get_session_context()]
            }
            
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2)
            
            logger.debug(f"Session context saved: {session_file}")
            
        except Exception as e:
            logger.error(f"Failed to save session context: {e}")
    
    def _load_persistent_context(self):
        """Load persistent context from storage."""
        try:
            context_dir = Path("data/context")
            if not context_dir.exists():
                return
            
            # Load recent session files
            session_files = sorted(context_dir.glob("*.json"), key=lambda x: x.stat().st_mtime)
            
            # Load last few sessions
            for session_file in session_files[-3:]:  # Last 3 sessions
                try:
                    with open(session_file, 'r', encoding='utf-8') as f:
                        session_data = json.load(f)
                    
                    for entry_data in session_data.get("entries", []):
                        entry = ContextEntry(**entry_data)
                        self.context_history.append(entry)
                        
                except Exception as e:
                    logger.warning(f"Failed to load session file {session_file}: {e}")
            
            logger.info(f"Loaded {len(self.context_history)} context entries from persistent storage")
            
        except Exception as e:
            logger.error(f"Failed to load persistent context: {e}")
    
    def export_context(self, file_path: str, session_id: Optional[str] = None):
        """
        Export context to file.
        
        Args:
            file_path: Path to export file
            session_id: Session ID to export (defaults to current session)
        """
        try:
            entries = self.get_session_context(session_id)
            export_data = {
                "session_id": session_id or self.current_session_id,
                "export_time": datetime.now().isoformat(),
                "entries": [asdict(e) for e in entries]
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2)
            
            logger.info(f"Context exported to {file_path}")
            
        except Exception as e:
            logger.error(f"Failed to export context: {e}")
    
    def clear_context(self, session_id: Optional[str] = None):
        """
        Clear context for specified session.
        
        Args:
            session_id: Session ID to clear (defaults to current session)
        """
        if session_id:
            self.context_history = [
                e for e in self.context_history 
                if e.session_id != session_id
            ]
        else:
            self.context_history.clear()
            
        logger.info(f"Context cleared for session: {session_id or 'all'}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get context manager statistics.
        
        Returns:
            Dictionary containing statistics
        """
        total_entries = len(self.context_history)
        session_entries = len(self.get_session_context())
        
        user_inputs = len([e for e in self.context_history if e.type == "user_input"])
        ai_responses = len([e for e in self.context_history if e.type == "ai_response"])
        system_events = len([e for e in self.context_history if e.type == "system_event"])
        
        return {
            "total_entries": total_entries,
            "current_session_entries": session_entries,
            "user_inputs": user_inputs,
            "ai_responses": ai_responses,
            "system_events": system_events,
            "current_session_id": self.current_session_id,
            "session_age_seconds": time.time() - self.session_start_time,
            "memory_enabled": self.enable_memory
        }
