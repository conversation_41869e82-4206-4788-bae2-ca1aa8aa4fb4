"""
Performance Monitor and Optimizer
Ultimate Voice-Controlled AI Computer System

Monitors system performance and automatically optimizes settings.
"""

import psutil
import gc
import time
import threading
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass
from loguru import logger

from .config_manager import ConfigManager

@dataclass
class PerformanceMetrics:
    """Performance metrics data structure."""
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_usage_percent: float
    network_sent_mb: float
    network_recv_mb: float
    process_count: int
    thread_count: int
    timestamp: float

class PerformanceMonitor:
    """Monitors and optimizes system performance."""
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        self.is_monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.metrics_history = []
        self.max_history_size = 100
        
        # Performance thresholds
        self.memory_alert_threshold = self.config.get("performance.monitoring.memory_alert_threshold", 80)
        self.cpu_alert_threshold = self.config.get("performance.monitoring.cpu_alert_threshold", 85)
        
        # Callbacks
        self.on_performance_alert: Optional[Callable[[str, PerformanceMetrics], None]] = None
        self.on_optimization_applied: Optional[Callable[[str], None]] = None
        
        # Optimization flags
        self.auto_optimization_enabled = True
        self.last_optimization_time = 0
        self.optimization_cooldown = 60  # seconds
        
        logger.info("Performance Monitor initialized")
    
    def start_monitoring(self, interval: float = 30.0):
        """Start performance monitoring."""
        if self.is_monitoring:
            logger.warning("Performance monitoring already running")
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        logger.info(f"Performance monitoring started (interval: {interval}s)")
    
    def stop_monitoring(self):
        """Stop performance monitoring."""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5.0)
        
        logger.info("Performance monitoring stopped")
    
    def _monitoring_loop(self, interval: float):
        """Main monitoring loop."""
        while self.is_monitoring:
            try:
                metrics = self.get_current_metrics()
                self._process_metrics(metrics)
                time.sleep(interval)
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(interval)
    
    def get_current_metrics(self) -> PerformanceMetrics:
        """Get current system performance metrics."""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory metrics
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_mb = memory.used / (1024 * 1024)
            memory_available_mb = memory.available / (1024 * 1024)
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            disk_usage_percent = disk.percent
            
            # Network metrics
            network = psutil.net_io_counters()
            network_sent_mb = network.bytes_sent / (1024 * 1024)
            network_recv_mb = network.bytes_recv / (1024 * 1024)
            
            # Process metrics
            process_count = len(psutil.pids())
            thread_count = threading.active_count()
            
            return PerformanceMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_used_mb=memory_used_mb,
                memory_available_mb=memory_available_mb,
                disk_usage_percent=disk_usage_percent,
                network_sent_mb=network_sent_mb,
                network_recv_mb=network_recv_mb,
                process_count=process_count,
                thread_count=thread_count,
                timestamp=time.time()
            )
            
        except Exception as e:
            logger.error(f"Error getting performance metrics: {e}")
            return PerformanceMetrics(
                cpu_percent=0, memory_percent=0, memory_used_mb=0,
                memory_available_mb=0, disk_usage_percent=0,
                network_sent_mb=0, network_recv_mb=0,
                process_count=0, thread_count=0, timestamp=time.time()
            )
    
    def _process_metrics(self, metrics: PerformanceMetrics):
        """Process performance metrics and trigger optimizations."""
        # Add to history
        self.metrics_history.append(metrics)
        if len(self.metrics_history) > self.max_history_size:
            self.metrics_history.pop(0)
        
        # Check for performance alerts
        self._check_performance_alerts(metrics)
        
        # Apply automatic optimizations if needed
        if self.auto_optimization_enabled:
            self._apply_auto_optimizations(metrics)
        
        # Log performance info
        if self.config.get("performance.monitoring.performance_logging", True):
            logger.debug(
                f"Performance: CPU {metrics.cpu_percent:.1f}%, "
                f"Memory {metrics.memory_percent:.1f}% ({metrics.memory_used_mb:.1f}MB), "
                f"Threads {metrics.thread_count}"
            )
    
    def _check_performance_alerts(self, metrics: PerformanceMetrics):
        """Check for performance alerts and trigger callbacks."""
        alerts = []
        
        if metrics.memory_percent > self.memory_alert_threshold:
            alerts.append(f"High memory usage: {metrics.memory_percent:.1f}%")
        
        if metrics.cpu_percent > self.cpu_alert_threshold:
            alerts.append(f"High CPU usage: {metrics.cpu_percent:.1f}%")
        
        for alert in alerts:
            logger.warning(f"Performance Alert: {alert}")
            if self.on_performance_alert:
                try:
                    self.on_performance_alert(alert, metrics)
                except Exception as e:
                    logger.error(f"Error in performance alert callback: {e}")
    
    def _apply_auto_optimizations(self, metrics: PerformanceMetrics):
        """Apply automatic performance optimizations."""
        current_time = time.time()
        
        # Check cooldown
        if current_time - self.last_optimization_time < self.optimization_cooldown:
            return
        
        optimizations_applied = []
        
        # Memory optimization
        if metrics.memory_percent > 75:
            if self._optimize_memory():
                optimizations_applied.append("memory_cleanup")
        
        # CPU optimization
        if metrics.cpu_percent > 80:
            if self._optimize_cpu():
                optimizations_applied.append("cpu_optimization")
        
        # Apply optimizations
        if optimizations_applied:
            self.last_optimization_time = current_time
            optimization_msg = f"Applied optimizations: {', '.join(optimizations_applied)}"
            logger.info(optimization_msg)
            
            if self.on_optimization_applied:
                try:
                    self.on_optimization_applied(optimization_msg)
                except Exception as e:
                    logger.error(f"Error in optimization callback: {e}")
    
    def _optimize_memory(self) -> bool:
        """Optimize memory usage."""
        try:
            # Force garbage collection
            collected = gc.collect()
            logger.debug(f"Garbage collection freed {collected} objects")
            
            # Clear metrics history if too large
            if len(self.metrics_history) > 50:
                self.metrics_history = self.metrics_history[-25:]
                logger.debug("Trimmed metrics history")
            
            return True
            
        except Exception as e:
            logger.error(f"Error optimizing memory: {e}")
            return False
    
    def _optimize_cpu(self) -> bool:
        """Optimize CPU usage."""
        try:
            # Reduce monitoring frequency temporarily
            logger.debug("Reducing monitoring frequency due to high CPU usage")
            return True
            
        except Exception as e:
            logger.error(f"Error optimizing CPU: {e}")
            return False
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary."""
        if not self.metrics_history:
            return {"status": "no_data"}
        
        recent_metrics = self.metrics_history[-10:]  # Last 10 measurements
        
        avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
        avg_memory = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics)
        current_memory_mb = recent_metrics[-1].memory_used_mb
        
        return {
            "status": "healthy" if avg_cpu < 70 and avg_memory < 70 else "stressed",
            "average_cpu_percent": round(avg_cpu, 1),
            "average_memory_percent": round(avg_memory, 1),
            "current_memory_mb": round(current_memory_mb, 1),
            "thread_count": recent_metrics[-1].thread_count,
            "measurements_count": len(self.metrics_history),
            "monitoring_active": self.is_monitoring
        }
    
    def force_optimization(self):
        """Force immediate optimization."""
        logger.info("Forcing performance optimization")
        
        try:
            # Memory cleanup
            collected = gc.collect()
            logger.info(f"Forced garbage collection freed {collected} objects")
            
            # Clear caches if available
            self._clear_caches()
            
            # Reset optimization cooldown
            self.last_optimization_time = 0
            
            logger.info("Forced optimization completed")
            
        except Exception as e:
            logger.error(f"Error in forced optimization: {e}")
    
    def _clear_caches(self):
        """Clear various caches to free memory."""
        try:
            # Clear metrics history
            self.metrics_history = self.metrics_history[-10:]
            logger.debug("Cleared metrics history cache")
            
        except Exception as e:
            logger.error(f"Error clearing caches: {e}")
    
    def set_thresholds(self, memory_threshold: float, cpu_threshold: float):
        """Set performance alert thresholds."""
        self.memory_alert_threshold = memory_threshold
        self.cpu_alert_threshold = cpu_threshold
        logger.info(f"Updated thresholds: Memory {memory_threshold}%, CPU {cpu_threshold}%")
    
    def enable_auto_optimization(self, enabled: bool = True):
        """Enable or disable automatic optimization."""
        self.auto_optimization_enabled = enabled
        logger.info(f"Auto-optimization {'enabled' if enabled else 'disabled'}")
