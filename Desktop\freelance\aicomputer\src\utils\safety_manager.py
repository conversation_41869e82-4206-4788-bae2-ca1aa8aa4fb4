"""
Safety Manager for Voice AI System

Provides comprehensive safety validation and protection:
- Command safety validation
- Dangerous operation detection
- User confirmation requirements
- System protection mechanisms
"""

import re
import os
from typing import List, Dict, Set, Optional
from pathlib import Path
from loguru import logger

from .config_manager import ConfigManager
from ..core.ai_processor import ProcessedCommand, CommandIntent


class SafetyManager:
    """
    Comprehensive safety management system for voice commands.
    
    Features:
    - Dangerous command detection
    - Path validation and restriction
    - Application whitelist enforcement
    - User confirmation requirements
    - System protection mechanisms
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        
        # Load safety configuration
        self.safe_mode = self.config.get("app.safe_mode", True)
        self.require_confirmation = self.config.get("ai.safety.require_confirmation", True)
        self.dangerous_commands_disabled = self.config.get("ai.safety.dangerous_commands", False)
        
        # Dangerous patterns
        self.dangerous_patterns = {
            "destructive": [
                r"\b(delete|remove|rm|del)\b.*\b(all|everything|\*)\b",
                r"\b(format|wipe|erase)\b.*\b(drive|disk|partition)\b",
                r"\b(shutdown|restart|reboot)\b.*\b(force|immediate)\b",
                r"\b(kill|terminate)\b.*\b(all|everything)\b"
            ],
            "system_critical": [
                r"\b(delete|remove|modify)\b.*\b(system32|windows|program files)\b",
                r"\b(edit|modify|change)\b.*\b(registry|boot|mbr)\b",
                r"\b(disable|stop)\b.*\b(antivirus|firewall|security)\b"
            ],
            "network_security": [
                r"\b(download|install)\b.*\b(from|url|http|ftp)\b",
                r"\b(execute|run)\b.*\b(script|exe|bat|cmd)\b.*\b(download|temp)\b",
                r"\b(connect|access)\b.*\b(remote|external|unknown)\b"
            ]
        }
        
        # Confirmation required patterns
        self.confirmation_patterns = [
            r"\b(delete|remove|uninstall)\b",
            r"\b(shutdown|restart|reboot)\b",
            r"\b(format|wipe)\b",
            r"\b(modify|change|edit)\b.*\b(system|registry|config)\b"
        ]
        
        # Protected paths
        self.protected_paths = self._get_protected_paths()
        
        # Allowed applications
        self.allowed_applications = set(self.config.get("applications.allowed_apps", []))
        
        logger.info("Safety Manager initialized")
    
    def validate_command(self, command: ProcessedCommand) -> bool:
        """
        Validate if a command is safe to execute.
        
        Args:
            command: Processed command to validate
            
        Returns:
            True if command is safe, False otherwise
        """
        if not self.safe_mode:
            logger.warning("Safe mode disabled - allowing all commands")
            return True
        
        try:
            # Check for dangerous patterns
            if self._contains_dangerous_patterns(command):
                logger.warning(f"Dangerous pattern detected in command: {command.raw_text}")
                return False
            
            # Validate based on command intent
            if command.intent == CommandIntent.FILE_OPERATION:
                return self._validate_file_operation(command)
            elif command.intent == CommandIntent.APP_CONTROL:
                return self._validate_app_control(command)
            elif command.intent == CommandIntent.SYSTEM_CONTROL:
                return self._validate_system_control(command)
            elif command.intent == CommandIntent.SYSTEM_INFO:
                return True  # System info queries are generally safe
            else:
                return True  # Unknown intents are allowed by default
                
        except Exception as e:
            logger.error(f"Error in safety validation: {e}")
            return False  # Fail safe
    
    def _contains_dangerous_patterns(self, command: ProcessedCommand) -> bool:
        """Check if command contains dangerous patterns."""
        
        text = command.raw_text.lower()
        action = command.action.lower()
        
        # Check all dangerous pattern categories
        for category, patterns in self.dangerous_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text, re.IGNORECASE) or re.search(pattern, action, re.IGNORECASE):
                    logger.warning(f"Dangerous pattern detected ({category}): {pattern}")
                    return True
        
        return False
    
    def _validate_file_operation(self, command: ProcessedCommand) -> bool:
        """Validate file operation safety."""
        
        entities = command.entities
        action = command.action.lower()
        
        # Get target path
        target = entities.get("target", "")
        location = entities.get("location", "")
        
        # Construct full path
        if location and target:
            if location == "current":
                full_path = Path.cwd() / target
            else:
                full_path = Path(location) / target
        elif target:
            full_path = Path(target)
        elif location:
            full_path = Path(location)
        else:
            full_path = None
        
        # Validate path safety
        if full_path and not self._is_path_safe(full_path):
            logger.warning(f"Unsafe path detected: {full_path}")
            return False
        
        # Check destructive operations
        if action in ["delete", "remove", "format", "wipe"]:
            if self.dangerous_commands_disabled:
                logger.warning(f"Destructive operation disabled: {action}")
                return False
            
            # Additional checks for destructive operations
            if full_path:
                if self._is_critical_path(full_path):
                    logger.warning(f"Attempt to perform destructive operation on critical path: {full_path}")
                    return False
        
        return True
    
    def _validate_app_control(self, command: ProcessedCommand) -> bool:
        """Validate application control safety."""
        
        entities = command.entities
        target = entities.get("target", "").lower()
        
        # Check if application is in allowed list
        if self.allowed_applications and target not in self.allowed_applications:
            logger.warning(f"Application not in allowed list: {target}")
            return False
        
        # Check for dangerous applications
        dangerous_apps = [
            "cmd.exe", "powershell.exe", "regedit.exe", "msconfig.exe",
            "services.msc", "gpedit.msc", "secpol.msc"
        ]
        
        if target in dangerous_apps:
            logger.warning(f"Dangerous application blocked: {target}")
            return False
        
        return True
    
    def _validate_system_control(self, command: ProcessedCommand) -> bool:
        """Validate system control safety."""
        
        if self.dangerous_commands_disabled:
            logger.warning("System control commands disabled in safe mode")
            return False
        
        action = command.action.lower()
        
        # Allow read-only system operations
        safe_actions = ["get", "show", "list", "display", "check", "status"]
        if any(safe_action in action for safe_action in safe_actions):
            return True
        
        # Block dangerous system operations
        dangerous_actions = ["shutdown", "restart", "format", "delete", "modify"]
        if any(dangerous_action in action for dangerous_action in dangerous_actions):
            logger.warning(f"Dangerous system operation blocked: {action}")
            return False
        
        return True
    
    def _is_path_safe(self, path: Path) -> bool:
        """Check if a path is safe to access."""
        
        try:
            # Resolve path to handle relative paths and symlinks
            resolved_path = path.resolve()
            
            # Check against protected paths
            for protected in self.protected_paths:
                try:
                    protected_resolved = Path(protected).resolve()
                    if resolved_path == protected_resolved or protected_resolved in resolved_path.parents:
                        return False
                except Exception:
                    continue
            
            # Check allowed paths
            allowed_paths = self.config.get("file_system.allowed_paths", [])
            if allowed_paths:
                path_allowed = False
                for allowed in allowed_paths:
                    try:
                        allowed_resolved = Path(allowed).resolve()
                        if resolved_path == allowed_resolved or allowed_resolved in resolved_path.parents:
                            path_allowed = True
                            break
                    except Exception:
                        continue
                
                if not path_allowed:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating path safety: {e}")
            return False
    
    def _is_critical_path(self, path: Path) -> bool:
        """Check if path is critical to system operation."""
        
        critical_paths = [
            "C:\\Windows\\System32",
            "C:\\Windows\\SysWOW64",
            "C:\\Program Files",
            "C:\\Program Files (x86)",
            "C:\\Windows\\Boot",
            "C:\\$Recycle.Bin"
        ]
        
        try:
            resolved_path = path.resolve()
            for critical in critical_paths:
                critical_resolved = Path(critical).resolve()
                if resolved_path == critical_resolved or critical_resolved in resolved_path.parents:
                    return True
        except Exception:
            pass
        
        return False
    
    def _get_protected_paths(self) -> List[str]:
        """Get list of protected system paths."""
        
        protected = [
            "C:\\Windows\\System32",
            "C:\\Windows\\SysWOW64",
            "C:\\Program Files",
            "C:\\Program Files (x86)",
            "C:\\Windows\\Boot",
            "C:\\Windows\\Fonts",
            "C:\\Windows\\inf",
            "C:\\Windows\\WinSxS"
        ]
        
        # Add configured restricted paths
        restricted_paths = self.config.get("file_system.restricted_paths", [])
        protected.extend(restricted_paths)
        
        return protected
    
    def requires_confirmation(self, command: ProcessedCommand) -> bool:
        """Check if command requires user confirmation."""
        
        if not self.require_confirmation:
            return False
        
        # Check if already marked as requiring confirmation
        if command.requires_confirmation:
            return True
        
        # Check confirmation patterns
        text = command.raw_text.lower()
        action = command.action.lower()
        
        for pattern in self.confirmation_patterns:
            if re.search(pattern, text, re.IGNORECASE) or re.search(pattern, action, re.IGNORECASE):
                return True
        
        # Check configured confirmation required commands
        confirmation_commands = self.config.get("commands.confirmation_required", [])
        for cmd in confirmation_commands:
            if cmd.lower() in text or cmd.lower() in action:
                return True
        
        return False
    
    def get_safety_warning(self, command: ProcessedCommand) -> Optional[str]:
        """Get safety warning message for command."""
        
        if not command.is_safe:
            return "This command has been blocked for safety reasons."
        
        if self.requires_confirmation(command):
            action = command.action
            target = command.entities.get("target", "")
            
            if target:
                return f"Are you sure you want to {action} '{target}'? This action may be irreversible."
            else:
                return f"Are you sure you want to {action}? This action may be irreversible."
        
        return None
    
    def log_safety_event(self, command: ProcessedCommand, event_type: str, details: str = ""):
        """Log safety-related events for audit purposes."""
        
        logger.warning(
            f"SAFETY EVENT: {event_type} | "
            f"Command: {command.raw_text} | "
            f"Intent: {command.intent.value} | "
            f"Action: {command.action} | "
            f"Details: {details}"
        )
    
    def get_safety_statistics(self) -> Dict[str, any]:
        """Get safety manager statistics."""
        
        return {
            "safe_mode_enabled": self.safe_mode,
            "confirmation_required": self.require_confirmation,
            "dangerous_commands_disabled": self.dangerous_commands_disabled,
            "protected_paths_count": len(self.protected_paths),
            "allowed_applications_count": len(self.allowed_applications),
            "dangerous_patterns_count": sum(len(patterns) for patterns in self.dangerous_patterns.values())
        }
