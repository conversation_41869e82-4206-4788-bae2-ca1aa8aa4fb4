"""
Test suite for Voice Engine component
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, patch, MagicMock
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.core.voice_engine import VoiceEngine
from src.utils.config_manager import ConfigManager


class TestVoiceEngine:
    """Test cases for VoiceEngine class."""
    
    @pytest.fixture
    def config_manager(self):
        """Create a test configuration manager."""
        config = ConfigManager()
        config.config_data = {
            "voice": {
                "recognition": {
                    "engine": "whisper",
                    "language": "en-US",
                    "timeout": 5,
                    "phrase_timeout": 1
                },
                "wake_word": {
                    "enabled": True,
                    "word": "computer",
                    "sensitivity": 0.7
                },
                "audio": {
                    "sample_rate": 16000,
                    "chunk_size": 1024,
                    "channels": 1
                }
            }
        }
        return config
    
    @pytest.fixture
    def voice_engine(self, config_manager):
        """Create a test voice engine instance."""
        with patch('src.core.voice_engine.sr.Microphone'), \
             patch('src.core.voice_engine.whisper.load_model'):
            engine = VoiceEngine(config_manager)
            return engine
    
    def test_voice_engine_initialization(self, voice_engine):
        """Test voice engine initialization."""
        assert voice_engine is not None
        assert not voice_engine.is_listening
        assert not voice_engine.is_wake_word_active
        assert voice_engine.on_wake_word_detected is None
        assert voice_engine.on_speech_recognized is None
        assert voice_engine.on_error is None
    
    def test_start_stop_listening(self, voice_engine):
        """Test starting and stopping voice recognition."""
        # Test start listening
        with patch.object(voice_engine, '_listen_loop'):
            voice_engine.start_listening()
            assert voice_engine.is_listening
            
            # Test stop listening
            voice_engine.stop_listening()
            assert not voice_engine.is_listening
    
    def test_wake_word_detection(self, voice_engine):
        """Test wake word detection logic."""
        # Test positive wake word detection
        assert voice_engine._is_wake_word("computer start notepad")
        assert voice_engine._is_wake_word("hey computer")
        assert voice_engine._is_wake_word("Computer, what time is it?")
        
        # Test negative wake word detection
        assert not voice_engine._is_wake_word("start notepad")
        assert not voice_engine._is_wake_word("hello there")
        assert not voice_engine._is_wake_word("computing power")
    
    @patch('src.core.voice_engine.sr.Recognizer.recognize_google')
    def test_google_recognition(self, mock_recognize, voice_engine):
        """Test Google speech recognition."""
        # Mock audio data
        mock_audio = Mock()
        mock_recognize.return_value = "test command"
        
        result = voice_engine._recognize_with_google(mock_audio)
        assert result == "test command"
        mock_recognize.assert_called_once()
    
    def test_microphone_management(self, voice_engine):
        """Test microphone device management."""
        with patch('src.core.voice_engine.sr.Microphone') as mock_mic:
            # Test setting microphone index
            voice_engine.set_microphone_index(1)
            mock_mic.assert_called_with(device_index=1)
    
    @patch('src.core.voice_engine.sr.Microphone.list_microphone_names')
    def test_get_available_microphones(self, mock_list_mics, voice_engine):
        """Test getting available microphones."""
        mock_list_mics.return_value = ["Microphone 1", "Microphone 2", "Microphone 3"]
        
        mics = voice_engine.get_available_microphones()
        assert len(mics) == 3
        assert mics[0] == "Microphone 1"
        assert mics[1] == "Microphone 2"
        assert mics[2] == "Microphone 3"
    
    def test_callback_assignment(self, voice_engine):
        """Test callback function assignment."""
        wake_word_callback = Mock()
        speech_callback = Mock()
        error_callback = Mock()
        
        voice_engine.on_wake_word_detected = wake_word_callback
        voice_engine.on_speech_recognized = speech_callback
        voice_engine.on_error = error_callback
        
        assert voice_engine.on_wake_word_detected == wake_word_callback
        assert voice_engine.on_speech_recognized == speech_callback
        assert voice_engine.on_error == error_callback
    
    @patch('src.core.voice_engine.whisper.load_model')
    def test_whisper_model_loading(self, mock_load_model, config_manager):
        """Test Whisper model loading."""
        mock_model = Mock()
        mock_load_model.return_value = mock_model
        
        with patch('src.core.voice_engine.sr.Microphone'):
            engine = VoiceEngine(config_manager)
            mock_load_model.assert_called_once()
            assert engine.whisper_model == mock_model
    
    def test_recognition_engine_fallback(self, voice_engine):
        """Test fallback between recognition engines."""
        mock_audio = Mock()
        
        # Test fallback to Google when Whisper fails
        with patch.object(voice_engine, '_recognize_with_whisper', side_effect=Exception("Whisper failed")), \
             patch.object(voice_engine, '_recognize_with_google', return_value="fallback result"):
            
            result = voice_engine._recognize_speech(mock_audio)
            assert result == "fallback result"
    
    @pytest.mark.asyncio
    async def test_speech_recognition_callback(self, voice_engine):
        """Test speech recognition callback execution."""
        callback_called = False
        recognized_text = None
        
        def speech_callback(text):
            nonlocal callback_called, recognized_text
            callback_called = True
            recognized_text = text
        
        voice_engine.on_speech_recognized = speech_callback
        
        # Simulate speech recognition
        await voice_engine._on_speech_recognized("test command")
        
        assert callback_called
        assert recognized_text == "test command"
    
    def test_configuration_integration(self, voice_engine):
        """Test integration with configuration manager."""
        # Test that configuration values are properly used
        assert voice_engine.config.get("voice.wake_word.word") == "computer"
        assert voice_engine.config.get("voice.recognition.language") == "en-US"
        assert voice_engine.config.get("voice.audio.sample_rate") == 16000


if __name__ == "__main__":
    pytest.main([__file__])
