<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌟 Universal AI Computer System - Analytics</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar-ai {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .analytics-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-ai">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-brain"></i> Universal AI Computer System
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a class="nav-link" href="/docs"><i class="fas fa-book"></i> Documentation</a>
                <a class="nav-link" href="/components"><i class="fas fa-microchip"></i> Components</a>
                <a class="nav-link" href="/operations"><i class="fas fa-cogs"></i> Operations</a>
                <a class="nav-link active" href="/analytics"><i class="fas fa-chart-line"></i> Analytics</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid p-4">
        <h1><i class="fas fa-chart-line"></i> System Analytics</h1>
        <p class="text-white-50">Advanced analytics and performance metrics</p>
        
        <div class="analytics-card">
            <h3>Performance Analytics</h3>
            <canvas id="analytics-chart" width="400" height="200"></canvas>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Initialize analytics chart
        document.addEventListener('DOMContentLoaded', function() {
            const ctx = document.getElementById('analytics-chart').getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Consciousness', 'Quantum', 'Temporal', 'Reality', 'Hyperdimensional', 'Molecular', 'Predictive', 'Universal'],
                    datasets: [{
                        data: [12, 15, 8, 10, 6, 9, 11, 7],
                        backgroundColor: [
                            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                            '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
