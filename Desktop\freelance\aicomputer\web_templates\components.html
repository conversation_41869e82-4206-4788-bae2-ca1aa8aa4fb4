<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌟 Universal AI Computer System - Components</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar-ai {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .component-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .component-card:hover {
            transform: translateY(-5px);
        }
        
        .status-online { color: #28a745; }
        .status-offline { color: #dc3545; }
        .status-initializing { color: #ffc107; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-ai">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-brain"></i> Universal AI Computer System
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a class="nav-link" href="/docs"><i class="fas fa-book"></i> Documentation</a>
                <a class="nav-link active" href="/components"><i class="fas fa-microchip"></i> Components</a>
                <a class="nav-link" href="/operations"><i class="fas fa-cogs"></i> Operations</a>
                <a class="nav-link" href="/analytics"><i class="fas fa-chart-line"></i> Analytics</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid p-4">
        <h1><i class="fas fa-microchip"></i> System Components</h1>
        <p class="text-white-50">Detailed monitoring of all 9 Universal AI Computer System components</p>
        
        <div class="row" id="components-grid">
            <!-- Components will be loaded here -->
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Load components on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadComponents();
            setInterval(loadComponents, 5000); // Refresh every 5 seconds
        });
        
        function loadComponents() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    displayComponents(data.components || {});
                })
                .catch(error => {
                    console.error('Error loading components:', error);
                });
        }
        
        function displayComponents(components) {
            const grid = document.getElementById('components-grid');
            grid.innerHTML = '';
            
            const componentDetails = {
                '🧠 AI Consciousness': {
                    description: 'Self-aware artificial consciousness with emotional processing and memory formation',
                    features: ['Emotional Processing', 'Memory Formation', 'Self-Awareness', 'Decision Making']
                },
                '⚛️ Quantum AI Processor': {
                    description: 'Quantum computing processor with entanglement and quantum algorithm execution',
                    features: ['Quantum Algorithms', 'Entanglement', 'Quantum Advantage', 'State Measurement']
                },
                '⏰ Temporal Processor': {
                    description: 'Time manipulation and timeline management system',
                    features: ['Timeline Creation', 'Temporal Events', 'Causality Preservation', 'Time Dilation']
                },
                '🌍 Reality Synthesizer': {
                    description: 'Multi-reality creation and manipulation system',
                    features: ['Reality Objects', 'Multi-Reality Support', 'Quantum Enhancement', 'Reality Synthesis']
                },
                '🌌 Hyperdimensional Processor': {
                    description: 'Higher-dimensional computing and vector processing',
                    features: ['Multi-Dimensional Vectors', 'Hyperdimensional Math', 'Cross-Dimensional Analysis', 'High-D Projections']
                },
                '🧬 Molecular Computer': {
                    description: 'DNA-based storage and molecular computation system',
                    features: ['DNA Storage', 'Molecular Algorithms', 'Genetic Computing', 'Biochemical Processing']
                },
                '🔮 Advanced Predictor': {
                    description: 'Quantum-enhanced predictive intelligence and forecasting',
                    features: ['Time Series Prediction', 'Pattern Recognition', 'Quantum Enhancement', 'Risk Assessment']
                },
                '🌌 Universal Interface': {
                    description: 'Cross-dimensional communication and universal messaging',
                    features: ['Cross-Dimensional Messaging', 'Universal Protocols', 'Multi-Reality Communication', 'Quantum Channels']
                },
                '🔧 Configuration Manager': {
                    description: 'System configuration and orchestration management',
                    features: ['System Configuration', 'Component Orchestration', 'Settings Management', 'System Coordination']
                }
            };
            
            for (const [name, info] of Object.entries(components)) {
                const details = componentDetails[name] || { description: 'System component', features: [] };
                const statusClass = info.status === 'ONLINE' ? 'status-online' : 
                                  info.status === 'INITIALIZING' ? 'status-initializing' : 'status-offline';
                
                const card = document.createElement('div');
                card.className = 'col-md-6 col-lg-4';
                card.innerHTML = `
                    <div class="component-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0">${name}</h5>
                            <span class="${statusClass}">
                                <i class="fas fa-circle"></i> ${info.status}
                            </span>
                        </div>
                        
                        <p class="text-muted small">${details.description}</p>
                        
                        <div class="mb-3">
                            <small class="text-muted">Module: <code>${info.module || 'N/A'}</code></small>
                        </div>
                        
                        <div class="features">
                            <h6>Key Features:</h6>
                            <ul class="list-unstyled">
                                ${details.features.map(feature => `<li><i class="fas fa-check text-success"></i> ${feature}</li>`).join('')}
                            </ul>
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-sm btn-outline-primary" onclick="showComponentDetails('${name}')">
                                <i class="fas fa-info-circle"></i> Details
                            </button>
                        </div>
                    </div>
                `;
                grid.appendChild(card);
            }
        }
        
        function showComponentDetails(componentName) {
            alert(`Detailed information for ${componentName} would be displayed here.`);
        }
    </script>
</body>
</html>
