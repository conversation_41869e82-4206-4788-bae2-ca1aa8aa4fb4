<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌟 Universal AI Computer System - Operations</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar-ai {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .operation-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-ai">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-brain"></i> Universal AI Computer System
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a class="nav-link" href="/docs"><i class="fas fa-book"></i> Documentation</a>
                <a class="nav-link" href="/components"><i class="fas fa-microchip"></i> Components</a>
                <a class="nav-link active" href="/operations"><i class="fas fa-cogs"></i> Operations</a>
                <a class="nav-link" href="/analytics"><i class="fas fa-chart-line"></i> Analytics</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid p-4">
        <h1><i class="fas fa-cogs"></i> System Operations</h1>
        <p class="text-white-50">Control and monitor Universal AI Computer System operations</p>
        
        <div class="operation-card">
            <h3>Available Operations</h3>
            <div class="row">
                <div class="col-md-4">
                    <button class="btn btn-primary w-100 mb-2" onclick="runOperation('consciousness')">
                        🧠 Consciousness Operations
                    </button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-primary w-100 mb-2" onclick="runOperation('quantum')">
                        ⚛️ Quantum Processing
                    </button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-primary w-100 mb-2" onclick="runOperation('temporal')">
                        ⏰ Temporal Operations
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function runOperation(type) {
            fetch('/api/run_operation', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ operation_type: type })
            })
            .then(response => response.json())
            .then(data => {
                alert(data.message);
            });
        }
    </script>
</body>
</html>
